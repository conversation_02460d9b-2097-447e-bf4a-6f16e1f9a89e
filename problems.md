[{"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/README.md", "owner": "cSpell", "severity": 2, "message": "\"livewallpaper\": Unknown word.", "source": "cSpell", "startLineNumber": 11, "startColumn": 22, "endLineNumber": 11, "endColumn": 35, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/README.md", "owner": "cSpell", "severity": 2, "message": "\"livewallpaper\": Unknown word.", "source": "cSpell", "startLineNumber": 20, "startColumn": 27, "endLineNumber": 20, "endColumn": 40, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/README.md", "owner": "cSpell", "severity": 2, "message": "\"livewallpaper\": Unknown word.", "source": "cSpell", "startLineNumber": 27, "startColumn": 31, "endLineNumber": 27, "endColumn": 44, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/README.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 49, "startColumn": 13, "endLineNumber": 49, "endColumn": 20, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/README.md", "owner": "cSpell", "severity": 2, "message": "\"MVVM\": Unknown word.", "source": "cSpell", "startLineNumber": 50, "startColumn": 11, "endLineNumber": 50, "endColumn": 15, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Trae\": Unknown word.", "source": "cSpell", "startLineNumber": 9, "startColumn": 28, "endLineNumber": 9, "endColumn": 32, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Logcat\": Unknown word.", "source": "cSpell", "startLineNumber": 17, "startColumn": 78, "endLineNumber": 17, "endColumn": 84, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"KAPT\": Unknown word.", "source": "cSpell", "startLineNumber": 19, "startColumn": 68, "endLineNumber": 19, "endColumn": 72, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Argb\": Unknown word.", "source": "cSpell", "startLineNumber": 41, "startColumn": 109, "endLineNumber": 41, "endColumn": 113, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"OPPO\": Unknown word.", "source": "cSpell", "startLineNumber": 57, "startColumn": 100, "endLineNumber": 57, "endColumn": 104, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 71, "startColumn": 10, "endLineNumber": 71, "endColumn": 17, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 73, "startColumn": 40, "endLineNumber": 73, "endColumn": 47, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"MVVM\": Unknown word.", "source": "cSpell", "startLineNumber": 75, "startColumn": 1, "endLineNumber": 75, "endColumn": 5, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"MVVM\": Unknown word.", "source": "cSpell", "startLineNumber": 77, "startColumn": 1, "endLineNumber": 77, "endColumn": 5, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 89, "startColumn": 15, "endLineNumber": 89, "endColumn": 22, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"yourcompany\": Unknown word.", "source": "cSpell", "startLineNumber": 113, "startColumn": 39, "endLineNumber": 113, "endColumn": 50, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"dynamicwallpaper\": Unknown word.", "source": "cSpell", "startLineNumber": 113, "startColumn": 51, "endLineNumber": 113, "endColumn": 67, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Trae\": Unknown word.", "source": "cSpell", "startLineNumber": 119, "startColumn": 23, "endLineNumber": 119, "endColumn": 27, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"kapt\": Unknown word.", "source": "cSpell", "startLineNumber": 127, "startColumn": 1, "endLineNumber": 127, "endColumn": 5, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 128, "startColumn": 17, "endLineNumber": 128, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 131, "startColumn": 17, "endLineNumber": 131, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 132, "startColumn": 26, "endLineNumber": 132, "endColumn": 34, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 133, "startColumn": 17, "endLineNumber": 133, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 134, "startColumn": 22, "endLineNumber": 134, "endColumn": 30, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 135, "startColumn": 17, "endLineNumber": 135, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"viewmodel\": Unknown word.", "source": "cSpell", "startLineNumber": 135, "startColumn": 46, "endLineNumber": 135, "endColumn": 55, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 136, "startColumn": 17, "endLineNumber": 136, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"gson\": Unknown word.", "source": "cSpell", "startLineNumber": 140, "startColumn": 50, "endLineNumber": 140, "endColumn": 54, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"okhttp\": Unknown word.", "source": "cSpell", "startLineNumber": 141, "startColumn": 30, "endLineNumber": 141, "endColumn": 36, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 150, "startColumn": 17, "endLineNumber": 150, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 151, "startColumn": 17, "endLineNumber": 151, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"kapt\": Unknown word.", "source": "cSpell", "startLineNumber": 152, "startColumn": 1, "endLineNumber": 152, "endColumn": 5, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 152, "startColumn": 7, "endLineNumber": 152, "endColumn": 15, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"androidx\": Unknown word.", "source": "cSpell", "startLineNumber": 153, "startColumn": 17, "endLineNumber": 153, "endColumn": 25, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"kotlinx\": Unknown word.", "source": "cSpell", "startLineNumber": 159, "startColumn": 31, "endLineNumber": 159, "endColumn": 38, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"kotlinx\": Unknown word.", "source": "cSpell", "startLineNumber": 159, "startColumn": 39, "endLineNumber": 159, "endColumn": 46, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 246, "startColumn": 20, "endLineNumber": 246, "endColumn": 27, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Kastro\": Unknown word.", "source": "cSpell", "startLineNumber": 261, "startColumn": 12, "endLineNumber": 261, "endColumn": 18, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 319, "startColumn": 12, "endLineNumber": 319, "endColumn": 19, "extensionID": "streetsidesoftware.code-spell-checker"}, {"resource": "/Users/<USER>/Documents/GitHub/LiveWallpaper/startup.md", "owner": "cSpell", "severity": 2, "message": "\"Jetpack\": Unknown word.", "source": "cSpell", "startLineNumber": 325, "startColumn": 29, "endLineNumber": 325, "endColumn": 36, "extensionID": "streetsidesoftware.code-spell-checker"}]