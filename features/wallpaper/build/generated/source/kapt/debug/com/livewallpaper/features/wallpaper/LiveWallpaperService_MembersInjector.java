// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.features.wallpaper;

import com.livewallpaper.core.data.repository.SettingsRepository;
import com.livewallpaper.core.domain.music.MusicCardRenderer;
import com.livewallpaper.core.domain.music.MusicManager;
import com.livewallpaper.core.domain.scene.SceneInitializer;
import com.livewallpaper.core.domain.scene.SceneManager;
import com.livewallpaper.core.domain.scene.SceneRenderer;
import com.livewallpaper.core.domain.time.WallpaperTimeManager;
import com.livewallpaper.core.domain.weather.WeatherEffectRenderer;
import com.livewallpaper.core.domain.weather.WeatherManager;
import com.livewallpaper.core.performance.DirtyRegionManager;
import com.livewallpaper.core.performance.ObjectPoolManager;
import com.livewallpaper.core.performance.PerformanceMonitor;
import com.livewallpaper.core.performance.PerformanceOptimizer;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class LiveWallpaperService_MembersInjector implements MembersInjector<LiveWallpaperService> {
  private final Provider<WallpaperTimeManager> timeManagerProvider;

  private final Provider<SceneManager> sceneManagerProvider;

  private final Provider<SceneRenderer> sceneRendererProvider;

  private final Provider<SceneInitializer> sceneInitializerProvider;

  private final Provider<WeatherManager> weatherManagerProvider;

  private final Provider<WeatherEffectRenderer> weatherEffectRendererProvider;

  private final Provider<MusicManager> musicManagerProvider;

  private final Provider<MusicCardRenderer> musicCardRendererProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  private final Provider<PerformanceMonitor> performanceMonitorProvider;

  private final Provider<PerformanceOptimizer> performanceOptimizerProvider;

  private final Provider<DirtyRegionManager> dirtyRegionManagerProvider;

  private final Provider<ObjectPoolManager> objectPoolManagerProvider;

  public LiveWallpaperService_MembersInjector(Provider<WallpaperTimeManager> timeManagerProvider,
      Provider<SceneManager> sceneManagerProvider, Provider<SceneRenderer> sceneRendererProvider,
      Provider<SceneInitializer> sceneInitializerProvider,
      Provider<WeatherManager> weatherManagerProvider,
      Provider<WeatherEffectRenderer> weatherEffectRendererProvider,
      Provider<MusicManager> musicManagerProvider,
      Provider<MusicCardRenderer> musicCardRendererProvider,
      Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<PerformanceOptimizer> performanceOptimizerProvider,
      Provider<DirtyRegionManager> dirtyRegionManagerProvider,
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    this.timeManagerProvider = timeManagerProvider;
    this.sceneManagerProvider = sceneManagerProvider;
    this.sceneRendererProvider = sceneRendererProvider;
    this.sceneInitializerProvider = sceneInitializerProvider;
    this.weatherManagerProvider = weatherManagerProvider;
    this.weatherEffectRendererProvider = weatherEffectRendererProvider;
    this.musicManagerProvider = musicManagerProvider;
    this.musicCardRendererProvider = musicCardRendererProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
    this.performanceMonitorProvider = performanceMonitorProvider;
    this.performanceOptimizerProvider = performanceOptimizerProvider;
    this.dirtyRegionManagerProvider = dirtyRegionManagerProvider;
    this.objectPoolManagerProvider = objectPoolManagerProvider;
  }

  public static MembersInjector<LiveWallpaperService> create(
      Provider<WallpaperTimeManager> timeManagerProvider,
      Provider<SceneManager> sceneManagerProvider, Provider<SceneRenderer> sceneRendererProvider,
      Provider<SceneInitializer> sceneInitializerProvider,
      Provider<WeatherManager> weatherManagerProvider,
      Provider<WeatherEffectRenderer> weatherEffectRendererProvider,
      Provider<MusicManager> musicManagerProvider,
      Provider<MusicCardRenderer> musicCardRendererProvider,
      Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<PerformanceOptimizer> performanceOptimizerProvider,
      Provider<DirtyRegionManager> dirtyRegionManagerProvider,
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    return new LiveWallpaperService_MembersInjector(timeManagerProvider, sceneManagerProvider, sceneRendererProvider, sceneInitializerProvider, weatherManagerProvider, weatherEffectRendererProvider, musicManagerProvider, musicCardRendererProvider, settingsRepositoryProvider, performanceMonitorProvider, performanceOptimizerProvider, dirtyRegionManagerProvider, objectPoolManagerProvider);
  }

  @Override
  public void injectMembers(LiveWallpaperService instance) {
    injectTimeManager(instance, timeManagerProvider.get());
    injectSceneManager(instance, sceneManagerProvider.get());
    injectSceneRenderer(instance, sceneRendererProvider.get());
    injectSceneInitializer(instance, sceneInitializerProvider.get());
    injectWeatherManager(instance, weatherManagerProvider.get());
    injectWeatherEffectRenderer(instance, weatherEffectRendererProvider.get());
    injectMusicManager(instance, musicManagerProvider.get());
    injectMusicCardRenderer(instance, musicCardRendererProvider.get());
    injectSettingsRepository(instance, settingsRepositoryProvider.get());
    injectPerformanceMonitor(instance, performanceMonitorProvider.get());
    injectPerformanceOptimizer(instance, performanceOptimizerProvider.get());
    injectDirtyRegionManager(instance, dirtyRegionManagerProvider.get());
    injectObjectPoolManager(instance, objectPoolManagerProvider.get());
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.timeManager")
  public static void injectTimeManager(LiveWallpaperService instance,
      WallpaperTimeManager timeManager) {
    instance.timeManager = timeManager;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.sceneManager")
  public static void injectSceneManager(LiveWallpaperService instance, SceneManager sceneManager) {
    instance.sceneManager = sceneManager;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.sceneRenderer")
  public static void injectSceneRenderer(LiveWallpaperService instance,
      SceneRenderer sceneRenderer) {
    instance.sceneRenderer = sceneRenderer;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.sceneInitializer")
  public static void injectSceneInitializer(LiveWallpaperService instance,
      SceneInitializer sceneInitializer) {
    instance.sceneInitializer = sceneInitializer;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.weatherManager")
  public static void injectWeatherManager(LiveWallpaperService instance,
      WeatherManager weatherManager) {
    instance.weatherManager = weatherManager;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.weatherEffectRenderer")
  public static void injectWeatherEffectRenderer(LiveWallpaperService instance,
      WeatherEffectRenderer weatherEffectRenderer) {
    instance.weatherEffectRenderer = weatherEffectRenderer;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.musicManager")
  public static void injectMusicManager(LiveWallpaperService instance, MusicManager musicManager) {
    instance.musicManager = musicManager;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.musicCardRenderer")
  public static void injectMusicCardRenderer(LiveWallpaperService instance,
      MusicCardRenderer musicCardRenderer) {
    instance.musicCardRenderer = musicCardRenderer;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.settingsRepository")
  public static void injectSettingsRepository(LiveWallpaperService instance,
      SettingsRepository settingsRepository) {
    instance.settingsRepository = settingsRepository;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.performanceMonitor")
  public static void injectPerformanceMonitor(LiveWallpaperService instance,
      PerformanceMonitor performanceMonitor) {
    instance.performanceMonitor = performanceMonitor;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.performanceOptimizer")
  public static void injectPerformanceOptimizer(LiveWallpaperService instance,
      PerformanceOptimizer performanceOptimizer) {
    instance.performanceOptimizer = performanceOptimizer;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.dirtyRegionManager")
  public static void injectDirtyRegionManager(LiveWallpaperService instance,
      DirtyRegionManager dirtyRegionManager) {
    instance.dirtyRegionManager = dirtyRegionManager;
  }

  @InjectedFieldSignature("com.livewallpaper.features.wallpaper.LiveWallpaperService.objectPoolManager")
  public static void injectObjectPoolManager(LiveWallpaperService instance,
      ObjectPoolManager objectPoolManager) {
    instance.objectPoolManager = objectPoolManager;
  }
}
