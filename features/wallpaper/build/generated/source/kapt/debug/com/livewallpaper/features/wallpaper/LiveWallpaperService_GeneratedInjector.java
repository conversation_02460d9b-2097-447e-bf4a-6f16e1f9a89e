package com.livewallpaper.features.wallpaper;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = LiveWallpaperService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface LiveWallpaperService_GeneratedInjector {
  void injectLiveWallpaperService(LiveWallpaperService liveWallpaperService);
}
