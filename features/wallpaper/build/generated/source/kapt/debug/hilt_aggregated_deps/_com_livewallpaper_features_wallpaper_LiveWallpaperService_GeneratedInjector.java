package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ServiceComponent",
    entryPoints = "com.livewallpaper.features.wallpaper.LiveWallpaperService_GeneratedInjector"
)
public class _com_livewallpaper_features_wallpaper_LiveWallpaperService_GeneratedInjector {
}
