package com.livewallpaper.features.wallpaper;

/**
 * 动态壁纸服务
 * 这是动态壁纸的核心服务类，集成了时间和天文计算系统
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001:\u0001SB\u0005\u00a2\u0006\u0002\u0010\u0002J\f\u0010Q\u001a\u00060RR\u00020\u0001H\u0016R\u001e\u0010\u0003\u001a\u00020\u00048\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR\u001e\u0010\t\u001a\u00020\n8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR\u001e\u0010\u000f\u001a\u00020\u00108\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\u001e\u0010\u0015\u001a\u00020\u00168\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u001e\u0010\u001b\u001a\u00020\u001c8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001e\"\u0004\b\u001f\u0010 R\u001e\u0010!\u001a\u00020\"8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010$\"\u0004\b%\u0010&R\u001e\u0010\'\u001a\u00020(8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010*\"\u0004\b+\u0010,R\u001e\u0010-\u001a\u00020.8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u00100\"\u0004\b1\u00102R\u001e\u00103\u001a\u0002048\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u00106\"\u0004\b7\u00108R\u001e\u00109\u001a\u00020:8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u0010<\"\u0004\b=\u0010>R\u001e\u0010?\u001a\u00020@8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\bA\u0010B\"\u0004\bC\u0010DR\u001e\u0010E\u001a\u00020F8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010H\"\u0004\bI\u0010JR\u001e\u0010K\u001a\u00020L8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010N\"\u0004\bO\u0010P\u00a8\u0006T"}, d2 = {"Lcom/livewallpaper/features/wallpaper/LiveWallpaperService;", "Landroid/service/wallpaper/WallpaperService;", "()V", "dirtyRegionManager", "Lcom/livewallpaper/core/performance/DirtyRegionManager;", "getDirtyRegionManager", "()Lcom/livewallpaper/core/performance/DirtyRegionManager;", "setDirtyRegionManager", "(Lcom/livewallpaper/core/performance/DirtyRegionManager;)V", "musicCardRenderer", "Lcom/livewallpaper/core/domain/music/MusicCardRenderer;", "getMusicCardRenderer", "()Lcom/livewallpaper/core/domain/music/MusicCardRenderer;", "setMusicCardRenderer", "(Lcom/livewallpaper/core/domain/music/MusicCardRenderer;)V", "musicManager", "Lcom/livewallpaper/core/domain/music/MusicManager;", "getMusicManager", "()Lcom/livewallpaper/core/domain/music/MusicManager;", "setMusicManager", "(Lcom/livewallpaper/core/domain/music/MusicManager;)V", "objectPoolManager", "Lcom/livewallpaper/core/performance/ObjectPoolManager;", "getObjectPoolManager", "()Lcom/livewallpaper/core/performance/ObjectPoolManager;", "setObjectPoolManager", "(Lcom/livewallpaper/core/performance/ObjectPoolManager;)V", "performanceMonitor", "Lcom/livewallpaper/core/performance/PerformanceMonitor;", "getPerformanceMonitor", "()Lcom/livewallpaper/core/performance/PerformanceMonitor;", "setPerformanceMonitor", "(Lcom/livewallpaper/core/performance/PerformanceMonitor;)V", "performanceOptimizer", "Lcom/livewallpaper/core/performance/PerformanceOptimizer;", "getPerformanceOptimizer", "()Lcom/livewallpaper/core/performance/PerformanceOptimizer;", "setPerformanceOptimizer", "(Lcom/livewallpaper/core/performance/PerformanceOptimizer;)V", "sceneInitializer", "Lcom/livewallpaper/core/domain/scene/SceneInitializer;", "getSceneInitializer", "()Lcom/livewallpaper/core/domain/scene/SceneInitializer;", "setSceneInitializer", "(Lcom/livewallpaper/core/domain/scene/SceneInitializer;)V", "sceneManager", "Lcom/livewallpaper/core/domain/scene/SceneManager;", "getSceneManager", "()Lcom/livewallpaper/core/domain/scene/SceneManager;", "setSceneManager", "(Lcom/livewallpaper/core/domain/scene/SceneManager;)V", "sceneRenderer", "Lcom/livewallpaper/core/domain/scene/SceneRenderer;", "getSceneRenderer", "()Lcom/livewallpaper/core/domain/scene/SceneRenderer;", "setSceneRenderer", "(Lcom/livewallpaper/core/domain/scene/SceneRenderer;)V", "settingsRepository", "Lcom/livewallpaper/core/data/repository/SettingsRepository;", "getSettingsRepository", "()Lcom/livewallpaper/core/data/repository/SettingsRepository;", "setSettingsRepository", "(Lcom/livewallpaper/core/data/repository/SettingsRepository;)V", "timeManager", "Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "getTimeManager", "()Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "setTimeManager", "(Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;)V", "weatherEffectRenderer", "Lcom/livewallpaper/core/domain/weather/WeatherEffectRenderer;", "getWeatherEffectRenderer", "()Lcom/livewallpaper/core/domain/weather/WeatherEffectRenderer;", "setWeatherEffectRenderer", "(Lcom/livewallpaper/core/domain/weather/WeatherEffectRenderer;)V", "weatherManager", "Lcom/livewallpaper/core/domain/weather/WeatherManager;", "getWeatherManager", "()Lcom/livewallpaper/core/domain/weather/WeatherManager;", "setWeatherManager", "(Lcom/livewallpaper/core/domain/weather/WeatherManager;)V", "onCreateEngine", "Landroid/service/wallpaper/WallpaperService$Engine;", "LiveWallpaperEngine", "wallpaper_debug"})
public final class LiveWallpaperService extends android.service.wallpaper.WallpaperService {
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.time.WallpaperTimeManager timeManager;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.scene.SceneManager sceneManager;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.scene.SceneRenderer sceneRenderer;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.scene.SceneInitializer sceneInitializer;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.weather.WeatherManager weatherManager;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.weather.WeatherEffectRenderer weatherEffectRenderer;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.music.MusicManager musicManager;
    @javax.inject.Inject()
    public com.livewallpaper.core.domain.music.MusicCardRenderer musicCardRenderer;
    @javax.inject.Inject()
    public com.livewallpaper.core.data.repository.SettingsRepository settingsRepository;
    @javax.inject.Inject()
    public com.livewallpaper.core.performance.PerformanceMonitor performanceMonitor;
    @javax.inject.Inject()
    public com.livewallpaper.core.performance.PerformanceOptimizer performanceOptimizer;
    @javax.inject.Inject()
    public com.livewallpaper.core.performance.DirtyRegionManager dirtyRegionManager;
    @javax.inject.Inject()
    public com.livewallpaper.core.performance.ObjectPoolManager objectPoolManager;
    
    public LiveWallpaperService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.time.WallpaperTimeManager getTimeManager() {
        return null;
    }
    
    public final void setTimeManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeManager p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneManager getSceneManager() {
        return null;
    }
    
    public final void setSceneManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneManager p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneRenderer getSceneRenderer() {
        return null;
    }
    
    public final void setSceneRenderer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneRenderer p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneInitializer getSceneInitializer() {
        return null;
    }
    
    public final void setSceneInitializer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneInitializer p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.weather.WeatherManager getWeatherManager() {
        return null;
    }
    
    public final void setWeatherManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherManager p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.weather.WeatherEffectRenderer getWeatherEffectRenderer() {
        return null;
    }
    
    public final void setWeatherEffectRenderer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherEffectRenderer p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.music.MusicManager getMusicManager() {
        return null;
    }
    
    public final void setMusicManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicManager p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.music.MusicCardRenderer getMusicCardRenderer() {
        return null;
    }
    
    public final void setMusicCardRenderer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicCardRenderer p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.repository.SettingsRepository getSettingsRepository() {
        return null;
    }
    
    public final void setSettingsRepository(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SettingsRepository p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.PerformanceMonitor getPerformanceMonitor() {
        return null;
    }
    
    public final void setPerformanceMonitor(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceMonitor p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.PerformanceOptimizer getPerformanceOptimizer() {
        return null;
    }
    
    public final void setPerformanceOptimizer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceOptimizer p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.DirtyRegionManager getDirtyRegionManager() {
        return null;
    }
    
    public final void setDirtyRegionManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.DirtyRegionManager p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.ObjectPoolManager getObjectPoolManager() {
        return null;
    }
    
    public final void setObjectPoolManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.service.wallpaper.WallpaperService.Engine onCreateEngine() {
        return null;
    }
    
    /**
     * 壁纸引擎 - 负责实际的绘制和生命周期管理
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00bc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0015\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0013\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\u0005\u00a2\u0006\u0002\u0010\u0003J\u000e\u0010,\u001a\u00020-H\u0082@\u00a2\u0006\u0002\u0010.J \u0010/\u001a\u00020-2\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u00020\u0018H\u0002J\u0010\u00105\u001a\u00020-2\u0006\u00100\u001a\u000201H\u0002J\u0018\u00106\u001a\u00020-2\u0006\u00100\u001a\u0002012\u0006\u00107\u001a\u000208H\u0002J\u0010\u00109\u001a\u00020-2\u0006\u00100\u001a\u000201H\u0002J\b\u0010:\u001a\u00020-H\u0002J\u0018\u0010;\u001a\u00020-2\u0006\u00100\u001a\u0002012\u0006\u0010<\u001a\u000208H\u0002J4\u0010=\u001a\u00020-2\u0006\u00100\u001a\u0002012\u0006\u0010>\u001a\u0002082\u0006\u00102\u001a\u00020?2\b\u0010@\u001a\u0004\u0018\u00010A2\b\u0010B\u001a\u0004\u0018\u00010CH\u0002J\u0018\u0010D\u001a\u00020-2\u0006\u00100\u001a\u0002012\u0006\u00104\u001a\u00020\u0018H\u0002J \u0010E\u001a\u00020-2\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u00020\u0018H\u0002J\u0018\u0010F\u001a\u00020G2\u0006\u0010H\u001a\u00020I2\u0006\u0010J\u001a\u000203H\u0002J\u0016\u0010K\u001a\u00020-2\u0006\u0010L\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010MJ\b\u0010N\u001a\u00020-H\u0002J\b\u0010O\u001a\u00020-H\u0002J\b\u0010P\u001a\u00020-H\u0016J(\u0010Q\u001a\u00020-2\u0006\u0010R\u001a\u00020S2\u0006\u0010T\u001a\u00020&2\u0006\u0010U\u001a\u00020&2\u0006\u0010V\u001a\u00020&H\u0016J\u0010\u0010W\u001a\u00020-2\u0006\u0010R\u001a\u00020SH\u0016J\u0010\u0010X\u001a\u00020-2\u0006\u0010R\u001a\u00020SH\u0016J\u0010\u0010Y\u001a\u00020-2\u0006\u0010Z\u001a\u00020\u0018H\u0016J\b\u0010[\u001a\u00020-H\u0002J\b\u0010\\\u001a\u00020-H\u0002J\b\u0010]\u001a\u00020-H\u0002J\b\u0010^\u001a\u00020-H\u0002J\b\u0010_\u001a\u00020-H\u0002J\b\u0010`\u001a\u00020-H\u0002J\b\u0010a\u001a\u00020-H\u0002J\b\u0010b\u001a\u00020-H\u0002J\b\u0010c\u001a\u00020-H\u0002J\u0010\u0010d\u001a\u00020-2\u0006\u0010e\u001a\u00020\u000fH\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010(\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010*\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010+\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006f"}, d2 = {"Lcom/livewallpaper/features/wallpaper/LiveWallpaperService$LiveWallpaperEngine;", "Landroid/service/wallpaper/WallpaperService$Engine;", "Landroid/service/wallpaper/WallpaperService;", "(Lcom/livewallpaper/features/wallpaper/LiveWallpaperService;)V", "animationTime", "", "backgroundGradient", "Landroid/graphics/LinearGradient;", "backgroundPaint", "Landroid/graphics/Paint;", "currentMusicState", "Lcom/livewallpaper/core/domain/music/MusicState;", "currentSceneState", "Lcom/livewallpaper/core/domain/scene/SceneState;", "currentSettings", "Lcom/livewallpaper/core/data/model/WallpaperSettings;", "currentTimeState", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "currentVisualizationData", "Lcom/livewallpaper/core/data/model/MusicVisualizationData;", "currentWeatherState", "Lcom/livewallpaper/core/domain/weather/WeatherState;", "gradientPaint", "isInitialized", "", "isRunning", "isVisible", "lastOptimizationCheck", "", "musicStateJob", "Lkotlinx/coroutines/Job;", "optimizedSettings", "Lcom/livewallpaper/core/performance/OptimizedRenderSettings;", "renderJob", "renderScope", "Lkotlinx/coroutines/CoroutineScope;", "sceneStateJob", "screenHeight", "", "screenWidth", "settingsJob", "textPaint", "timeStateJob", "weatherStateJob", "checkPerformanceOptimization", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "drawCelestialBody", "canvas", "Landroid/graphics/Canvas;", "timeProgress", "", "isDay", "drawContent", "drawErrorState", "errorMessage", "", "drawFallbackContent", "drawFrame", "drawLoadingState", "message", "drawSceneInfo", "sceneName", "Lcom/livewallpaper/core/data/model/TimeProgress;", "weather", "Lcom/livewallpaper/core/data/model/Weather;", "music", "Lcom/livewallpaper/core/data/model/MusicInfo;", "drawStatusInfo", "drawTimeProgressBar", "getColorsForTimePhase", "", "phase", "Lcom/livewallpaper/core/data/model/TimePhase;", "progress", "handleSceneStateChange", "sceneState", "(Lcom/livewallpaper/core/domain/scene/SceneState;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initializeWallpaper", "manageMemory", "onDestroy", "onSurfaceChanged", "holder", "Landroid/view/SurfaceHolder;", "format", "width", "height", "onSurfaceCreated", "onSurfaceDestroyed", "onVisibilityChanged", "visible", "startMusicStateMonitoring", "startRendering", "startSceneStateMonitoring", "startSettingsMonitoring", "startTimeStateMonitoring", "startWeatherStateMonitoring", "stopAllMonitoring", "stopRendering", "updateBackgroundGradient", "updateRenderingParameters", "settings", "wallpaper_debug"})
    public final class LiveWallpaperEngine extends android.service.wallpaper.WallpaperService.Engine {
        private boolean isVisible = false;
        private boolean isRunning = false;
        @org.jetbrains.annotations.Nullable()
        private kotlinx.coroutines.Job renderJob;
        @org.jetbrains.annotations.Nullable()
        private kotlinx.coroutines.Job timeStateJob;
        @org.jetbrains.annotations.Nullable()
        private kotlinx.coroutines.Job sceneStateJob;
        @org.jetbrains.annotations.Nullable()
        private kotlinx.coroutines.Job weatherStateJob;
        @org.jetbrains.annotations.Nullable()
        private kotlinx.coroutines.Job musicStateJob;
        @org.jetbrains.annotations.Nullable()
        private kotlinx.coroutines.Job settingsJob;
        @org.jetbrains.annotations.NotNull()
        private final kotlinx.coroutines.CoroutineScope renderScope = null;
        @org.jetbrains.annotations.NotNull()
        private com.livewallpaper.core.domain.time.WallpaperTimeState currentTimeState;
        @org.jetbrains.annotations.NotNull()
        private com.livewallpaper.core.domain.scene.SceneState currentSceneState;
        @org.jetbrains.annotations.NotNull()
        private com.livewallpaper.core.domain.weather.WeatherState currentWeatherState;
        @org.jetbrains.annotations.NotNull()
        private com.livewallpaper.core.domain.music.MusicState currentMusicState;
        @org.jetbrains.annotations.NotNull()
        private com.livewallpaper.core.data.model.MusicVisualizationData currentVisualizationData;
        @org.jetbrains.annotations.NotNull()
        private com.livewallpaper.core.data.model.WallpaperSettings currentSettings;
        @org.jetbrains.annotations.Nullable()
        private com.livewallpaper.core.performance.OptimizedRenderSettings optimizedSettings;
        private boolean isInitialized = false;
        private long lastOptimizationCheck = 0L;
        @org.jetbrains.annotations.NotNull()
        private final android.graphics.Paint textPaint = null;
        @org.jetbrains.annotations.NotNull()
        private final android.graphics.Paint backgroundPaint = null;
        @org.jetbrains.annotations.NotNull()
        private final android.graphics.Paint gradientPaint = null;
        private int screenWidth = 0;
        private int screenHeight = 0;
        private float animationTime = 0.0F;
        @org.jetbrains.annotations.Nullable()
        private android.graphics.LinearGradient backgroundGradient;
        
        public LiveWallpaperEngine() {
            super();
        }
        
        @java.lang.Override()
        public void onSurfaceCreated(@org.jetbrains.annotations.NotNull()
        android.view.SurfaceHolder holder) {
        }
        
        @java.lang.Override()
        public void onSurfaceChanged(@org.jetbrains.annotations.NotNull()
        android.view.SurfaceHolder holder, int format, int width, int height) {
        }
        
        @java.lang.Override()
        public void onSurfaceDestroyed(@org.jetbrains.annotations.NotNull()
        android.view.SurfaceHolder holder) {
        }
        
        @java.lang.Override()
        public void onVisibilityChanged(boolean visible) {
        }
        
        @java.lang.Override()
        public void onDestroy() {
        }
        
        /**
         * 初始化壁纸系统
         */
        private final void initializeWallpaper() {
        }
        
        /**
         * 开始时间状态监控
         */
        private final void startTimeStateMonitoring() {
        }
        
        /**
         * 开始场景状态监控
         */
        private final void startSceneStateMonitoring() {
        }
        
        /**
         * 处理场景状态变化
         */
        private final java.lang.Object handleSceneStateChange(com.livewallpaper.core.domain.scene.SceneState sceneState, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
            return null;
        }
        
        /**
         * 开始天气状态监控
         */
        private final void startWeatherStateMonitoring() {
        }
        
        /**
         * 开始音乐状态监控
         */
        private final void startMusicStateMonitoring() {
        }
        
        /**
         * 开始设置监控
         */
        private final void startSettingsMonitoring() {
        }
        
        /**
         * 检查性能优化
         */
        private final java.lang.Object checkPerformanceOptimization(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
            return null;
        }
        
        /**
         * 内存管理
         */
        private final void manageMemory() {
        }
        
        /**
         * 更新渲染参数
         */
        private final void updateRenderingParameters(com.livewallpaper.core.data.model.WallpaperSettings settings) {
        }
        
        /**
         * 停止所有监控
         */
        private final void stopAllMonitoring() {
        }
        
        /**
         * 开始渲染循环
         */
        private final void startRendering() {
        }
        
        /**
         * 停止渲染循环
         */
        private final void stopRendering() {
        }
        
        /**
         * 绘制一帧
         */
        private final void drawFrame() {
        }
        
        /**
         * 更新背景渐变
         */
        private final void updateBackgroundGradient() {
        }
        
        /**
         * 根据时间阶段获取颜色
         */
        private final int[] getColorsForTimePhase(com.livewallpaper.core.data.model.TimePhase phase, double progress) {
            return null;
        }
        
        /**
         * 绘制内容
         */
        private final void drawContent(android.graphics.Canvas canvas) {
        }
        
        /**
         * 绘制场景信息
         */
        private final void drawSceneInfo(android.graphics.Canvas canvas, java.lang.String sceneName, com.livewallpaper.core.data.model.TimeProgress timeProgress, com.livewallpaper.core.data.model.Weather weather, com.livewallpaper.core.data.model.MusicInfo music) {
        }
        
        /**
         * 绘制加载状态
         */
        private final void drawLoadingState(android.graphics.Canvas canvas, java.lang.String message) {
        }
        
        /**
         * 绘制错误状态
         */
        private final void drawErrorState(android.graphics.Canvas canvas, java.lang.String errorMessage) {
        }
        
        /**
         * 绘制降级内容
         */
        private final void drawFallbackContent(android.graphics.Canvas canvas) {
        }
        
        /**
         * 绘制天体（太阳/月亮）
         */
        private final void drawCelestialBody(android.graphics.Canvas canvas, double timeProgress, boolean isDay) {
        }
        
        /**
         * 绘制时间进度条
         */
        private final void drawTimeProgressBar(android.graphics.Canvas canvas, double timeProgress, boolean isDay) {
        }
        
        /**
         * 绘制状态信息
         */
        private final void drawStatusInfo(android.graphics.Canvas canvas, boolean isDay) {
        }
    }
}