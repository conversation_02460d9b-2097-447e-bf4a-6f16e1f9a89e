1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livewallpaper.features.wallpaper" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <!-- 壁纸服务 -->
8    <service
8-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:5:5-17:15
9        android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
9-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:6:9-45
10        android:enabled="true"
10-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:7:9-31
11        android:exported="true"
11-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:8:9-32
12        android:label="@string/wallpaper_service_label"
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:9:9-56
13        android:permission="android.permission.BIND_WALLPAPER" >
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:10:9-63
14        <intent-filter>
14-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:11:9-13:25
15            <action android:name="android.service.wallpaper.WallpaperService" />
15-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:12:13-81
15-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:12:21-78
16        </intent-filter>
17
18        <meta-data
18-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:14:9-16:49
19            android:name="android.service.wallpaper"
19-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:15:13-53
20            android:resource="@xml/wallpaper" />
20-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:16:13-46
21    </service>
22
23</manifest>
