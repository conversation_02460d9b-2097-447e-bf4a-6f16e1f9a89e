1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livewallpaper.features.wallpaper" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <application>
7-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:4:5-19:19
8
9        <!-- 壁纸服务 -->
10        <service
10-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:6:9-18:19
11            android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
11-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:7:13-49
12            android:enabled="true"
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:8:13-35
13            android:exported="true"
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:9:13-36
14            android:label="@string/wallpaper_service_label"
14-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:10:13-60
15            android:permission="android.permission.BIND_WALLPAPER" >
15-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:11:13-67
16            <intent-filter>
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:12:13-14:29
17                <action android:name="android.service.wallpaper.WallpaperService" />
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:13:17-85
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:13:25-82
18            </intent-filter>
19
20            <meta-data
20-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:15:13-17:53
21                android:name="android.service.wallpaper"
21-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:16:17-57
22                android:resource="@xml/wallpaper" />
22-->/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/src/main/AndroidManifest.xml:17:17-50
23        </service>
24    </application>
25
26</manifest>
