package com.livewallpaper.features.wallpaper

import android.graphics.*
import android.service.wallpaper.WallpaperService
import android.view.SurfaceHolder
import com.livewallpaper.core.domain.time.TimePhase
import com.livewallpaper.core.domain.time.WallpaperTimeManager
import com.livewallpaper.core.domain.time.WallpaperTimeState
import com.livewallpaper.core.domain.time.getProgressOrDefault
import com.livewallpaper.core.domain.time.isDayOrDefault
import com.livewallpaper.core.domain.time.getPhaseOrDefault
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.catch
import javax.inject.Inject
import kotlin.math.*

/**
 * 动态壁纸服务
 * 这是动态壁纸的核心服务类，集成了时间和天文计算系统
 */
@AndroidEntryPoint
class LiveWallpaperService : WallpaperService() {

    @Inject
    lateinit var timeManager: WallpaperTimeManager

    override fun onCreateEngine(): Engine {
        Logger.d("Creating wallpaper engine")
        return LiveWallpaperEngine()
    }

    /**
     * 壁纸引擎 - 负责实际的绘制和生命周期管理
     */
    inner class LiveWallpaperEngine : Engine() {

        private var isVisible = false
        private var isRunning = false
        private var renderJob: Job? = null
        private var timeStateJob: Job? = null
        private val renderScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

        // 时间状态
        private var currentTimeState: WallpaperTimeState = WallpaperTimeState.Loading

        // 绘制相关
        private val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = 48f
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        private val backgroundPaint = Paint().apply {
            isAntiAlias = true
        }

        private val gradientPaint = Paint().apply {
            isAntiAlias = true
        }

        private var screenWidth = 0
        private var screenHeight = 0
        private var animationTime = 0f

        // 渐变相关
        private var backgroundGradient: LinearGradient? = null

        override fun onSurfaceCreated(holder: SurfaceHolder) {
            super.onSurfaceCreated(holder)
            Logger.d("Surface created")
            isRunning = true
            startTimeStateMonitoring()
        }

        override fun onSurfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
            super.onSurfaceChanged(holder, format, width, height)
            Logger.d("Surface changed: ${width}x${height}")
            screenWidth = width
            screenHeight = height
            updateBackgroundGradient()
        }

        override fun onSurfaceDestroyed(holder: SurfaceHolder) {
            super.onSurfaceDestroyed(holder)
            Logger.d("Surface destroyed")
            isRunning = false
            stopRendering()
            stopTimeStateMonitoring()
        }

        override fun onVisibilityChanged(visible: Boolean) {
            super.onVisibilityChanged(visible)
            Logger.d("Visibility changed: $visible")
            isVisible = visible

            if (visible) {
                startRendering()
            } else {
                stopRendering()
            }
        }

        override fun onDestroy() {
            super.onDestroy()
            Logger.d("Engine destroyed")
            isRunning = false
            stopRendering()
            stopTimeStateMonitoring()
            renderScope.cancel()
        }

        /**
         * 开始时间状态监控
         */
        private fun startTimeStateMonitoring() {
            if (timeStateJob?.isActive == true) return

            Logger.d("Starting time state monitoring")
            timeStateJob = renderScope.launch {
                timeManager.getTimeStateFlow()
                    .catch { e ->
                        Logger.e("Time state flow error", e)
                    }
                    .collect { timeState ->
                        currentTimeState = timeState
                        updateBackgroundGradient()
                    }
            }
        }

        /**
         * 停止时间状态监控
         */
        private fun stopTimeStateMonitoring() {
            Logger.d("Stopping time state monitoring")
            timeStateJob?.cancel()
            timeStateJob = null
        }

        /**
         * 开始渲染循环
         */
        private fun startRendering() {
            if (renderJob?.isActive == true) return

            Logger.d("Starting render loop")
            renderJob = renderScope.launch {
                while (isRunning && isVisible) {
                    try {
                        drawFrame()
                        delay(16) // 约60FPS
                    } catch (e: Exception) {
                        Logger.e("Error in render loop", e)
                        break
                    }
                }
            }
        }

        /**
         * 停止渲染循环
         */
        private fun stopRendering() {
            Logger.d("Stopping render loop")
            renderJob?.cancel()
            renderJob = null
        }

        /**
         * 绘制一帧
         */
        private fun drawFrame() {
            val holder = surfaceHolder
            var canvas: Canvas? = null

            try {
                canvas = holder.lockCanvas()
                if (canvas != null) {
                    drawContent(canvas)
                }
            } catch (e: Exception) {
                Logger.e("Error drawing frame", e)
            } finally {
                if (canvas != null) {
                    try {
                        holder.unlockCanvasAndPost(canvas)
                    } catch (e: Exception) {
                        Logger.e("Error unlocking canvas", e)
                    }
                }
            }
        }

        /**
         * 更新背景渐变
         */
        private fun updateBackgroundGradient() {
            if (screenWidth <= 0 || screenHeight <= 0) return

            val timeProgress = currentTimeState.getProgressOrDefault(0.5)
            val isDay = currentTimeState.isDayOrDefault(true)
            val phase = currentTimeState.getPhaseOrDefault(TimePhase.DAY)

            val colors = getColorsForTimePhase(phase, timeProgress)

            backgroundGradient = LinearGradient(
                0f, 0f,
                0f, screenHeight.toFloat(),
                colors,
                null,
                Shader.TileMode.CLAMP
            )

            gradientPaint.shader = backgroundGradient
        }

        /**
         * 根据时间阶段获取颜色
         */
        private fun getColorsForTimePhase(phase: TimePhase, progress: Double): IntArray {
            return when (phase) {
                TimePhase.NIGHT -> intArrayOf(
                    Color.rgb(10, 10, 30),    // 深蓝夜空
                    Color.rgb(5, 5, 15)       // 更深的底部
                )
                TimePhase.ASTRONOMICAL_TWILIGHT -> intArrayOf(
                    Color.rgb(20, 20, 50),    // 天文暮光
                    Color.rgb(10, 10, 25)
                )
                TimePhase.NAUTICAL_TWILIGHT -> intArrayOf(
                    Color.rgb(40, 40, 80),    // 航海暮光
                    Color.rgb(20, 20, 40)
                )
                TimePhase.CIVIL_TWILIGHT -> {
                    // 根据进度在黄昏和黎明之间变化
                    if (progress < 0.5) {
                        // 黎明
                        intArrayOf(
                            Color.rgb(255, 180, 120), // 橙色天空
                            Color.rgb(120, 80, 160)   // 紫色底部
                        )
                    } else {
                        // 黄昏
                        intArrayOf(
                            Color.rgb(255, 120, 80),  // 红橙色天空
                            Color.rgb(80, 40, 120)    // 深紫色底部
                        )
                    }
                }
                TimePhase.DAY -> intArrayOf(
                    Color.rgb(135, 206, 250), // 天蓝色
                    Color.rgb(176, 224, 230)  // 浅蓝色
                )
                TimePhase.UNKNOWN -> intArrayOf(
                    Color.rgb(100, 100, 100), // 灰色
                    Color.rgb(50, 50, 50)
                )
            }
        }

        /**
         * 绘制内容
         */
        private fun drawContent(canvas: Canvas) {
            // 绘制背景渐变
            if (backgroundGradient != null) {
                canvas.drawRect(0f, 0f, screenWidth.toFloat(), screenHeight.toFloat(), gradientPaint)
            } else {
                canvas.drawColor(Color.BLACK)
            }

            // 更新动画时间
            animationTime += 0.016f // 16ms

            // 获取时间信息
            val timeProgress = currentTimeState.getProgressOrDefault(0.5)
            val phase = currentTimeState.getPhaseOrDefault(TimePhase.DAY)
            val isDay = currentTimeState.isDayOrDefault(true)

            // 绘制太阳/月亮
            drawCelestialBody(canvas, timeProgress, isDay)

            // 绘制主标题
            val title = "智能动态壁纸"
            val titleY = screenHeight * 0.3f + sin(animationTime.toDouble()).toFloat() * 20f
            textPaint.textSize = 64f
            textPaint.color = if (isDay) Color.rgb(50, 50, 50) else Color.WHITE
            canvas.drawText(title, screenWidth / 2f, titleY, textPaint)

            // 绘制时间信息
            val timeInfo = when (phase) {
                TimePhase.NIGHT -> "夜晚"
                TimePhase.ASTRONOMICAL_TWILIGHT -> "天文暮光"
                TimePhase.NAUTICAL_TWILIGHT -> "航海暮光"
                TimePhase.CIVIL_TWILIGHT -> "民用暮光"
                TimePhase.DAY -> "白天"
                TimePhase.UNKNOWN -> "时间未知"
            }

            textPaint.textSize = 32f
            textPaint.color = if (isDay) Color.rgb(80, 80, 80) else Color.rgb(200, 200, 200)
            canvas.drawText(timeInfo, screenWidth / 2f, titleY + 80f, textPaint)

            // 绘制进度条
            drawTimeProgressBar(canvas, timeProgress, isDay)

            // 绘制状态信息
            drawStatusInfo(canvas, isDay)
        }

        /**
         * 绘制天体（太阳/月亮）
         */
        private fun drawCelestialBody(canvas: Canvas, timeProgress: Double, isDay: Boolean) {
            val centerX = screenWidth / 2f
            val radius = 40f

            // 计算天体位置（弧形轨迹）
            val angle = timeProgress * PI
            val x = centerX + cos(angle + PI).toFloat() * screenWidth * 0.3f
            val y = screenHeight * 0.15f + sin(angle).toFloat() * screenHeight * 0.1f

            val bodyPaint = Paint().apply {
                isAntiAlias = true
                color = if (isDay) Color.rgb(255, 255, 100) else Color.rgb(220, 220, 220)
            }

            // 绘制光晕效果
            if (isDay) {
                val glowPaint = Paint().apply {
                    isAntiAlias = true
                    color = Color.rgb(255, 255, 150)
                    alpha = 50
                }
                canvas.drawCircle(x, y, radius * 1.5f, glowPaint)
            }

            // 绘制天体
            canvas.drawCircle(x, y, radius, bodyPaint)
        }

        /**
         * 绘制时间进度条
         */
        private fun drawTimeProgressBar(canvas: Canvas, timeProgress: Double, isDay: Boolean) {
            val barWidth = screenWidth * 0.6f
            val barHeight = 8f
            val barX = (screenWidth - barWidth) / 2f
            val barY = screenHeight * 0.7f

            // 背景条
            val bgPaint = Paint().apply {
                color = if (isDay) Color.rgb(200, 200, 200) else Color.rgb(80, 80, 80)
                isAntiAlias = true
            }
            canvas.drawRoundRect(barX, barY, barX + barWidth, barY + barHeight, barHeight / 2, barHeight / 2, bgPaint)

            // 进度条
            val progressWidth = barWidth * timeProgress.toFloat()
            val progressPaint = Paint().apply {
                color = if (isDay) Color.rgb(100, 150, 255) else Color.rgb(255, 200, 100)
                isAntiAlias = true
            }
            canvas.drawRoundRect(barX, barY, barX + progressWidth, barY + barHeight, barHeight / 2, barHeight / 2, progressPaint)

            // 进度文字
            textPaint.textSize = 24f
            textPaint.color = if (isDay) Color.rgb(100, 100, 100) else Color.rgb(180, 180, 180)
            val progressText = "${(timeProgress * 100).toInt()}%"
            canvas.drawText(progressText, screenWidth / 2f, barY + 40f, textPaint)
        }

        /**
         * 绘制状态信息
         */
        private fun drawStatusInfo(canvas: Canvas, isDay: Boolean) {
            textPaint.textSize = 20f
            textPaint.color = if (isDay) Color.rgb(120, 120, 120) else Color.rgb(160, 160, 160)

            val statusY = screenHeight * 0.9f

            when (currentTimeState) {
                is WallpaperTimeState.Success -> {
                    val location = currentTimeState.location
                    canvas.drawText("位置: ${location.city}", screenWidth / 2f, statusY, textPaint)
                }
                is WallpaperTimeState.Loading -> {
                    canvas.drawText("正在获取位置信息...", screenWidth / 2f, statusY, textPaint)
                }
                is WallpaperTimeState.Error -> {
                    canvas.drawText("位置获取失败", screenWidth / 2f, statusY, textPaint)
                }
            }
        }
    }
}
