package com.livewallpaper.features.wallpaper

import android.graphics.*
import android.service.wallpaper.WallpaperService
import android.view.SurfaceHolder
import com.livewallpaper.core.domain.time.TimePhase
import com.livewallpaper.core.domain.time.WallpaperTimeManager
import com.livewallpaper.core.domain.time.WallpaperTimeState
import com.livewallpaper.core.domain.time.getProgressOrDefault
import com.livewallpaper.core.domain.time.isDayOrDefault
import com.livewallpaper.core.domain.time.getPhaseOrDefault
import com.livewallpaper.core.domain.scene.SceneManager
import com.livewallpaper.core.domain.scene.SceneRenderer
import com.livewallpaper.core.domain.scene.SceneState
import com.livewallpaper.core.domain.scene.SceneLayers
import com.livewallpaper.core.domain.scene.SceneInitializer
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.catch
import javax.inject.Inject
import kotlin.math.*

/**
 * 动态壁纸服务
 * 这是动态壁纸的核心服务类，集成了时间和天文计算系统
 */
@AndroidEntryPoint
class LiveWallpaperService : WallpaperService() {

    @Inject
    lateinit var timeManager: WallpaperTimeManager

    @Inject
    lateinit var sceneManager: SceneManager

    @Inject
    lateinit var sceneRenderer: SceneRenderer

    @Inject
    lateinit var sceneInitializer: SceneInitializer

    override fun onCreateEngine(): Engine {
        Logger.d("Creating wallpaper engine")
        return LiveWallpaperEngine()
    }

    /**
     * 壁纸引擎 - 负责实际的绘制和生命周期管理
     */
    inner class LiveWallpaperEngine : Engine() {

        private var isVisible = false
        private var isRunning = false
        private var renderJob: Job? = null
        private var timeStateJob: Job? = null
        private var sceneStateJob: Job? = null
        private val renderScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

        // 状态管理
        private var currentTimeState: WallpaperTimeState = WallpaperTimeState.Loading
        private var currentSceneState: SceneState = SceneState.Loading
        private var isInitialized = false

        // 绘制相关
        private val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = 48f
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        private val backgroundPaint = Paint().apply {
            isAntiAlias = true
        }

        private val gradientPaint = Paint().apply {
            isAntiAlias = true
        }

        private var screenWidth = 0
        private var screenHeight = 0
        private var animationTime = 0f

        // 渐变相关
        private var backgroundGradient: LinearGradient? = null

        override fun onSurfaceCreated(holder: SurfaceHolder) {
            super.onSurfaceCreated(holder)
            Logger.d("Surface created")
            isRunning = true
            initializeWallpaper()
        }

        override fun onSurfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
            super.onSurfaceChanged(holder, format, width, height)
            Logger.d("Surface changed: ${width}x${height}")
            screenWidth = width
            screenHeight = height
            updateBackgroundGradient()
        }

        override fun onSurfaceDestroyed(holder: SurfaceHolder) {
            super.onSurfaceDestroyed(holder)
            Logger.d("Surface destroyed")
            isRunning = false
            stopAllMonitoring()
        }

        override fun onVisibilityChanged(visible: Boolean) {
            super.onVisibilityChanged(visible)
            Logger.d("Visibility changed: $visible")
            isVisible = visible

            if (visible) {
                startRendering()
            } else {
                stopRendering()
            }
        }

        override fun onDestroy() {
            super.onDestroy()
            Logger.d("Engine destroyed")
            isRunning = false
            stopAllMonitoring()
            sceneManager.cleanup()
            renderScope.cancel()
        }

        /**
         * 初始化壁纸系统
         */
        private fun initializeWallpaper() {
            if (isInitialized) return

            Logger.d("Initializing wallpaper system")
            renderScope.launch {
                try {
                    // 初始化默认场景
                    if (sceneInitializer.shouldInitialize()) {
                        sceneInitializer.initializeDefaultScenes()
                    }

                    isInitialized = true
                    startTimeStateMonitoring()
                    startSceneStateMonitoring()
                } catch (e: Exception) {
                    Logger.e("Failed to initialize wallpaper", e)
                }
            }
        }

        /**
         * 开始时间状态监控
         */
        private fun startTimeStateMonitoring() {
            if (timeStateJob?.isActive == true) return

            Logger.d("Starting time state monitoring")
            timeStateJob = renderScope.launch {
                timeManager.getTimeStateFlow()
                    .catch { e ->
                        Logger.e("Time state flow error", e)
                    }
                    .collect { timeState ->
                        currentTimeState = timeState
                        updateBackgroundGradient()
                    }
            }
        }

        /**
         * 开始场景状态监控
         */
        private fun startSceneStateMonitoring() {
            if (sceneStateJob?.isActive == true) return

            Logger.d("Starting scene state monitoring")
            sceneStateJob = renderScope.launch {
                sceneManager.getCurrentSceneFlow()
                    .catch { e ->
                        Logger.e("Scene state flow error", e)
                    }
                    .collect { sceneState ->
                        currentSceneState = sceneState
                        handleSceneStateChange(sceneState)
                    }
            }
        }

        /**
         * 处理场景状态变化
         */
        private suspend fun handleSceneStateChange(sceneState: SceneState) {
            when (sceneState) {
                is SceneState.Success -> {
                    if (sceneState.layers == null) {
                        // 需要加载场景资源
                        val result = sceneManager.loadSceneResources(
                            sceneState.scene,
                            screenWidth,
                            screenHeight
                        )
                        if (result is com.livewallpaper.core.utils.Resource.Success) {
                            Logger.d("Scene resources loaded: ${sceneState.scene.name}")
                        }
                    }
                }
                is SceneState.Error -> {
                    Logger.e("Scene state error: ${sceneState.message}")
                }
                is SceneState.Loading -> {
                    Logger.d("Scene loading...")
                }
            }
        }

        /**
         * 停止所有监控
         */
        private fun stopAllMonitoring() {
            Logger.d("Stopping all monitoring")
            timeStateJob?.cancel()
            timeStateJob = null
            sceneStateJob?.cancel()
            sceneStateJob = null
            stopRendering()
        }

        /**
         * 开始渲染循环
         */
        private fun startRendering() {
            if (renderJob?.isActive == true) return

            Logger.d("Starting render loop")
            renderJob = renderScope.launch {
                while (isRunning && isVisible) {
                    try {
                        drawFrame()
                        delay(16) // 约60FPS
                    } catch (e: Exception) {
                        Logger.e("Error in render loop", e)
                        break
                    }
                }
            }
        }

        /**
         * 停止渲染循环
         */
        private fun stopRendering() {
            Logger.d("Stopping render loop")
            renderJob?.cancel()
            renderJob = null
        }

        /**
         * 绘制一帧
         */
        private fun drawFrame() {
            val holder = surfaceHolder
            var canvas: Canvas? = null

            try {
                canvas = holder.lockCanvas()
                if (canvas != null) {
                    drawContent(canvas)
                }
            } catch (e: Exception) {
                Logger.e("Error drawing frame", e)
            } finally {
                if (canvas != null) {
                    try {
                        holder.unlockCanvasAndPost(canvas)
                    } catch (e: Exception) {
                        Logger.e("Error unlocking canvas", e)
                    }
                }
            }
        }

        /**
         * 更新背景渐变
         */
        private fun updateBackgroundGradient() {
            if (screenWidth <= 0 || screenHeight <= 0) return

            val timeProgress = currentTimeState.getProgressOrDefault(0.5)
            val isDay = currentTimeState.isDayOrDefault(true)
            val phase = currentTimeState.getPhaseOrDefault(TimePhase.DAY)

            val colors = getColorsForTimePhase(phase, timeProgress)

            backgroundGradient = LinearGradient(
                0f, 0f,
                0f, screenHeight.toFloat(),
                colors,
                null,
                Shader.TileMode.CLAMP
            )

            gradientPaint.shader = backgroundGradient
        }

        /**
         * 根据时间阶段获取颜色
         */
        private fun getColorsForTimePhase(phase: TimePhase, progress: Double): IntArray {
            return when (phase) {
                TimePhase.NIGHT -> intArrayOf(
                    Color.rgb(10, 10, 30),    // 深蓝夜空
                    Color.rgb(5, 5, 15)       // 更深的底部
                )
                TimePhase.ASTRONOMICAL_TWILIGHT -> intArrayOf(
                    Color.rgb(20, 20, 50),    // 天文暮光
                    Color.rgb(10, 10, 25)
                )
                TimePhase.NAUTICAL_TWILIGHT -> intArrayOf(
                    Color.rgb(40, 40, 80),    // 航海暮光
                    Color.rgb(20, 20, 40)
                )
                TimePhase.CIVIL_TWILIGHT -> {
                    // 根据进度在黄昏和黎明之间变化
                    if (progress < 0.5) {
                        // 黎明
                        intArrayOf(
                            Color.rgb(255, 180, 120), // 橙色天空
                            Color.rgb(120, 80, 160)   // 紫色底部
                        )
                    } else {
                        // 黄昏
                        intArrayOf(
                            Color.rgb(255, 120, 80),  // 红橙色天空
                            Color.rgb(80, 40, 120)    // 深紫色底部
                        )
                    }
                }
                TimePhase.DAY -> intArrayOf(
                    Color.rgb(135, 206, 250), // 天蓝色
                    Color.rgb(176, 224, 230)  // 浅蓝色
                )
                TimePhase.UNKNOWN -> intArrayOf(
                    Color.rgb(100, 100, 100), // 灰色
                    Color.rgb(50, 50, 50)
                )
            }
        }

        /**
         * 绘制内容
         */
        private fun drawContent(canvas: Canvas) {
            // 更新动画时间
            animationTime += 0.016f // 16ms

            // 获取当前状态
            val timeProgress = when (val timeState = currentTimeState) {
                is WallpaperTimeState.Success -> timeState.timeProgress
                else -> null
            }

            // 如果有场景数据，使用场景渲染器
            when (val sceneState = currentSceneState) {
                is SceneState.Success -> {
                    if (sceneState.layers != null && timeProgress != null) {
                        // 使用场景渲染器绘制
                        sceneRenderer.renderScene(
                            canvas = canvas,
                            sceneLayers = sceneState.layers,
                            timeProgress = timeProgress,
                            animationTime = animationTime,
                            enableParallax = true
                        )

                        // 绘制场景信息
                        drawSceneInfo(canvas, sceneState.scene.name, timeProgress)
                    } else {
                        // 场景资源未加载，绘制加载状态
                        drawLoadingState(canvas, "加载场景中...")
                    }
                }
                is SceneState.Loading -> {
                    drawLoadingState(canvas, "初始化场景系统...")
                }
                is SceneState.Error -> {
                    drawErrorState(canvas, sceneState.message)
                }
            }

            // 如果没有时间进度数据，绘制降级界面
            if (timeProgress == null) {
                drawFallbackContent(canvas)
            }
        }

        /**
         * 绘制场景信息
         */
        private fun drawSceneInfo(canvas: Canvas, sceneName: String, timeProgress: com.livewallpaper.core.domain.time.TimeProgress) {
            val isDay = timeProgress.isDay
            val width = canvas.width.toFloat()
            val height = canvas.height.toFloat()

            // 绘制场景名称
            textPaint.textSize = 48f
            textPaint.color = if (isDay) Color.argb(180, 50, 50, 50) else Color.argb(180, 255, 255, 255)
            textPaint.setShadowLayer(4f, 2f, 2f, Color.argb(100, 0, 0, 0))

            val titleY = height * 0.85f + sin(animationTime.toDouble()).toFloat() * 10f
            canvas.drawText(sceneName, width / 2f, titleY, textPaint)

            // 绘制时间阶段
            val phaseText = when (timeProgress.phase) {
                TimePhase.NIGHT -> "夜晚"
                TimePhase.ASTRONOMICAL_TWILIGHT -> "天文暮光"
                TimePhase.NAUTICAL_TWILIGHT -> "航海暮光"
                TimePhase.CIVIL_TWILIGHT -> "民用暮光"
                TimePhase.DAY -> "白天"
                TimePhase.UNKNOWN -> "时间未知"
            }

            textPaint.textSize = 24f
            textPaint.color = if (isDay) Color.argb(150, 80, 80, 80) else Color.argb(150, 200, 200, 200)
            canvas.drawText(phaseText, width / 2f, titleY + 40f, textPaint)

            // 清除阴影
            textPaint.clearShadowLayer()
        }

        /**
         * 绘制加载状态
         */
        private fun drawLoadingState(canvas: Canvas, message: String) {
            canvas.drawColor(Color.rgb(20, 20, 30))

            val width = canvas.width.toFloat()
            val height = canvas.height.toFloat()

            textPaint.textSize = 32f
            textPaint.color = Color.WHITE
            canvas.drawText(message, width / 2f, height / 2f, textPaint)

            // 绘制加载动画
            val loadingRadius = 30f + sin(animationTime * 3).toFloat() * 5f
            val loadingPaint = Paint().apply {
                color = Color.WHITE
                alpha = (128 + 127 * sin(animationTime * 2)).toInt()
                isAntiAlias = true
            }
            canvas.drawCircle(width / 2f, height / 2f + 60f, loadingRadius, loadingPaint)
        }

        /**
         * 绘制错误状态
         */
        private fun drawErrorState(canvas: Canvas, errorMessage: String) {
            canvas.drawColor(Color.rgb(40, 20, 20))

            val width = canvas.width.toFloat()
            val height = canvas.height.toFloat()

            textPaint.textSize = 28f
            textPaint.color = Color.rgb(255, 150, 150)
            canvas.drawText("场景加载失败", width / 2f, height / 2f - 30f, textPaint)

            textPaint.textSize = 20f
            textPaint.color = Color.rgb(200, 100, 100)
            canvas.drawText(errorMessage, width / 2f, height / 2f + 20f, textPaint)
        }

        /**
         * 绘制降级内容
         */
        private fun drawFallbackContent(canvas: Canvas) {
            // 绘制简单的渐变背景
            if (backgroundGradient != null) {
                canvas.drawRect(0f, 0f, screenWidth.toFloat(), screenHeight.toFloat(), gradientPaint)
            } else {
                canvas.drawColor(Color.BLACK)
            }

            // 绘制基本信息
            val width = canvas.width.toFloat()
            val height = canvas.height.toFloat()

            textPaint.textSize = 48f
            textPaint.color = Color.WHITE
            val titleY = height * 0.3f + sin(animationTime.toDouble()).toFloat() * 20f
            canvas.drawText("智能动态壁纸", width / 2f, titleY, textPaint)

            textPaint.textSize = 24f
            textPaint.color = Color.rgb(200, 200, 200)
            canvas.drawText("正在初始化...", width / 2f, titleY + 60f, textPaint)
        }

        /**
         * 绘制天体（太阳/月亮）
         */
        private fun drawCelestialBody(canvas: Canvas, timeProgress: Double, isDay: Boolean) {
            val centerX = screenWidth / 2f
            val radius = 40f

            // 计算天体位置（弧形轨迹）
            val angle = timeProgress * PI
            val x = centerX + cos(angle + PI).toFloat() * screenWidth * 0.3f
            val y = screenHeight * 0.15f + sin(angle).toFloat() * screenHeight * 0.1f

            val bodyPaint = Paint().apply {
                isAntiAlias = true
                color = if (isDay) Color.rgb(255, 255, 100) else Color.rgb(220, 220, 220)
            }

            // 绘制光晕效果
            if (isDay) {
                val glowPaint = Paint().apply {
                    isAntiAlias = true
                    color = Color.rgb(255, 255, 150)
                    alpha = 50
                }
                canvas.drawCircle(x, y, radius * 1.5f, glowPaint)
            }

            // 绘制天体
            canvas.drawCircle(x, y, radius, bodyPaint)
        }

        /**
         * 绘制时间进度条
         */
        private fun drawTimeProgressBar(canvas: Canvas, timeProgress: Double, isDay: Boolean) {
            val barWidth = screenWidth * 0.6f
            val barHeight = 8f
            val barX = (screenWidth - barWidth) / 2f
            val barY = screenHeight * 0.7f

            // 背景条
            val bgPaint = Paint().apply {
                color = if (isDay) Color.rgb(200, 200, 200) else Color.rgb(80, 80, 80)
                isAntiAlias = true
            }
            canvas.drawRoundRect(barX, barY, barX + barWidth, barY + barHeight, barHeight / 2, barHeight / 2, bgPaint)

            // 进度条
            val progressWidth = barWidth * timeProgress.toFloat()
            val progressPaint = Paint().apply {
                color = if (isDay) Color.rgb(100, 150, 255) else Color.rgb(255, 200, 100)
                isAntiAlias = true
            }
            canvas.drawRoundRect(barX, barY, barX + progressWidth, barY + barHeight, barHeight / 2, barHeight / 2, progressPaint)

            // 进度文字
            textPaint.textSize = 24f
            textPaint.color = if (isDay) Color.rgb(100, 100, 100) else Color.rgb(180, 180, 180)
            val progressText = "${(timeProgress * 100).toInt()}%"
            canvas.drawText(progressText, screenWidth / 2f, barY + 40f, textPaint)
        }

        /**
         * 绘制状态信息
         */
        private fun drawStatusInfo(canvas: Canvas, isDay: Boolean) {
            textPaint.textSize = 20f
            textPaint.color = if (isDay) Color.rgb(120, 120, 120) else Color.rgb(160, 160, 160)

            val statusY = screenHeight * 0.9f

            when (currentTimeState) {
                is WallpaperTimeState.Success -> {
                    val location = currentTimeState.location
                    canvas.drawText("位置: ${location.city}", screenWidth / 2f, statusY, textPaint)
                }
                is WallpaperTimeState.Loading -> {
                    canvas.drawText("正在获取位置信息...", screenWidth / 2f, statusY, textPaint)
                }
                is WallpaperTimeState.Error -> {
                    canvas.drawText("位置获取失败", screenWidth / 2f, statusY, textPaint)
                }
            }
        }
    }
}
