package com.livewallpaper.features.wallpaper

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.service.wallpaper.WallpaperService
import android.view.SurfaceHolder
import com.livewallpaper.core.utils.Logger
import kotlinx.coroutines.*

/**
 * 动态壁纸服务
 * 这是动态壁纸的核心服务类
 */
class LiveWallpaperService : WallpaperService() {
    
    override fun onCreateEngine(): Engine {
        Logger.d("Creating wallpaper engine")
        return LiveWallpaperEngine()
    }
    
    /**
     * 壁纸引擎 - 负责实际的绘制和生命周期管理
     */
    inner class LiveWallpaperEngine : Engine() {
        
        private var isVisible = false
        private var isRunning = false
        private var renderJob: Job? = null
        private val renderScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
        
        // 绘制相关
        private val paint = Paint().apply {
            color = Color.WHITE
            textSize = 48f
            isAntiAlias = true
        }
        
        private var screenWidth = 0
        private var screenHeight = 0
        private var animationTime = 0f
        
        override fun onSurfaceCreated(holder: SurfaceHolder) {
            super.onSurfaceCreated(holder)
            Logger.d("Surface created")
            isRunning = true
        }
        
        override fun onSurfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
            super.onSurfaceChanged(holder, format, width, height)
            Logger.d("Surface changed: ${width}x${height}")
            screenWidth = width
            screenHeight = height
        }
        
        override fun onSurfaceDestroyed(holder: SurfaceHolder) {
            super.onSurfaceDestroyed(holder)
            Logger.d("Surface destroyed")
            isRunning = false
            stopRendering()
        }
        
        override fun onVisibilityChanged(visible: Boolean) {
            super.onVisibilityChanged(visible)
            Logger.d("Visibility changed: $visible")
            isVisible = visible
            
            if (visible) {
                startRendering()
            } else {
                stopRendering()
            }
        }
        
        override fun onDestroy() {
            super.onDestroy()
            Logger.d("Engine destroyed")
            isRunning = false
            stopRendering()
            renderScope.cancel()
        }
        
        /**
         * 开始渲染循环
         */
        private fun startRendering() {
            if (renderJob?.isActive == true) return
            
            Logger.d("Starting render loop")
            renderJob = renderScope.launch {
                while (isRunning && isVisible) {
                    try {
                        drawFrame()
                        delay(16) // 约60FPS
                    } catch (e: Exception) {
                        Logger.e("Error in render loop", e)
                        break
                    }
                }
            }
        }
        
        /**
         * 停止渲染循环
         */
        private fun stopRendering() {
            Logger.d("Stopping render loop")
            renderJob?.cancel()
            renderJob = null
        }
        
        /**
         * 绘制一帧
         */
        private fun drawFrame() {
            val holder = surfaceHolder
            var canvas: Canvas? = null
            
            try {
                canvas = holder.lockCanvas()
                if (canvas != null) {
                    drawContent(canvas)
                }
            } catch (e: Exception) {
                Logger.e("Error drawing frame", e)
            } finally {
                if (canvas != null) {
                    try {
                        holder.unlockCanvasAndPost(canvas)
                    } catch (e: Exception) {
                        Logger.e("Error unlocking canvas", e)
                    }
                }
            }
        }
        
        /**
         * 绘制内容
         */
        private fun drawContent(canvas: Canvas) {
            // 清空画布
            canvas.drawColor(Color.BLACK)
            
            // 更新动画时间
            animationTime += 0.016f // 16ms
            
            // 绘制简单的动画文字
            val text = "动态壁纸"
            val x = screenWidth / 2f
            val y = screenHeight / 2f + Math.sin(animationTime.toDouble()).toFloat() * 50f
            
            // 设置文字颜色（随时间变化）
            val hue = (animationTime * 30) % 360
            paint.color = Color.HSVToColor(floatArrayOf(hue, 1f, 1f))
            
            // 居中绘制文字
            val textWidth = paint.measureText(text)
            canvas.drawText(text, x - textWidth / 2, y, paint)
            
            // 绘制时间信息
            val timeText = "时间: ${System.currentTimeMillis() / 1000}"
            paint.textSize = 24f
            paint.color = Color.WHITE
            canvas.drawText(timeText, 50f, 100f, paint)
            
            // 恢复文字大小
            paint.textSize = 48f
        }
    }
}
