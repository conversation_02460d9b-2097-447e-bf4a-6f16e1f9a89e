<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="CHOOSE_PER_TEST" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/core" />
            <option value="$PROJECT_DIR$/features" />
            <option value="$PROJECT_DIR$/features/music" />
            <option value="$PROJECT_DIR$/features/wallpaper" />
            <option value="$PROJECT_DIR$/features/weather" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>