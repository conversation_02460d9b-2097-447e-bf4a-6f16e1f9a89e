<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dbc4595d-bc91-48b1-8dcd-fae322861d63" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/app/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/app/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/AboutActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/AboutActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/MainActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/MainActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/MusicTestActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/MusicTestActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SceneTestActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SceneTestActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SceneTestViewModel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SceneTestViewModel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SettingsActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SettingsActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SettingsViewModel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/SettingsViewModel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/TimeTestActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/TimeTestActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/WeatherTestActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/WeatherTestActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/WeatherTestViewModel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/livewallpaper/app/WeatherTestViewModel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/values/app_info.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/values/strings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/values/strings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/values/themes.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/values/themes.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/core/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/data/model/Music.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/data/model/WallpaperSettings.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/data/model/WallpaperSettings.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/data/preferences/SettingsDataStore.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/data/preferences/SettingsDataStore.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/di/TimeModule.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/di/TimeModule.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/location/LocationManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/location/LocationManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/music/MusicCardRenderer.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/music/MusicCardRenderer.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/scene/SceneInitializer.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/scene/SceneInitializer.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/scene/SceneManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/scene/SceneManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/scene/SceneRenderer.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/scene/SceneRenderer.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/time/TimeAndSunCalcManager.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/time/WallpaperTimeManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/time/WallpaperTimeManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/weather/WeatherManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/domain/weather/WeatherManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/performance/ObjectPool.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/performance/ObjectPool.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/service/MusicNotificationListenerService.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/service/MusicNotificationListenerService.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/utils/AppUpdateChecker.kt" beforeDir="false" afterPath="$PROJECT_DIR$/core/src/main/java/com/livewallpaper/core/utils/AppUpdateChecker.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/features/wallpaper/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/features/wallpaper/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/features/wallpaper/src/main/java/com/livewallpaper/features/wallpaper/LiveWallpaperService.kt" beforeDir="false" afterPath="$PROJECT_DIR$/features/wallpaper/src/main/java/com/livewallpaper/features/wallpaper/LiveWallpaperService.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gradle/libs.versions.toml" beforeDir="false" afterPath="$PROJECT_DIR$/gradle/libs.versions.toml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=10ACCE0TDQ000Y3)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2yMIE36wovhbB5Tqs2U9XKT0c15" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "/Users/<USER>/Documents/GitHub/LiveWallpaper"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="LiveWallpaper.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="dbc4595d-bc91-48b1-8dcd-fae322861d63" name="Changes" comment="" />
      <created>1749637905170</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749637905170</updated>
    </task>
    <servers />
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.livewallpaper.app">
          <value>
            <CheckInfo lastCheckTimestamp="1749638817025" />
          </value>
        </entry>
        <entry key="com.livewallpaper.app.debug">
          <value>
            <CheckInfo lastCheckTimestamp="1749638817031" />
          </value>
        </entry>
        <entry key="com.livewallpaper.app.debug.test">
          <value>
            <CheckInfo lastCheckTimestamp="1749638817024" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>