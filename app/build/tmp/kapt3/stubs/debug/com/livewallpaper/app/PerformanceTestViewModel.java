package com.livewallpaper.app;

/**
 * 性能测试ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010&\u001a\u00020\'J\u0006\u0010(\u001a\u00020\'J\b\u0010)\u001a\u00020\'H\u0002J\u0006\u0010*\u001a\u00020\'R\u0016\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u000e\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00110\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00130\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00150\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\u001a\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00110\u000f0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0019\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00130\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u0017\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00150\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0019\u00a8\u0006+"}, d2 = {"Lcom/livewallpaper/app/PerformanceTestViewModel;", "Landroidx/lifecycle/ViewModel;", "performanceMonitor", "Lcom/livewallpaper/core/performance/PerformanceMonitor;", "performanceOptimizer", "Lcom/livewallpaper/core/performance/PerformanceOptimizer;", "dirtyRegionManager", "Lcom/livewallpaper/core/performance/DirtyRegionManager;", "objectPoolManager", "Lcom/livewallpaper/core/performance/ObjectPoolManager;", "(Lcom/livewallpaper/core/performance/PerformanceMonitor;Lcom/livewallpaper/core/performance/PerformanceOptimizer;Lcom/livewallpaper/core/performance/DirtyRegionManager;Lcom/livewallpaper/core/performance/ObjectPoolManager;)V", "_dirtyRegionStats", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/livewallpaper/core/performance/DirtyRegionStats;", "_objectPoolStats", "", "", "", "_optimizedSettings", "Lcom/livewallpaper/core/performance/OptimizedRenderSettings;", "_recommendations", "", "dirtyRegionStats", "Lkotlinx/coroutines/flow/StateFlow;", "getDirtyRegionStats", "()Lkotlinx/coroutines/flow/StateFlow;", "objectPoolStats", "getObjectPoolStats", "optimizedSettings", "getOptimizedSettings", "performanceLevel", "Lcom/livewallpaper/core/performance/PerformanceLevel;", "getPerformanceLevel", "performanceMetrics", "Lcom/livewallpaper/core/performance/PerformanceMetrics;", "getPerformanceMetrics", "recommendations", "getRecommendations", "clearObjectPools", "", "resetStats", "startPerformanceMonitoring", "triggerGC", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class PerformanceTestViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.PerformanceMonitor performanceMonitor = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.PerformanceOptimizer performanceOptimizer = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.DirtyRegionManager dirtyRegionManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPoolManager objectPoolManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.PerformanceMetrics> performanceMetrics = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.PerformanceLevel> performanceLevel = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.performance.OptimizedRenderSettings> _optimizedSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.OptimizedRenderSettings> optimizedSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.performance.DirtyRegionStats> _dirtyRegionStats = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.DirtyRegionStats> dirtyRegionStats = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<java.lang.String>> _recommendations = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> recommendations = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, java.lang.Integer>> _objectPoolStats = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.lang.Integer>> objectPoolStats = null;
    
    @javax.inject.Inject()
    public PerformanceTestViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceMonitor performanceMonitor, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceOptimizer performanceOptimizer, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.DirtyRegionManager dirtyRegionManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager objectPoolManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.PerformanceMetrics> getPerformanceMetrics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.PerformanceLevel> getPerformanceLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.OptimizedRenderSettings> getOptimizedSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.performance.DirtyRegionStats> getDirtyRegionStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<java.lang.String>> getRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, java.lang.Integer>> getObjectPoolStats() {
        return null;
    }
    
    private final void startPerformanceMonitoring() {
    }
    
    /**
     * 触发垃圾回收
     */
    public final void triggerGC() {
    }
    
    /**
     * 重置统计数据
     */
    public final void resetStats() {
    }
    
    /**
     * 清理对象池
     */
    public final void clearObjectPools() {
    }
}