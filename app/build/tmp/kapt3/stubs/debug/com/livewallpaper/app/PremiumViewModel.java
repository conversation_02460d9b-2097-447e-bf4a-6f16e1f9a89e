package com.livewallpaper.app;

/**
 * 高级功能购买页面ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010$\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u001a\u001a\u00020\u0007J\u0006\u0010\u001b\u001a\u00020\u001cJ\b\u0010\u001d\u001a\u0004\u0018\u00010\fJ\u0010\u0010\u001e\u001a\u0004\u0018\u00010\f2\u0006\u0010\u001f\u001a\u00020\u0007J\u0018\u0010 \u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0!J\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\f0\u000bJ\b\u0010#\u001a\u00020\u001cH\u0002J\u000e\u0010$\u001a\u00020\t2\u0006\u0010\u001f\u001a\u00020\u0007J\u0006\u0010%\u001a\u00020\u001cJ\b\u0010&\u001a\u00020\u001cH\u0014J\u0016\u0010\'\u001a\u00020\u001c2\u0006\u0010(\u001a\u00020)2\u0006\u0010\u001f\u001a\u00020\u0007J\u0006\u0010*\u001a\u00020\u001cJ\u0006\u0010+\u001a\u00020\u001cR\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0019\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u001d\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0011\u00a8\u0006,"}, d2 = {"Lcom/livewallpaper/app/PremiumViewModel;", "Landroidx/lifecycle/ViewModel;", "billingManager", "Lcom/livewallpaper/core/domain/billing/BillingManager;", "(Lcom/livewallpaper/core/domain/billing/BillingManager;)V", "_errorMessage", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_isLoading", "", "_products", "", "Lcom/livewallpaper/core/domain/billing/ProductDetails;", "billingState", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/livewallpaper/core/domain/billing/BillingState;", "getBillingState", "()Lkotlinx/coroutines/flow/StateFlow;", "errorMessage", "getErrorMessage", "isLoading", "products", "getProducts", "purchaseState", "Lcom/livewallpaper/core/domain/billing/PurchaseState;", "getPurchaseState", "calculateTotalValue", "clearErrorMessage", "", "getBestDealProduct", "getProductDetails", "productId", "getProductsByCategory", "", "getRecommendedProducts", "initializeBilling", "isProductPurchased", "loadProducts", "onCleared", "purchaseProduct", "activity", "Landroid/app/Activity;", "refresh", "restorePurchases", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class PremiumViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.billing.BillingManager billingManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.livewallpaper.core.domain.billing.ProductDetails>> _products = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.domain.billing.ProductDetails>> products = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.BillingState> billingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.PurchaseState> purchaseState = null;
    
    @javax.inject.Inject()
    public PremiumViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.billing.BillingManager billingManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.domain.billing.ProductDetails>> getProducts() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.BillingState> getBillingState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.PurchaseState> getPurchaseState() {
        return null;
    }
    
    /**
     * 初始化计费服务
     */
    private final void initializeBilling() {
    }
    
    /**
     * 加载产品列表
     */
    public final void loadProducts() {
    }
    
    /**
     * 购买产品
     */
    public final void purchaseProduct(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    java.lang.String productId) {
    }
    
    /**
     * 恢复购买
     */
    public final void restorePurchases() {
    }
    
    /**
     * 检查产品是否已购买
     */
    public final boolean isProductPurchased(@org.jetbrains.annotations.NotNull()
    java.lang.String productId) {
        return false;
    }
    
    /**
     * 获取产品详情
     */
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.domain.billing.ProductDetails getProductDetails(@org.jetbrains.annotations.NotNull()
    java.lang.String productId) {
        return null;
    }
    
    /**
     * 清除错误消息
     */
    public final void clearErrorMessage() {
    }
    
    /**
     * 刷新产品和购买状态
     */
    public final void refresh() {
    }
    
    /**
     * 获取推荐产品
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.livewallpaper.core.domain.billing.ProductDetails> getRecommendedProducts() {
        return null;
    }
    
    /**
     * 获取产品类别
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.util.List<com.livewallpaper.core.domain.billing.ProductDetails>> getProductsByCategory() {
        return null;
    }
    
    /**
     * 计算总价值
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String calculateTotalValue() {
        return null;
    }
    
    /**
     * 获取最优惠的套餐
     */
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.domain.billing.ProductDetails getBestDealProduct() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}