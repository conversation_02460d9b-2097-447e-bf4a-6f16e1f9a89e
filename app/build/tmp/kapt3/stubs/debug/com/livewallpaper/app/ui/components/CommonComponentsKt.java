package com.livewallpaper.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0018\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0003H\u0007\u001a\b\u0010\u0007\u001a\u00020\u0001H\u0007\u00a8\u0006\b"}, d2 = {"ErrorCard", "", "message", "", "InfoRow", "label", "value", "LoadingCard", "app_debug"})
public final class CommonComponentsKt {
    
    /**
     * 通用信息行组件
     */
    @androidx.compose.runtime.Composable()
    public static final void InfoRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
    
    /**
     * 错误卡片组件
     */
    @androidx.compose.runtime.Composable()
    public static final void ErrorCard(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    /**
     * 加载卡片组件
     */
    @androidx.compose.runtime.Composable()
    public static final void LoadingCard() {
    }
}