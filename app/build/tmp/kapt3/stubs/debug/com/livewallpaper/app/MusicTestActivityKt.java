package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0018\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0003H\u0007\u001a\b\u0010\u0007\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\nH\u0007\u001a\u0010\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\rH\u0007\u001a \u0010\u000e\u001a\u00020\u00012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001a\b\u0010\u0013\u001a\u00020\u0001H\u0007\u001a\u001e\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010H\u0007\u001a\b\u0010\u0018\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\u001bH\u0007\u001a\u0015\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001d\u001a\u00020\u001eH\u0002\u00a2\u0006\u0002\u0010\u001f\u00a8\u0006 "}, d2 = {"ErrorCard", "", "message", "", "InfoRow", "label", "value", "LoadingCard", "MusicDetailsCard", "musicInfo", "Lcom/livewallpaper/core/data/model/MusicInfo;", "MusicStateCard", "musicState", "Lcom/livewallpaper/core/domain/music/MusicState;", "MusicTestScreen", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/livewallpaper/app/MusicTestViewModel;", "NoMusicCard", "NotificationPermissionCard", "hasPermission", "", "onOpenSettings", "SupportedAppsCard", "VisualizationDataCard", "visualizationData", "Lcom/livewallpaper/core/data/model/MusicVisualizationData;", "formatTimestamp", "instant", "error/NonExistentClass", "(Lerror/NonExistentClass;)Ljava/lang/String;", "app_debug"})
public final class MusicTestActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MusicTestScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.MusicTestViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void NotificationPermissionCard(boolean hasPermission, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onOpenSettings) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MusicStateCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicState musicState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MusicDetailsCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.MusicInfo musicInfo) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void VisualizationDataCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.MusicVisualizationData visualizationData) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SupportedAppsCard() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void NoMusicCard() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InfoRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ErrorCard(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LoadingCard() {
    }
    
    private static final java.lang.String formatTimestamp(error.NonExistentClass instant) {
        return null;
    }
}