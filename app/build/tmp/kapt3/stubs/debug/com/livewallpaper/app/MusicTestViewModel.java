package com.livewallpaper.app;

/**
 * 音乐测试ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0010\u001a\u00020\u0011R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\tR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\t\u00a8\u0006\u0012"}, d2 = {"Lcom/livewallpaper/app/MusicTestViewModel;", "Landroidx/lifecycle/ViewModel;", "musicManager", "Lcom/livewallpaper/core/domain/music/MusicManager;", "(Lcom/livewallpaper/core/domain/music/MusicManager;)V", "hasNotificationPermission", "Lkotlinx/coroutines/flow/StateFlow;", "", "getHasNotificationPermission", "()Lkotlinx/coroutines/flow/StateFlow;", "musicState", "Lcom/livewallpaper/core/domain/music/MusicState;", "getMusicState", "visualizationData", "Lcom/livewallpaper/core/data/model/MusicVisualizationData;", "getVisualizationData", "refreshMusicState", "", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class MusicTestViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.music.MusicManager musicManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.music.MusicState> musicState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.data.model.MusicVisualizationData> visualizationData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> hasNotificationPermission = null;
    
    @javax.inject.Inject()
    public MusicTestViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicManager musicManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.music.MusicState> getMusicState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.data.model.MusicVisualizationData> getVisualizationData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getHasNotificationPermission() {
        return null;
    }
    
    public final void refreshMusicState() {
    }
}