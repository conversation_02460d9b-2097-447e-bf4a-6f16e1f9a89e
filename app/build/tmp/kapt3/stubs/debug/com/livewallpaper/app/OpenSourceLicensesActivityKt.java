package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0016\u0010\u0004\u001a\u00020\u00012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\b\u00a8\u0006\t"}, d2 = {"LicenseItem", "", "license", "Lcom/livewallpaper/app/OpenSourceLicense;", "OpenSourceLicensesScreen", "onBackClick", "Lkotlin/Function0;", "getOpenSourceLicenses", "", "app_debug"})
public final class OpenSourceLicensesActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void OpenSourceLicensesScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LicenseItem(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.OpenSourceLicense license) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.livewallpaper.app.OpenSourceLicense> getOpenSourceLicenses() {
        return null;
    }
}