package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000Z\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u0007\u001a2\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\nH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u000b\u0010\f\u001a\u001c\u0010\r\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000f0\u000eH\u0007\u001a\u0012\u0010\u0010\u001a\u00020\u00012\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0007\u001a\u0010\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u0015H\u0007\u001a\u0010\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u0018H\u0007\u001a\u0016\u0010\u0019\u001a\u00020\u00012\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00060\u001bH\u0007\u001a \u0010\u001c\u001a\u00020\u00012\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\b\b\u0002\u0010\u001f\u001a\u00020 H\u0007\u001a\u0018\u0010!\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\""}, d2 = {"DirtyRegionStatsCard", "", "stats", "Lcom/livewallpaper/core/performance/DirtyRegionStats;", "MetricItem", "label", "", "value", "unit", "color", "Landroidx/compose/ui/graphics/Color;", "MetricItem-g2O1Hgs", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V", "ObjectPoolStatsCard", "", "", "OptimizedSettingsCard", "settings", "Lcom/livewallpaper/core/performance/OptimizedRenderSettings;", "PerformanceLevelCard", "performanceLevel", "Lcom/livewallpaper/core/performance/PerformanceLevel;", "PerformanceMetricsCard", "metrics", "Lcom/livewallpaper/core/performance/PerformanceMetrics;", "PerformanceRecommendationsCard", "recommendations", "", "PerformanceTestScreen", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/livewallpaper/app/PerformanceTestViewModel;", "SettingRow", "app_debug"})
public final class PerformanceTestActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PerformanceTestScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.PerformanceTestViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PerformanceLevelCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceLevel performanceLevel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PerformanceMetricsCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceMetrics metrics) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void OptimizedSettingsCard(@org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.performance.OptimizedRenderSettings settings) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DirtyRegionStatsCard(@org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.performance.DirtyRegionStats stats) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ObjectPoolStatsCard(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.Integer> stats) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PerformanceRecommendationsCard(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> recommendations) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettingRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
}