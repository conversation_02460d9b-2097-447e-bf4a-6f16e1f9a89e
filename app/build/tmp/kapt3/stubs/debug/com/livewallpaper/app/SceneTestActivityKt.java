package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000X\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u001e\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\u0016\u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u000bH\u0007\u001a \u0010\f\u001a\u00020\u00012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0007\u001a*\u0010\u0010\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001a\u0010\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u0016H\u0002\u001a\u0010\u0010\u0017\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u0019H\u0002\u001a\u0010\u0010\u001a\u001a\u00020\u00132\u0006\u0010\u001b\u001a\u00020\u001cH\u0002\u001a\u0010\u0010\u001d\u001a\u00020\u00132\u0006\u0010\u001e\u001a\u00020\u001fH\u0002\u00a8\u0006 "}, d2 = {"CurrentSceneCard", "", "sceneState", "Lcom/livewallpaper/core/domain/scene/SceneState;", "SceneItem", "scene", "Lcom/livewallpaper/core/data/model/Scene;", "onClick", "Lkotlin/Function0;", "SceneStatsCard", "scenes", "", "SceneTestScreen", "onBackClick", "viewModel", "Lcom/livewallpaper/app/SceneTestViewModel;", "ScenesListCard", "onSceneSelect", "Lkotlin/Function1;", "", "getCategoryDisplayName", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "getSeasonDisplayName", "season", "Lcom/livewallpaper/core/data/model/Season;", "getTimeOfDayDisplayName", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "getWeatherTypeDisplayName", "weatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "app_debug"})
public final class SceneTestActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SceneTestScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.SceneTestViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CurrentSceneCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneState sceneState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ScenesListCard(@org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.Scene> scenes, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSceneSelect) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SceneItem(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SceneStatsCard(@org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.Scene> scenes) {
    }
    
    private static final java.lang.String getCategoryDisplayName(com.livewallpaper.core.data.model.SceneCategory category) {
        return null;
    }
    
    private static final java.lang.String getSeasonDisplayName(com.livewallpaper.core.data.model.Season season) {
        return null;
    }
    
    private static final java.lang.String getTimeOfDayDisplayName(com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    private static final java.lang.String getWeatherTypeDisplayName(com.livewallpaper.core.data.model.WeatherType weatherType) {
        return null;
    }
}