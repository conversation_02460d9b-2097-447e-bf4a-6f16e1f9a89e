package com.livewallpaper.app;

/**
 * 天气测试ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\b\u0010\u0017\u001a\u00020\u0018H\u0002J\u0006\u0010\u0019\u001a\u00020\u0018J\u000e\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001cJ\b\u0010\u001d\u001a\u00020\u0018H\u0002R\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011\u00a8\u0006\u001e"}, d2 = {"Lcom/livewallpaper/app/WeatherTestViewModel;", "Landroidx/lifecycle/ViewModel;", "weatherManager", "Lcom/livewallpaper/core/domain/weather/WeatherManager;", "locationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "(Lcom/livewallpaper/core/domain/weather/WeatherManager;Lcom/livewallpaper/core/domain/location/LocationManager;)V", "_weatherForecast", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/livewallpaper/core/data/model/WeatherForecast;", "_weatherState", "Lcom/livewallpaper/core/domain/weather/WeatherState;", "hasLocationPermission", "Lkotlinx/coroutines/flow/StateFlow;", "", "getHasLocationPermission", "()Lkotlinx/coroutines/flow/StateFlow;", "isApiKeyConfigured", "weatherForecast", "getWeatherForecast", "weatherState", "getWeatherState", "loadWeatherForecast", "", "refreshWeather", "setApiKey", "apiKey", "", "startWeatherStateMonitoring", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class WeatherTestViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.weather.WeatherManager weatherManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.location.LocationManager locationManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.weather.WeatherState> _weatherState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.weather.WeatherState> weatherState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>> _weatherForecast = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>> weatherForecast = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> hasLocationPermission = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isApiKeyConfigured = null;
    
    @javax.inject.Inject()
    public WeatherTestViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherManager weatherManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.weather.WeatherState> getWeatherState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>> getWeatherForecast() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getHasLocationPermission() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isApiKeyConfigured() {
        return null;
    }
    
    private final void startWeatherStateMonitoring() {
    }
    
    private final void loadWeatherForecast() {
    }
    
    public final void refreshWeather() {
    }
    
    public final void setApiKey(@org.jetbrains.annotations.NotNull()
    java.lang.String apiKey) {
    }
}