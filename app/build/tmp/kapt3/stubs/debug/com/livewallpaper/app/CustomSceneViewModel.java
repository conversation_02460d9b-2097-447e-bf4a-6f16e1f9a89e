package com.livewallpaper.app;

/**
 * 自定义场景管理ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u001d\u001a\u00020\u000eJ\u0006\u0010\u001e\u001a\u00020\u001fJ\u0006\u0010 \u001a\u00020\u001fJ\u0016\u0010!\u001a\u00020\u001f2\u0006\u0010\"\u001a\u00020\f2\u0006\u0010#\u001a\u00020\fJ\u000e\u0010$\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020\fJ\u001e\u0010&\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020\f2\u0006\u0010\'\u001a\u00020\f2\u0006\u0010(\u001a\u00020\fJ\u0006\u0010)\u001a\u00020\fJ\u0006\u0010*\u001a\u00020+J\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\f0\tJ\u0006\u0010-\u001a\u00020\u001fJ\u0006\u0010.\u001a\u00020\u001fJ\u000e\u0010/\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020\fJ\u000e\u00100\u001a\u00020\u001f2\u0006\u00101\u001a\u00020\u0011J\u0010\u00102\u001a\u0004\u0018\u00010\f2\u0006\u0010\"\u001a\u00020\fR\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0019\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0015\u00a8\u00063"}, d2 = {"Lcom/livewallpaper/app/CustomSceneViewModel;", "Landroidx/lifecycle/ViewModel;", "customSceneManager", "Lcom/livewallpaper/core/domain/scene/CustomSceneManager;", "sceneManager", "Lcom/livewallpaper/core/domain/scene/SceneManager;", "(Lcom/livewallpaper/core/domain/scene/CustomSceneManager;Lcom/livewallpaper/core/domain/scene/SceneManager;)V", "_customScenes", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/livewallpaper/core/data/model/Scene;", "_errorMessage", "", "_isLoading", "", "_operationInProgress", "_selectedImageUri", "Landroid/net/Uri;", "customScenes", "Lkotlinx/coroutines/flow/StateFlow;", "getCustomScenes", "()Lkotlinx/coroutines/flow/StateFlow;", "errorMessage", "getErrorMessage", "isLoading", "operationInProgress", "getOperationInProgress", "selectedImageUri", "getSelectedImageUri", "canCreateMoreScenes", "clearErrorMessage", "", "clearSelectedImage", "createCustomScene", "name", "description", "deleteCustomScene", "sceneId", "editCustomScene", "newName", "newDescription", "getCreationLimitInfo", "getSceneStats", "Lcom/livewallpaper/app/SceneStats;", "getSuggestedSceneNames", "loadCustomScenes", "refresh", "selectScene", "setSelectedImageUri", "uri", "validateSceneName", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class CustomSceneViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.scene.CustomSceneManager customSceneManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.scene.SceneManager sceneManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.livewallpaper.core.data.model.Scene>> _customScenes = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.data.model.Scene>> customScenes = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<android.net.Uri> _selectedImageUri = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<android.net.Uri> selectedImageUri = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _operationInProgress = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> operationInProgress = null;
    
    @javax.inject.Inject()
    public CustomSceneViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.CustomSceneManager customSceneManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneManager sceneManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.data.model.Scene>> getCustomScenes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<android.net.Uri> getSelectedImageUri() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getOperationInProgress() {
        return null;
    }
    
    /**
     * 加载自定义场景列表
     */
    public final void loadCustomScenes() {
    }
    
    /**
     * 创建自定义场景
     */
    public final void createCustomScene(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description) {
    }
    
    /**
     * 编辑自定义场景
     */
    public final void editCustomScene(@org.jetbrains.annotations.NotNull()
    java.lang.String sceneId, @org.jetbrains.annotations.NotNull()
    java.lang.String newName, @org.jetbrains.annotations.NotNull()
    java.lang.String newDescription) {
    }
    
    /**
     * 删除自定义场景
     */
    public final void deleteCustomScene(@org.jetbrains.annotations.NotNull()
    java.lang.String sceneId) {
    }
    
    /**
     * 选择场景作为当前壁纸
     */
    public final void selectScene(@org.jetbrains.annotations.NotNull()
    java.lang.String sceneId) {
    }
    
    /**
     * 设置选择的图片URI
     */
    public final void setSelectedImageUri(@org.jetbrains.annotations.NotNull()
    android.net.Uri uri) {
    }
    
    /**
     * 清除选择的图片
     */
    public final void clearSelectedImage() {
    }
    
    /**
     * 清除错误消息
     */
    public final void clearErrorMessage() {
    }
    
    /**
     * 刷新场景列表
     */
    public final void refresh() {
    }
    
    /**
     * 获取场景统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.app.SceneStats getSceneStats() {
        return null;
    }
    
    /**
     * 检查是否可以创建更多场景
     */
    public final boolean canCreateMoreScenes() {
        return false;
    }
    
    /**
     * 获取创建场景的限制信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCreationLimitInfo() {
        return null;
    }
    
    /**
     * 验证场景名称
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String validateSceneName(@org.jetbrains.annotations.NotNull()
    java.lang.String name) {
        return null;
    }
    
    /**
     * 获取推荐的场景名称
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSuggestedSceneNames() {
        return null;
    }
}