package com.livewallpaper.app;

/**
 * 场景测试ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\u0015\u001a\u00020\u0016H\u0002J\b\u0010\u0017\u001a\u00020\u0016H\u0002J\u0006\u0010\u0018\u001a\u00020\u0016J\b\u0010\u0019\u001a\u00020\u0016H\u0002J\u000e\u0010\u001a\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u001cR\u001a\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012\u00a8\u0006\u001d"}, d2 = {"Lcom/livewallpaper/app/SceneTestViewModel;", "Landroidx/lifecycle/ViewModel;", "sceneManager", "Lcom/livewallpaper/core/domain/scene/SceneManager;", "sceneRepository", "Lcom/livewallpaper/core/data/repository/SceneRepository;", "sceneInitializer", "Lcom/livewallpaper/core/domain/scene/SceneInitializer;", "(Lcom/livewallpaper/core/domain/scene/SceneManager;Lcom/livewallpaper/core/data/repository/SceneRepository;Lcom/livewallpaper/core/domain/scene/SceneInitializer;)V", "_allScenes", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/livewallpaper/core/data/model/Scene;", "_sceneState", "Lcom/livewallpaper/core/domain/scene/SceneState;", "allScenes", "Lkotlinx/coroutines/flow/StateFlow;", "getAllScenes", "()Lkotlinx/coroutines/flow/StateFlow;", "sceneState", "getSceneState", "initializeScenes", "", "loadAllScenes", "refreshScenes", "startSceneStateMonitoring", "switchToScene", "sceneId", "", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SceneTestViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.scene.SceneManager sceneManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.repository.SceneRepository sceneRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.scene.SceneInitializer sceneInitializer = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.scene.SceneState> _sceneState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.scene.SceneState> sceneState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.livewallpaper.core.data.model.Scene>> _allScenes = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.data.model.Scene>> allScenes = null;
    
    @javax.inject.Inject()
    public SceneTestViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneManager sceneManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SceneRepository sceneRepository, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneInitializer sceneInitializer) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.scene.SceneState> getSceneState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.livewallpaper.core.data.model.Scene>> getAllScenes() {
        return null;
    }
    
    private final void initializeScenes() {
    }
    
    private final void startSceneStateMonitoring() {
    }
    
    private final void loadAllScenes() {
    }
    
    public final void switchToScene(@org.jetbrains.annotations.NotNull()
    java.lang.String sceneId) {
    }
    
    public final void refreshScenes() {
    }
}