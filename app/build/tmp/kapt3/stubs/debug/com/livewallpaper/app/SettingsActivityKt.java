package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000h\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\b\u000b\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\f\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\t\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\n\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a[\u0010\r\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u000e2\u0006\u0010\u000f\u001a\u00020\u00062\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0011\u001a\u0002H\u000e2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u0002H\u000e\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u00a2\u0006\u0002\u0010\u0019\u001a2\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a \u0010\u001d\u001a\u00020\u00012\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u001f2\b\b\u0002\u0010 \u001a\u00020!H\u0007\u001a\u001c\u0010\"\u001a\u00020\u00012\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070$H\u0007\u001af\u0010%\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00062\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00062\u0006\u0010&\u001a\u00020\'2\f\u0010(\u001a\b\u0012\u0004\u0012\u00020\'0)2\b\b\u0002\u0010*\u001a\u00020+2\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u00062\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u001a \u0010.\u001a\u00020\u00012\u0006\u0010/\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\u00062\u0006\u00100\u001a\u00020\u0006H\u0007\u001aB\u00101\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00062\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00062\u0006\u00102\u001a\u00020\u00182\u0012\u00103\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u001a*\u00104\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u00105\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u00106\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u00067"}, d2 = {"AdvancedSettings", "", "settings", "Lcom/livewallpaper/core/data/model/WallpaperSettings;", "onSettingChanged", "Lkotlin/Function2;", "", "", "DisplaySettings", "GeneralSettings", "MusicSettings", "PerformanceSettings", "SceneSettings", "SelectionSetting", "T", "title", "description", "selectedValue", "options", "", "Lcom/livewallpaper/core/data/model/SettingOption;", "onSelectionChange", "Lkotlin/Function1;", "enabled", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/util/List;Lkotlin/jvm/functions/Function1;Z)V", "SettingsCategoryCard", "category", "Lcom/livewallpaper/core/data/model/SettingsCategory;", "SettingsScreen", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/livewallpaper/app/SettingsViewModel;", "SettingsStatsCard", "stats", "", "SliderSetting", "value", "", "valueRange", "Lkotlin/ranges/ClosedFloatingPointRange;", "steps", "", "unit", "onValueChange", "StatItem", "label", "icon", "SwitchSetting", "checked", "onCheckedChange", "ThemeSettings", "TimeSettings", "WeatherSettings", "app_debug"})
public final class SettingsActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SettingsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.SettingsViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettingsStatsCard(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> stats) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatItem(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    java.lang.String icon) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettingsCategoryCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SettingsCategory category, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void GeneralSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SceneSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TimeSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WeatherSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MusicSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PerformanceSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DisplaySettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ThemeSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdvancedSettings(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WallpaperSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, java.lang.Object, kotlin.Unit> onSettingChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SwitchSetting(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.String description, boolean checked, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onCheckedChange, boolean enabled) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SliderSetting(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.String description, float value, @org.jetbrains.annotations.NotNull()
    kotlin.ranges.ClosedFloatingPointRange<java.lang.Float> valueRange, int steps, @org.jetbrains.annotations.Nullable()
    java.lang.String unit, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onValueChange, boolean enabled) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final <T extends java.lang.Object>void SelectionSetting(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.String description, T selectedValue, @org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.SettingOption> options, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super T, kotlin.Unit> onSelectionChange, boolean enabled) {
    }
}