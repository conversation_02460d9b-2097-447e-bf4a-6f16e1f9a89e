package com.livewallpaper.app;

/**
 * 时间测试ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0011\u001a\u00020\u0012J\b\u0010\u0013\u001a\u00020\u0012H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000e\u00a8\u0006\u0014"}, d2 = {"Lcom/livewallpaper/app/TimeTestViewModel;", "Landroidx/lifecycle/ViewModel;", "timeManager", "Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "locationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "(Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;Lcom/livewallpaper/core/domain/location/LocationManager;)V", "_timeState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "hasLocationPermission", "Lkotlinx/coroutines/flow/StateFlow;", "", "getHasLocationPermission", "()Lkotlinx/coroutines/flow/StateFlow;", "timeState", "getTimeState", "refreshTimeState", "", "startTimeStateMonitoring", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class TimeTestViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.time.WallpaperTimeManager timeManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.location.LocationManager locationManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.time.WallpaperTimeState> _timeState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.time.WallpaperTimeState> timeState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> hasLocationPermission = null;
    
    @javax.inject.Inject()
    public TimeTestViewModel(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeManager timeManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.time.WallpaperTimeState> getTimeState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getHasLocationPermission() {
        return null;
    }
    
    private final void startTimeStateMonitoring() {
    }
    
    public final void refreshTimeState() {
    }
}