package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u001e\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0010\u0010\n\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0010\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\fH\u0007\u001a \u0010\r\u001a\u00020\u00012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002\u001a\u0010\u0010\u0015\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002\u001a\u0010\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00a8\u0006\u0019"}, d2 = {"LocationDetailsCard", "", "timeState", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Success;", "PermissionStatusCard", "hasPermission", "", "onRequestPermission", "Lkotlin/Function0;", "SolarEventsCard", "TimeDetailsCard", "TimeStateCard", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "TimeTestScreen", "onBackClick", "viewModel", "Lcom/livewallpaper/app/TimeTestViewModel;", "formatTime", "", "instant", "Lkotlinx/datetime/Instant;", "formatTimestamp", "getPhaseDisplayName", "phase", "Lcom/livewallpaper/core/data/model/TimePhase;", "app_debug"})
public final class TimeTestActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void TimeTestScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.TimeTestViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PermissionStatusCard(boolean hasPermission, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRequestPermission) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TimeStateCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState timeState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TimeDetailsCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState.Success timeState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LocationDetailsCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState.Success timeState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SolarEventsCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState.Success timeState) {
    }
    
    private static final java.lang.String getPhaseDisplayName(com.livewallpaper.core.data.model.TimePhase phase) {
        return null;
    }
    
    private static final java.lang.String formatTimestamp(kotlinx.datetime.Instant instant) {
        return null;
    }
    
    private static final java.lang.String formatTime(kotlinx.datetime.Instant instant) {
        return null;
    }
}