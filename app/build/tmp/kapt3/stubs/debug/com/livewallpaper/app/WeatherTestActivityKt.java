package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000V\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u001a$\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a\u0010\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\tH\u0007\u001a\u0010\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\fH\u0007\u001a\u0016\u0010\r\u001a\u00020\u00012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000fH\u0007\u001a\u0010\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u0012H\u0007\u001a \u0010\u0013\u001a\u00020\u00012\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001a\u0010\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u001aH\u0002\u001a\u0010\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u001aH\u0002\u001a\u0010\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u001d\u001a\u00020\u001eH\u0002\u00a8\u0006\u001f"}, d2 = {"ApiKeyStatusCard", "", "isConfigured", "", "onConfigureApiKey", "Lkotlin/Function1;", "", "ForecastItem", "forecast", "Lcom/livewallpaper/core/data/model/WeatherForecast;", "WeatherDetailsCard", "weather", "Lcom/livewallpaper/core/data/model/Weather;", "WeatherForecastCard", "forecasts", "", "WeatherStateCard", "weatherState", "Lcom/livewallpaper/core/domain/weather/WeatherState;", "WeatherTestScreen", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/livewallpaper/app/WeatherTestViewModel;", "formatDate", "instant", "Lkotlinx/datetime/Instant;", "formatTimestamp", "getWeatherTypeDisplayName", "weatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "app_debug"})
public final class WeatherTestActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void WeatherTestScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.WeatherTestViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ApiKeyStatusCard(boolean isConfigured, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onConfigureApiKey) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WeatherStateCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherState weatherState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WeatherDetailsCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Weather weather) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WeatherForecastCard(@org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.WeatherForecast> forecasts) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ForecastItem(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WeatherForecast forecast) {
    }
    
    private static final java.lang.String getWeatherTypeDisplayName(com.livewallpaper.core.data.model.WeatherType weatherType) {
        return null;
    }
    
    private static final java.lang.String formatTimestamp(kotlinx.datetime.Instant instant) {
        return null;
    }
    
    private static final java.lang.String formatDate(kotlinx.datetime.Instant instant) {
        return null;
    }
}