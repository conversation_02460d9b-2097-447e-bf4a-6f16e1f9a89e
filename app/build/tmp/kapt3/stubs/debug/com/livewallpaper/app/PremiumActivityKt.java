package com.livewallpaper.app;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004H\u0007\u001a\b\u0010\u0005\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0007\u001a \u0010\u0007\u001a\u00020\u00012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a&\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u0006\u0010\u0010\u001a\u00020\u0011H\u0007\u001a\u0016\u0010\u0012\u001a\u00020\u00012\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u00a8\u0006\u0014"}, d2 = {"FAQCard", "", "FeatureRow", "feature", "Lcom/livewallpaper/app/FeatureItem;", "PremiumFeaturesCard", "PremiumIntroCard", "PremiumScreen", "onBackClick", "Lkotlin/Function0;", "viewModel", "Lcom/livewallpaper/app/PremiumViewModel;", "ProductCard", "product", "Lcom/livewallpaper/core/domain/billing/ProductDetails;", "onPurchaseClick", "isLoading", "", "RestorePurchasesCard", "onRestoreClick", "app_debug"})
public final class PremiumActivityKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PremiumScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.PremiumViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PremiumIntroCard() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PremiumFeaturesCard() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProductCard(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.billing.ProductDetails product, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPurchaseClick, boolean isLoading) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RestorePurchasesCard(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRestoreClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FAQCard() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FeatureRow(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.app.FeatureItem feature) {
    }
}