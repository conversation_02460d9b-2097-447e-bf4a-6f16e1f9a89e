package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.livewallpaper.app.MainApplication",
    rootPackage = "com.livewallpaper.app",
    originatingRoot = "com.livewallpaper.app.MainApplication",
    originatingRootPackage = "com.livewallpaper.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MainApplication",
    originatingRootSimpleNames = "MainApplication"
)
public class _com_livewallpaper_app_MainApplication {
}
