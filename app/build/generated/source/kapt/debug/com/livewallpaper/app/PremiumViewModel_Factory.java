// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.domain.billing.BillingManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PremiumViewModel_Factory implements Factory<PremiumViewModel> {
  private final Provider<BillingManager> billingManagerProvider;

  public PremiumViewModel_Factory(Provider<BillingManager> billingManagerProvider) {
    this.billingManagerProvider = billingManagerProvider;
  }

  @Override
  public PremiumViewModel get() {
    return newInstance(billingManagerProvider.get());
  }

  public static PremiumViewModel_Factory create(Provider<BillingManager> billingManagerProvider) {
    return new PremiumViewModel_Factory(billingManagerProvider);
  }

  public static PremiumViewModel newInstance(BillingManager billingManager) {
    return new PremiumViewModel(billingManager);
  }
}
