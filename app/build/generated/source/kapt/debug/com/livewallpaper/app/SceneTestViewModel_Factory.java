// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.data.repository.SceneRepository;
import com.livewallpaper.core.domain.scene.SceneInitializer;
import com.livewallpaper.core.domain.scene.SceneManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneTestViewModel_Factory implements Factory<SceneTestViewModel> {
  private final Provider<SceneManager> sceneManagerProvider;

  private final Provider<SceneRepository> sceneRepositoryProvider;

  private final Provider<SceneInitializer> sceneInitializerProvider;

  public SceneTestViewModel_Factory(Provider<SceneManager> sceneManagerProvider,
      Provider<SceneRepository> sceneRepositoryProvider,
      Provider<SceneInitializer> sceneInitializerProvider) {
    this.sceneManagerProvider = sceneManagerProvider;
    this.sceneRepositoryProvider = sceneRepositoryProvider;
    this.sceneInitializerProvider = sceneInitializerProvider;
  }

  @Override
  public SceneTestViewModel get() {
    return newInstance(sceneManagerProvider.get(), sceneRepositoryProvider.get(), sceneInitializerProvider.get());
  }

  public static SceneTestViewModel_Factory create(Provider<SceneManager> sceneManagerProvider,
      Provider<SceneRepository> sceneRepositoryProvider,
      Provider<SceneInitializer> sceneInitializerProvider) {
    return new SceneTestViewModel_Factory(sceneManagerProvider, sceneRepositoryProvider, sceneInitializerProvider);
  }

  public static SceneTestViewModel newInstance(SceneManager sceneManager,
      SceneRepository sceneRepository, SceneInitializer sceneInitializer) {
    return new SceneTestViewModel(sceneManager, sceneRepository, sceneInitializer);
  }
}
