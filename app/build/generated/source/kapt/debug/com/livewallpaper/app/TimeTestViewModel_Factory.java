// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.domain.location.LocationManager;
import com.livewallpaper.core.domain.time.WallpaperTimeManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class TimeTestViewModel_Factory implements Factory<TimeTestViewModel> {
  private final Provider<WallpaperTimeManager> timeManagerProvider;

  private final Provider<LocationManager> locationManagerProvider;

  public TimeTestViewModel_Factory(Provider<WallpaperTimeManager> timeManagerProvider,
      Provider<LocationManager> locationManagerProvider) {
    this.timeManagerProvider = timeManagerProvider;
    this.locationManagerProvider = locationManagerProvider;
  }

  @Override
  public TimeTestViewModel get() {
    return newInstance(timeManagerProvider.get(), locationManagerProvider.get());
  }

  public static TimeTestViewModel_Factory create(Provider<WallpaperTimeManager> timeManagerProvider,
      Provider<LocationManager> locationManagerProvider) {
    return new TimeTestViewModel_Factory(timeManagerProvider, locationManagerProvider);
  }

  public static TimeTestViewModel newInstance(WallpaperTimeManager timeManager,
      LocationManager locationManager) {
    return new TimeTestViewModel(timeManager, locationManager);
  }
}
