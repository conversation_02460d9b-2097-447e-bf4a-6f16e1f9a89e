package com.livewallpaper.app;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = AboutActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface AboutActivity_GeneratedInjector {
  void injectAboutActivity(AboutActivity aboutActivity);
}
