// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.domain.location.LocationManager;
import com.livewallpaper.core.domain.weather.WeatherManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherTestViewModel_Factory implements Factory<WeatherTestViewModel> {
  private final Provider<WeatherManager> weatherManagerProvider;

  private final Provider<LocationManager> locationManagerProvider;

  public WeatherTestViewModel_Factory(Provider<WeatherManager> weatherManagerProvider,
      Provider<LocationManager> locationManagerProvider) {
    this.weatherManagerProvider = weatherManagerProvider;
    this.locationManagerProvider = locationManagerProvider;
  }

  @Override
  public WeatherTestViewModel get() {
    return newInstance(weatherManagerProvider.get(), locationManagerProvider.get());
  }

  public static WeatherTestViewModel_Factory create(Provider<WeatherManager> weatherManagerProvider,
      Provider<LocationManager> locationManagerProvider) {
    return new WeatherTestViewModel_Factory(weatherManagerProvider, locationManagerProvider);
  }

  public static WeatherTestViewModel newInstance(WeatherManager weatherManager,
      LocationManager locationManager) {
    return new WeatherTestViewModel(weatherManager, locationManager);
  }
}
