// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.internal.lifecycle.HiltViewModelMap.KeySet")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PremiumViewModel_HiltModules_KeyModule_ProvideFactory implements Factory<Boolean> {
  @Override
  public Boolean get() {
    return provide();
  }

  public static PremiumViewModel_HiltModules_KeyModule_ProvideFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static boolean provide() {
    return PremiumViewModel_HiltModules.KeyModule.provide();
  }

  private static final class InstanceHolder {
    private static final PremiumViewModel_HiltModules_KeyModule_ProvideFactory INSTANCE = new PremiumViewModel_HiltModules_KeyModule_ProvideFactory();
  }
}
