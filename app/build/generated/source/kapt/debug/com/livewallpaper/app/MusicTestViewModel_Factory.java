// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.domain.music.MusicManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicTestViewModel_Factory implements Factory<MusicTestViewModel> {
  private final Provider<MusicManager> musicManagerProvider;

  public MusicTestViewModel_Factory(Provider<MusicManager> musicManagerProvider) {
    this.musicManagerProvider = musicManagerProvider;
  }

  @Override
  public MusicTestViewModel get() {
    return newInstance(musicManagerProvider.get());
  }

  public static MusicTestViewModel_Factory create(Provider<MusicManager> musicManagerProvider) {
    return new MusicTestViewModel_Factory(musicManagerProvider);
  }

  public static MusicTestViewModel newInstance(MusicManager musicManager) {
    return new MusicTestViewModel(musicManager);
  }
}
