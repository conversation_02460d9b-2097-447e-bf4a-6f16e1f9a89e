package com.livewallpaper.app;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = MainApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface MainApplication_GeneratedInjector {
  void injectMainApplication(MainApplication mainApplication);
}
