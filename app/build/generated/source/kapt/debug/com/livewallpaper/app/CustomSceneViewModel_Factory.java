// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.domain.scene.CustomSceneManager;
import com.livewallpaper.core.domain.scene.SceneManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class CustomSceneViewModel_Factory implements Factory<CustomSceneViewModel> {
  private final Provider<CustomSceneManager> customSceneManagerProvider;

  private final Provider<SceneManager> sceneManagerProvider;

  public CustomSceneViewModel_Factory(Provider<CustomSceneManager> customSceneManagerProvider,
      Provider<SceneManager> sceneManagerProvider) {
    this.customSceneManagerProvider = customSceneManagerProvider;
    this.sceneManagerProvider = sceneManagerProvider;
  }

  @Override
  public CustomSceneViewModel get() {
    return newInstance(customSceneManagerProvider.get(), sceneManagerProvider.get());
  }

  public static CustomSceneViewModel_Factory create(
      Provider<CustomSceneManager> customSceneManagerProvider,
      Provider<SceneManager> sceneManagerProvider) {
    return new CustomSceneViewModel_Factory(customSceneManagerProvider, sceneManagerProvider);
  }

  public static CustomSceneViewModel newInstance(CustomSceneManager customSceneManager,
      SceneManager sceneManager) {
    return new CustomSceneViewModel(customSceneManager, sceneManager);
  }
}
