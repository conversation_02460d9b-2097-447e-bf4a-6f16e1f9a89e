package com.livewallpaper.app;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = CustomSceneActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface CustomSceneActivity_GeneratedInjector {
  void injectCustomSceneActivity(CustomSceneActivity customSceneActivity);
}
