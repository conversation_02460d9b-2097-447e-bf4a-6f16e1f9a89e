// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import com.livewallpaper.core.performance.DirtyRegionManager;
import com.livewallpaper.core.performance.ObjectPoolManager;
import com.livewallpaper.core.performance.PerformanceMonitor;
import com.livewallpaper.core.performance.PerformanceOptimizer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PerformanceTestViewModel_Factory implements Factory<PerformanceTestViewModel> {
  private final Provider<PerformanceMonitor> performanceMonitorProvider;

  private final Provider<PerformanceOptimizer> performanceOptimizerProvider;

  private final Provider<DirtyRegionManager> dirtyRegionManagerProvider;

  private final Provider<ObjectPoolManager> objectPoolManagerProvider;

  public PerformanceTestViewModel_Factory(Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<PerformanceOptimizer> performanceOptimizerProvider,
      Provider<DirtyRegionManager> dirtyRegionManagerProvider,
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    this.performanceMonitorProvider = performanceMonitorProvider;
    this.performanceOptimizerProvider = performanceOptimizerProvider;
    this.dirtyRegionManagerProvider = dirtyRegionManagerProvider;
    this.objectPoolManagerProvider = objectPoolManagerProvider;
  }

  @Override
  public PerformanceTestViewModel get() {
    return newInstance(performanceMonitorProvider.get(), performanceOptimizerProvider.get(), dirtyRegionManagerProvider.get(), objectPoolManagerProvider.get());
  }

  public static PerformanceTestViewModel_Factory create(
      Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<PerformanceOptimizer> performanceOptimizerProvider,
      Provider<DirtyRegionManager> dirtyRegionManagerProvider,
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    return new PerformanceTestViewModel_Factory(performanceMonitorProvider, performanceOptimizerProvider, dirtyRegionManagerProvider, objectPoolManagerProvider);
  }

  public static PerformanceTestViewModel newInstance(PerformanceMonitor performanceMonitor,
      PerformanceOptimizer performanceOptimizer, DirtyRegionManager dirtyRegionManager,
      ObjectPoolManager objectPoolManager) {
    return new PerformanceTestViewModel(performanceMonitor, performanceOptimizer, dirtyRegionManager, objectPoolManager);
  }
}
