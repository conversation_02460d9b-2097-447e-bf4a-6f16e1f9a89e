package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.livewallpaper.app.PerformanceTestViewModel_HiltModules.KeyModule"
)
public class _com_livewallpaper_app_PerformanceTestViewModel_HiltModules_KeyModule {
}
