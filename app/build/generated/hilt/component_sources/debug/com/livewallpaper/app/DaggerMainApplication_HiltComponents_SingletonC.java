// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.app;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.livewallpaper.core.data.database.WallpaperDatabase;
import com.livewallpaper.core.data.database.dao.SceneDao;
import com.livewallpaper.core.data.database.dao.WeatherDao;
import com.livewallpaper.core.data.network.WeatherApiService;
import com.livewallpaper.core.data.preferences.SettingsDataStore;
import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import com.livewallpaper.core.data.repository.SceneRepository;
import com.livewallpaper.core.data.repository.SettingsRepository;
import com.livewallpaper.core.data.repository.WeatherRepository;
import com.livewallpaper.core.di.DatabaseModule_ProvideSceneDaoFactory;
import com.livewallpaper.core.di.DatabaseModule_ProvideWallpaperDatabaseFactory;
import com.livewallpaper.core.di.DatabaseModule_ProvideWeatherDaoFactory;
import com.livewallpaper.core.di.MusicModule_ProvideMusicCardRendererFactory;
import com.livewallpaper.core.di.MusicModule_ProvideMusicDataBroadcasterFactory;
import com.livewallpaper.core.di.MusicModule_ProvideMusicManagerFactory;
import com.livewallpaper.core.di.NetworkModule_ProvideOkHttpClientFactory;
import com.livewallpaper.core.di.NetworkModule_ProvideWeatherApiServiceFactory;
import com.livewallpaper.core.di.NetworkModule_ProvideWeatherRetrofitFactory;
import com.livewallpaper.core.di.PerformanceModule_ProvideDirtyRegionManagerFactory;
import com.livewallpaper.core.di.PerformanceModule_ProvideObjectPoolManagerFactory;
import com.livewallpaper.core.di.PerformanceModule_ProvidePerformanceMonitorFactory;
import com.livewallpaper.core.di.PerformanceModule_ProvidePerformanceOptimizerFactory;
import com.livewallpaper.core.di.SceneModule_ProvideSceneInitializerFactory;
import com.livewallpaper.core.di.SceneModule_ProvideSceneLoaderFactory;
import com.livewallpaper.core.di.SceneModule_ProvideSceneManagerFactory;
import com.livewallpaper.core.di.SceneModule_ProvideSceneRendererFactory;
import com.livewallpaper.core.di.SettingsModule_ProvideSettingsDataStoreFactory;
import com.livewallpaper.core.di.SettingsModule_ProvideSettingsRepositoryFactory;
import com.livewallpaper.core.di.TimeModule_ProvideLocationManagerFactory;
import com.livewallpaper.core.di.TimeModule_ProvideWallpaperPreferencesFactory;
import com.livewallpaper.core.di.TimeModule_ProvideWallpaperTimeManagerFactory;
import com.livewallpaper.core.di.WeatherModule_ProvideWeatherEffectRendererFactory;
import com.livewallpaper.core.di.WeatherModule_ProvideWeatherManagerFactory;
import com.livewallpaper.core.di.WeatherModule_ProvideWeatherRepositoryFactory;
import com.livewallpaper.core.domain.location.LocationManager;
import com.livewallpaper.core.domain.music.MusicCardRenderer;
import com.livewallpaper.core.domain.music.MusicManager;
import com.livewallpaper.core.domain.scene.SceneInitializer;
import com.livewallpaper.core.domain.scene.SceneLoader;
import com.livewallpaper.core.domain.scene.SceneManager;
import com.livewallpaper.core.domain.scene.SceneRenderer;
import com.livewallpaper.core.domain.time.WallpaperTimeManager;
import com.livewallpaper.core.domain.weather.WeatherEffectRenderer;
import com.livewallpaper.core.domain.weather.WeatherManager;
import com.livewallpaper.core.performance.DirtyRegionManager;
import com.livewallpaper.core.performance.ObjectPoolManager;
import com.livewallpaper.core.performance.PerformanceMonitor;
import com.livewallpaper.core.performance.PerformanceOptimizer;
import com.livewallpaper.core.service.MusicDataBroadcaster;
import com.livewallpaper.core.service.MusicNotificationListenerService;
import com.livewallpaper.core.service.MusicNotificationListenerService_MembersInjector;
import com.livewallpaper.features.wallpaper.LiveWallpaperService;
import com.livewallpaper.features.wallpaper.LiveWallpaperService_MembersInjector;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DaggerMainApplication_HiltComponents_SingletonC {
  private DaggerMainApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public MainApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements MainApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements MainApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements MainApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements MainApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements MainApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements MainApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements MainApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public MainApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends MainApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends MainApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends MainApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends MainApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectAboutActivity(AboutActivity arg0) {
    }

    @Override
    public void injectMainActivity(MainActivity arg0) {
    }

    @Override
    public void injectMusicTestActivity(MusicTestActivity arg0) {
    }

    @Override
    public void injectOpenSourceLicensesActivity(OpenSourceLicensesActivity arg0) {
    }

    @Override
    public void injectPerformanceTestActivity(PerformanceTestActivity arg0) {
    }

    @Override
    public void injectPrivacyPolicyActivity(PrivacyPolicyActivity arg0) {
    }

    @Override
    public void injectSceneTestActivity(SceneTestActivity arg0) {
    }

    @Override
    public void injectSettingsActivity(SettingsActivity arg0) {
    }

    @Override
    public void injectTermsOfServiceActivity(TermsOfServiceActivity arg0) {
    }

    @Override
    public void injectTimeTestActivity(TimeTestActivity arg0) {
    }

    @Override
    public void injectWallpaperPreviewActivity(WallpaperPreviewActivity arg0) {
    }

    @Override
    public void injectWeatherTestActivity(WeatherTestActivity arg0) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(MapBuilder.<String, Boolean>newMapBuilder(6).put(LazyClassKeyProvider.com_livewallpaper_app_MusicTestViewModel, MusicTestViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_livewallpaper_app_PerformanceTestViewModel, PerformanceTestViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_livewallpaper_app_SceneTestViewModel, SceneTestViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_livewallpaper_app_SettingsViewModel, SettingsViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_livewallpaper_app_TimeTestViewModel, TimeTestViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_livewallpaper_app_WeatherTestViewModel, WeatherTestViewModel_HiltModules.KeyModule.provide()).build());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_livewallpaper_app_TimeTestViewModel = "com.livewallpaper.app.TimeTestViewModel";

      static String com_livewallpaper_app_SettingsViewModel = "com.livewallpaper.app.SettingsViewModel";

      static String com_livewallpaper_app_PerformanceTestViewModel = "com.livewallpaper.app.PerformanceTestViewModel";

      static String com_livewallpaper_app_MusicTestViewModel = "com.livewallpaper.app.MusicTestViewModel";

      static String com_livewallpaper_app_SceneTestViewModel = "com.livewallpaper.app.SceneTestViewModel";

      static String com_livewallpaper_app_WeatherTestViewModel = "com.livewallpaper.app.WeatherTestViewModel";

      @KeepFieldType
      TimeTestViewModel com_livewallpaper_app_TimeTestViewModel2;

      @KeepFieldType
      SettingsViewModel com_livewallpaper_app_SettingsViewModel2;

      @KeepFieldType
      PerformanceTestViewModel com_livewallpaper_app_PerformanceTestViewModel2;

      @KeepFieldType
      MusicTestViewModel com_livewallpaper_app_MusicTestViewModel2;

      @KeepFieldType
      SceneTestViewModel com_livewallpaper_app_SceneTestViewModel2;

      @KeepFieldType
      WeatherTestViewModel com_livewallpaper_app_WeatherTestViewModel2;
    }
  }

  private static final class ViewModelCImpl extends MainApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<MusicTestViewModel> musicTestViewModelProvider;

    private Provider<PerformanceTestViewModel> performanceTestViewModelProvider;

    private Provider<SceneTestViewModel> sceneTestViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private Provider<TimeTestViewModel> timeTestViewModelProvider;

    private Provider<WeatherTestViewModel> weatherTestViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.musicTestViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.performanceTestViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.sceneTestViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.timeTestViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.weatherTestViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(MapBuilder.<String, javax.inject.Provider<ViewModel>>newMapBuilder(6).put(LazyClassKeyProvider.com_livewallpaper_app_MusicTestViewModel, ((Provider) musicTestViewModelProvider)).put(LazyClassKeyProvider.com_livewallpaper_app_PerformanceTestViewModel, ((Provider) performanceTestViewModelProvider)).put(LazyClassKeyProvider.com_livewallpaper_app_SceneTestViewModel, ((Provider) sceneTestViewModelProvider)).put(LazyClassKeyProvider.com_livewallpaper_app_SettingsViewModel, ((Provider) settingsViewModelProvider)).put(LazyClassKeyProvider.com_livewallpaper_app_TimeTestViewModel, ((Provider) timeTestViewModelProvider)).put(LazyClassKeyProvider.com_livewallpaper_app_WeatherTestViewModel, ((Provider) weatherTestViewModelProvider)).build());
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return Collections.<Class<?>, Object>emptyMap();
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_livewallpaper_app_TimeTestViewModel = "com.livewallpaper.app.TimeTestViewModel";

      static String com_livewallpaper_app_MusicTestViewModel = "com.livewallpaper.app.MusicTestViewModel";

      static String com_livewallpaper_app_PerformanceTestViewModel = "com.livewallpaper.app.PerformanceTestViewModel";

      static String com_livewallpaper_app_WeatherTestViewModel = "com.livewallpaper.app.WeatherTestViewModel";

      static String com_livewallpaper_app_SceneTestViewModel = "com.livewallpaper.app.SceneTestViewModel";

      static String com_livewallpaper_app_SettingsViewModel = "com.livewallpaper.app.SettingsViewModel";

      @KeepFieldType
      TimeTestViewModel com_livewallpaper_app_TimeTestViewModel2;

      @KeepFieldType
      MusicTestViewModel com_livewallpaper_app_MusicTestViewModel2;

      @KeepFieldType
      PerformanceTestViewModel com_livewallpaper_app_PerformanceTestViewModel2;

      @KeepFieldType
      WeatherTestViewModel com_livewallpaper_app_WeatherTestViewModel2;

      @KeepFieldType
      SceneTestViewModel com_livewallpaper_app_SceneTestViewModel2;

      @KeepFieldType
      SettingsViewModel com_livewallpaper_app_SettingsViewModel2;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.livewallpaper.app.MusicTestViewModel 
          return (T) new MusicTestViewModel(singletonCImpl.provideMusicManagerProvider.get());

          case 1: // com.livewallpaper.app.PerformanceTestViewModel 
          return (T) new PerformanceTestViewModel(singletonCImpl.providePerformanceMonitorProvider.get(), singletonCImpl.providePerformanceOptimizerProvider.get(), singletonCImpl.provideDirtyRegionManagerProvider.get(), singletonCImpl.provideObjectPoolManagerProvider.get());

          case 2: // com.livewallpaper.app.SceneTestViewModel 
          return (T) new SceneTestViewModel(singletonCImpl.provideSceneManagerProvider.get(), singletonCImpl.sceneRepositoryProvider.get(), singletonCImpl.provideSceneInitializerProvider.get());

          case 3: // com.livewallpaper.app.SettingsViewModel 
          return (T) new SettingsViewModel(singletonCImpl.provideSettingsRepositoryProvider.get());

          case 4: // com.livewallpaper.app.TimeTestViewModel 
          return (T) new TimeTestViewModel(singletonCImpl.provideWallpaperTimeManagerProvider.get(), singletonCImpl.provideLocationManagerProvider.get());

          case 5: // com.livewallpaper.app.WeatherTestViewModel 
          return (T) new WeatherTestViewModel(singletonCImpl.provideWeatherManagerProvider.get(), singletonCImpl.provideLocationManagerProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends MainApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends MainApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectMusicNotificationListenerService(
        MusicNotificationListenerService musicNotificationListenerService) {
      injectMusicNotificationListenerService2(musicNotificationListenerService);
    }

    @Override
    public void injectLiveWallpaperService(LiveWallpaperService arg0) {
      injectLiveWallpaperService2(arg0);
    }

    private MusicNotificationListenerService injectMusicNotificationListenerService2(
        MusicNotificationListenerService instance) {
      MusicNotificationListenerService_MembersInjector.injectMusicDataBroadcaster(instance, singletonCImpl.provideMusicDataBroadcasterProvider.get());
      return instance;
    }

    private LiveWallpaperService injectLiveWallpaperService2(LiveWallpaperService instance) {
      LiveWallpaperService_MembersInjector.injectTimeManager(instance, singletonCImpl.provideWallpaperTimeManagerProvider.get());
      LiveWallpaperService_MembersInjector.injectSceneManager(instance, singletonCImpl.provideSceneManagerProvider.get());
      LiveWallpaperService_MembersInjector.injectSceneRenderer(instance, singletonCImpl.provideSceneRendererProvider.get());
      LiveWallpaperService_MembersInjector.injectSceneInitializer(instance, singletonCImpl.provideSceneInitializerProvider.get());
      LiveWallpaperService_MembersInjector.injectWeatherManager(instance, singletonCImpl.provideWeatherManagerProvider.get());
      LiveWallpaperService_MembersInjector.injectWeatherEffectRenderer(instance, singletonCImpl.provideWeatherEffectRendererProvider.get());
      LiveWallpaperService_MembersInjector.injectMusicManager(instance, singletonCImpl.provideMusicManagerProvider.get());
      LiveWallpaperService_MembersInjector.injectMusicCardRenderer(instance, singletonCImpl.provideMusicCardRendererProvider.get());
      LiveWallpaperService_MembersInjector.injectSettingsRepository(instance, singletonCImpl.provideSettingsRepositoryProvider.get());
      LiveWallpaperService_MembersInjector.injectPerformanceMonitor(instance, singletonCImpl.providePerformanceMonitorProvider.get());
      LiveWallpaperService_MembersInjector.injectPerformanceOptimizer(instance, singletonCImpl.providePerformanceOptimizerProvider.get());
      LiveWallpaperService_MembersInjector.injectDirtyRegionManager(instance, singletonCImpl.provideDirtyRegionManagerProvider.get());
      LiveWallpaperService_MembersInjector.injectObjectPoolManager(instance, singletonCImpl.provideObjectPoolManagerProvider.get());
      return instance;
    }
  }

  private static final class SingletonCImpl extends MainApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<MusicDataBroadcaster> provideMusicDataBroadcasterProvider;

    private Provider<MusicManager> provideMusicManagerProvider;

    private Provider<PerformanceMonitor> providePerformanceMonitorProvider;

    private Provider<SettingsDataStore> provideSettingsDataStoreProvider;

    private Provider<SettingsRepository> provideSettingsRepositoryProvider;

    private Provider<PerformanceOptimizer> providePerformanceOptimizerProvider;

    private Provider<ObjectPoolManager> provideObjectPoolManagerProvider;

    private Provider<DirtyRegionManager> provideDirtyRegionManagerProvider;

    private Provider<WallpaperDatabase> provideWallpaperDatabaseProvider;

    private Provider<SceneRepository> sceneRepositoryProvider;

    private Provider<SceneLoader> provideSceneLoaderProvider;

    private Provider<WallpaperPreferences> provideWallpaperPreferencesProvider;

    private Provider<LocationManager> provideLocationManagerProvider;

    private Provider<WallpaperTimeManager> provideWallpaperTimeManagerProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Retrofit> provideWeatherRetrofitProvider;

    private Provider<WeatherApiService> provideWeatherApiServiceProvider;

    private Provider<WeatherRepository> provideWeatherRepositoryProvider;

    private Provider<WeatherManager> provideWeatherManagerProvider;

    private Provider<SceneManager> provideSceneManagerProvider;

    private Provider<SceneInitializer> provideSceneInitializerProvider;

    private Provider<SceneRenderer> provideSceneRendererProvider;

    private Provider<WeatherEffectRenderer> provideWeatherEffectRendererProvider;

    private Provider<MusicCardRenderer> provideMusicCardRendererProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private SceneDao sceneDao() {
      return DatabaseModule_ProvideSceneDaoFactory.provideSceneDao(provideWallpaperDatabaseProvider.get());
    }

    private WeatherDao weatherDao() {
      return DatabaseModule_ProvideWeatherDaoFactory.provideWeatherDao(provideWallpaperDatabaseProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideMusicDataBroadcasterProvider = DoubleCheck.provider(new SwitchingProvider<MusicDataBroadcaster>(singletonCImpl, 1));
      this.provideMusicManagerProvider = DoubleCheck.provider(new SwitchingProvider<MusicManager>(singletonCImpl, 0));
      this.providePerformanceMonitorProvider = DoubleCheck.provider(new SwitchingProvider<PerformanceMonitor>(singletonCImpl, 2));
      this.provideSettingsDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<SettingsDataStore>(singletonCImpl, 5));
      this.provideSettingsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<SettingsRepository>(singletonCImpl, 4));
      this.providePerformanceOptimizerProvider = DoubleCheck.provider(new SwitchingProvider<PerformanceOptimizer>(singletonCImpl, 3));
      this.provideObjectPoolManagerProvider = DoubleCheck.provider(new SwitchingProvider<ObjectPoolManager>(singletonCImpl, 7));
      this.provideDirtyRegionManagerProvider = DoubleCheck.provider(new SwitchingProvider<DirtyRegionManager>(singletonCImpl, 6));
      this.provideWallpaperDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<WallpaperDatabase>(singletonCImpl, 10));
      this.sceneRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<SceneRepository>(singletonCImpl, 9));
      this.provideSceneLoaderProvider = DoubleCheck.provider(new SwitchingProvider<SceneLoader>(singletonCImpl, 11));
      this.provideWallpaperPreferencesProvider = DoubleCheck.provider(new SwitchingProvider<WallpaperPreferences>(singletonCImpl, 14));
      this.provideLocationManagerProvider = DoubleCheck.provider(new SwitchingProvider<LocationManager>(singletonCImpl, 13));
      this.provideWallpaperTimeManagerProvider = DoubleCheck.provider(new SwitchingProvider<WallpaperTimeManager>(singletonCImpl, 12));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 19));
      this.provideWeatherRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 18));
      this.provideWeatherApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<WeatherApiService>(singletonCImpl, 17));
      this.provideWeatherRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<WeatherRepository>(singletonCImpl, 16));
      this.provideWeatherManagerProvider = DoubleCheck.provider(new SwitchingProvider<WeatherManager>(singletonCImpl, 15));
      this.provideSceneManagerProvider = DoubleCheck.provider(new SwitchingProvider<SceneManager>(singletonCImpl, 8));
      this.provideSceneInitializerProvider = DoubleCheck.provider(new SwitchingProvider<SceneInitializer>(singletonCImpl, 20));
      this.provideSceneRendererProvider = DoubleCheck.provider(new SwitchingProvider<SceneRenderer>(singletonCImpl, 21));
      this.provideWeatherEffectRendererProvider = DoubleCheck.provider(new SwitchingProvider<WeatherEffectRenderer>(singletonCImpl, 22));
      this.provideMusicCardRendererProvider = DoubleCheck.provider(new SwitchingProvider<MusicCardRenderer>(singletonCImpl, 23));
    }

    @Override
    public void injectMainApplication(MainApplication arg0) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.livewallpaper.core.domain.music.MusicManager 
          return (T) MusicModule_ProvideMusicManagerFactory.provideMusicManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideMusicDataBroadcasterProvider.get());

          case 1: // com.livewallpaper.core.service.MusicDataBroadcaster 
          return (T) MusicModule_ProvideMusicDataBroadcasterFactory.provideMusicDataBroadcaster();

          case 2: // com.livewallpaper.core.performance.PerformanceMonitor 
          return (T) PerformanceModule_ProvidePerformanceMonitorFactory.providePerformanceMonitor(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 3: // com.livewallpaper.core.performance.PerformanceOptimizer 
          return (T) PerformanceModule_ProvidePerformanceOptimizerFactory.providePerformanceOptimizer(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.providePerformanceMonitorProvider.get(), singletonCImpl.provideSettingsRepositoryProvider.get());

          case 4: // com.livewallpaper.core.data.repository.SettingsRepository 
          return (T) SettingsModule_ProvideSettingsRepositoryFactory.provideSettingsRepository(singletonCImpl.provideSettingsDataStoreProvider.get());

          case 5: // com.livewallpaper.core.data.preferences.SettingsDataStore 
          return (T) SettingsModule_ProvideSettingsDataStoreFactory.provideSettingsDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 6: // com.livewallpaper.core.performance.DirtyRegionManager 
          return (T) PerformanceModule_ProvideDirtyRegionManagerFactory.provideDirtyRegionManager(singletonCImpl.provideObjectPoolManagerProvider.get());

          case 7: // com.livewallpaper.core.performance.ObjectPoolManager 
          return (T) PerformanceModule_ProvideObjectPoolManagerFactory.provideObjectPoolManager();

          case 8: // com.livewallpaper.core.domain.scene.SceneManager 
          return (T) SceneModule_ProvideSceneManagerFactory.provideSceneManager(singletonCImpl.sceneRepositoryProvider.get(), singletonCImpl.provideSceneLoaderProvider.get(), singletonCImpl.provideWallpaperTimeManagerProvider.get(), singletonCImpl.provideWeatherManagerProvider.get(), singletonCImpl.provideWallpaperPreferencesProvider.get());

          case 9: // com.livewallpaper.core.data.repository.SceneRepository 
          return (T) new SceneRepository(singletonCImpl.sceneDao());

          case 10: // com.livewallpaper.core.data.database.WallpaperDatabase 
          return (T) DatabaseModule_ProvideWallpaperDatabaseFactory.provideWallpaperDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.livewallpaper.core.domain.scene.SceneLoader 
          return (T) SceneModule_ProvideSceneLoaderFactory.provideSceneLoader(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 12: // com.livewallpaper.core.domain.time.WallpaperTimeManager 
          return (T) TimeModule_ProvideWallpaperTimeManagerFactory.provideWallpaperTimeManager(singletonCImpl.provideLocationManagerProvider.get());

          case 13: // com.livewallpaper.core.domain.location.LocationManager 
          return (T) TimeModule_ProvideLocationManagerFactory.provideLocationManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideWallpaperPreferencesProvider.get());

          case 14: // com.livewallpaper.core.data.preferences.WallpaperPreferences 
          return (T) TimeModule_ProvideWallpaperPreferencesFactory.provideWallpaperPreferences(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 15: // com.livewallpaper.core.domain.weather.WeatherManager 
          return (T) WeatherModule_ProvideWeatherManagerFactory.provideWeatherManager(singletonCImpl.provideWeatherRepositoryProvider.get(), singletonCImpl.provideLocationManagerProvider.get());

          case 16: // com.livewallpaper.core.data.repository.WeatherRepository 
          return (T) WeatherModule_ProvideWeatherRepositoryFactory.provideWeatherRepository(singletonCImpl.provideWeatherApiServiceProvider.get(), singletonCImpl.weatherDao(), singletonCImpl.provideWallpaperPreferencesProvider.get());

          case 17: // com.livewallpaper.core.data.network.WeatherApiService 
          return (T) NetworkModule_ProvideWeatherApiServiceFactory.provideWeatherApiService(singletonCImpl.provideWeatherRetrofitProvider.get());

          case 18: // @com.livewallpaper.core.di.NetworkModule.WeatherRetrofit retrofit2.Retrofit 
          return (T) NetworkModule_ProvideWeatherRetrofitFactory.provideWeatherRetrofit(singletonCImpl.provideOkHttpClientProvider.get());

          case 19: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient();

          case 20: // com.livewallpaper.core.domain.scene.SceneInitializer 
          return (T) SceneModule_ProvideSceneInitializerFactory.provideSceneInitializer(singletonCImpl.sceneRepositoryProvider.get());

          case 21: // com.livewallpaper.core.domain.scene.SceneRenderer 
          return (T) SceneModule_ProvideSceneRendererFactory.provideSceneRenderer();

          case 22: // com.livewallpaper.core.domain.weather.WeatherEffectRenderer 
          return (T) WeatherModule_ProvideWeatherEffectRendererFactory.provideWeatherEffectRenderer();

          case 23: // com.livewallpaper.core.domain.music.MusicCardRenderer 
          return (T) MusicModule_ProvideMusicCardRendererFactory.provideMusicCardRenderer();

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
