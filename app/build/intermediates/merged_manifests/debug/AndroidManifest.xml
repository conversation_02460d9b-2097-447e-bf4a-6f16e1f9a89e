<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.livewallpaper.app.debug"
    android:versionCode="1"
    android:versionName="1.0.0-debug" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- 读取外部存储权限（用于自定义壁纸） -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- 通知监听权限（用于音乐可视化） -->
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />

    <!-- 壁纸权限 -->
    <uses-permission android:name="android.permission.BIND_WALLPAPER" />

    <permission
        android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.livewallpaper.app.MainApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LiveWallpaper" >

        <!-- 主Activity -->
        <activity
            android:name="com.livewallpaper.app.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.LiveWallpaper" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 壁纸预览Activity -->
        <activity
            android:name="com.livewallpaper.app.WallpaperPreviewActivity"
            android:exported="true"
            android:label="@string/wallpaper_preview"
            android:theme="@style/Theme.LiveWallpaper" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>

        <!-- 时间系统测试Activity -->
        <activity
            android:name="com.livewallpaper.app.TimeTestActivity"
            android:exported="false"
            android:label="时间系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 场景系统测试Activity -->
        <activity
            android:name="com.livewallpaper.app.SceneTestActivity"
            android:exported="false"
            android:label="场景系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 天气系统测试Activity -->
        <activity
            android:name="com.livewallpaper.app.WeatherTestActivity"
            android:exported="false"
            android:label="天气系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 音乐系统测试Activity -->
        <activity
            android:name="com.livewallpaper.app.MusicTestActivity"
            android:exported="false"
            android:label="音乐系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 设置Activity -->
        <activity
            android:name="com.livewallpaper.app.SettingsActivity"
            android:exported="false"
            android:label="设置"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 性能测试Activity -->
        <activity
            android:name="com.livewallpaper.app.PerformanceTestActivity"
            android:exported="false"
            android:label="性能监控"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 关于Activity -->
        <activity
            android:name="com.livewallpaper.app.AboutActivity"
            android:exported="false"
            android:label="关于"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 隐私政策Activity -->
        <activity
            android:name="com.livewallpaper.app.PrivacyPolicyActivity"
            android:exported="false"
            android:label="隐私政策"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 服务条款Activity -->
        <activity
            android:name="com.livewallpaper.app.TermsOfServiceActivity"
            android:exported="false"
            android:label="服务条款"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 开源许可Activity -->
        <activity
            android:name="com.livewallpaper.app.OpenSourceLicensesActivity"
            android:exported="false"
            android:label="开源许可"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 音乐通知监听服务 -->
        <service
            android:name="com.livewallpaper.core.service.MusicNotificationListenerService"
            android:exported="false"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <!-- 壁纸服务 -->
        <service
            android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
            android:enabled="true"
            android:exported="true"
            android:label="@string/wallpaper_service_label"
            android:permission="android.permission.BIND_WALLPAPER" >
            <intent-filter>
                <action android:name="android.service.wallpaper.WallpaperService" />
            </intent-filter>

            <meta-data
                android:name="android.service.wallpaper"
                android:resource="@xml/wallpaper" />
        </service>

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.livewallpaper.app.debug.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>