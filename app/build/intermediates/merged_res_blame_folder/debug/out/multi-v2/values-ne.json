{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4828,4923,5030,5117,5202,5303,5412,5507,5610,5697,5808,5907,6012,6147,6232,6336", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,90,94,106,86,84,100,108,94,102,86,110,98,104,134,84,103,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4823,4918,5025,5112,5197,5298,5407,5502,5605,5692,5803,5902,6007,6142,6227,6331,6425"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4231,4360,4479,4595,4723,4822,4917,5029,5181,5302,5455,5539,5647,5745,5844,5956,6080,6193,6339,6482,6616,6781,6911,7063,7220,7349,7448,7543,7659,7783,7887,8006,8116,8262,8410,8520,8628,8703,8808,8913,9004,9099,9206,9293,9378,9479,9588,9683,9786,9873,9984,10083,10188,10323,10408,10512", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,90,94,106,86,84,100,108,94,102,86,110,98,104,134,84,103,93", "endOffsets": "4355,4474,4590,4718,4817,4912,5024,5176,5297,5450,5534,5642,5740,5839,5951,6075,6188,6334,6477,6611,6776,6906,7058,7215,7344,7443,7538,7654,7778,7882,8001,8111,8257,8405,8515,8623,8698,8803,8908,8999,9094,9201,9288,9373,9474,9583,9678,9781,9868,9979,10078,10183,10318,10403,10507,10601"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3341,3719,3831,3945", "endColumns": "100,111,113,107", "endOffsets": "3437,3826,3940,4048"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values-ne/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1014,1125,1283,1417,1534,1705,1841,1951,2220,2400,2514,2678,2811,2959,3111,3177,3249", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "1120,1278,1412,1529,1700,1836,1946,2051,2395,2509,2673,2806,2954,3106,3172,3244,3336"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,413,519,617,717,12824", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "203,306,408,514,612,712,820,12920"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values-ne/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2056", "endColumns": "163", "endOffsets": "2215"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "9,10,30,31,32,36,37,97,98,102,111,112,113,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,924,3442,3536,3633,4053,4135,10766,10853,11203,12045,12111,12201,12747,12925,13006,13074", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "919,1009,3531,3628,3714,4130,4226,10848,10934,11264,12106,12196,12289,12819,13001,13069,13189"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values-ne/values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "13194,13279", "endColumns": "84,88", "endOffsets": "13274,13363"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values-ne/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,292,347,420,494,599,661,790,900,1028,1081,1144,1259,1343,1391,1482,1527,1573,1637,1724,1768", "endColumns": "45,46,54,72,73,104,61,128,109,127,52,62,114,83,47,90,44,45,63,86,43,55", "endOffsets": "244,291,346,419,493,598,660,789,899,1027,1080,1143,1258,1342,1390,1481,1526,1572,1636,1723,1767,1823"}, "to": {"startLines": "94,95,96,99,100,101,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10606,10656,10707,10939,11016,11094,11269,11335,11468,11582,11714,11771,11838,11957,12294,12346,12441,12490,12540,12608,12699,13368", "endColumns": "49,50,58,76,77,108,65,132,113,131,56,66,118,87,51,94,48,49,67,90,47,59", "endOffsets": "10651,10702,10761,11011,11089,11198,11330,11463,11577,11709,11766,11833,11952,12040,12341,12436,12485,12535,12603,12694,12742,13423"}}]}]}