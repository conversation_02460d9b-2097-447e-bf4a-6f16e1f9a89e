{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-61:/values-in/values-in.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/res/values-in/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2013", "endColumns": "131", "endOffsets": "2140"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4690,4775,4874,4954,5037,5136,5235,5332,5432,5519,5622,5721,5825,5942,6022,6127", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4685,4770,4869,4949,5032,5131,5230,5327,5427,5514,5617,5716,5820,5937,6017,6122,6217"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3666,3785,3900,4012,4128,4226,4332,4455,4602,4725,4875,4962,5066,5159,5263,5381,5501,5610,5750,5888,6017,6195,6317,6437,6560,6683,6777,6878,6998,7131,7233,7340,7447,7589,7736,7845,7945,8021,8117,8212,8301,8386,8485,8565,8648,8747,8846,8943,9043,9130,9233,9332,9436,9553,9633,9738", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "3780,3895,4007,4123,4221,4327,4450,4597,4720,4870,4957,5061,5154,5258,5376,5496,5605,5745,5883,6012,6190,6312,6432,6555,6678,6772,6873,6993,7126,7228,7335,7442,7584,7731,7840,7940,8016,8112,8207,8296,8381,8480,8560,8643,8742,8841,8938,9038,9125,9228,9327,9431,9548,9628,9733,9828"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,925,3208,3306,3406,3492,3575,9833,9920,10005,10075,10142,10224,10307,10480,10558,10624", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "920,1002,3301,3401,3487,3570,3661,9915,10000,10070,10137,10219,10302,10374,10553,10619,10738"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,399,496,602,720,10379", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "195,297,394,491,597,715,830,10475"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/res/values-in/values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10743,10829", "endColumns": "85,89", "endOffsets": "10824,10914"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/res/values-in/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1007,1114,1278,1404,1510,1665,1792,1907,2145,2311,2416,2580,2706,2861,3005,3069,3129", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "1109,1273,1399,1505,1660,1787,1902,2008,2306,2411,2575,2701,2856,3000,3064,3124,3203"}}]}]}