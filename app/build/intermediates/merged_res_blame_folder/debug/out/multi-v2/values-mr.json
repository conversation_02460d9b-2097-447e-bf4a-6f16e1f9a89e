{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-62:/values-mr/values-mr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/res/values-mr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1013,1120,1288,1411,1523,1668,1789,1897,2137,2287,2395,2549,2673,2812,2965,3025,3091", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "1115,1283,1406,1518,1663,1784,1892,1989,2282,2390,2544,2668,2807,2960,3020,3086,3167"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10677,10762", "endColumns": "84,84", "endOffsets": "10757,10842"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/res/values-mr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1994", "endColumns": "142", "endOffsets": "2132"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,931,3172,3269,3367,3454,3540,9769,9858,9941,10010,10080,10160,10245,10417,10493,10559", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "926,1008,3264,3362,3449,3535,3620,9853,9936,10005,10075,10155,10240,10311,10488,10554,10672"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4665,4755,4855,4935,5018,5117,5216,5313,5412,5499,5603,5703,5807,5925,6005,6105", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4660,4750,4850,4930,5013,5112,5211,5308,5407,5494,5598,5698,5802,5920,6000,6100,6194"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3625,3750,3873,3984,4109,4212,4312,4427,4563,4686,4832,4917,5023,5114,5212,5326,5456,5567,5702,5836,5964,6142,6267,6383,6502,6627,6719,6814,6934,7063,7163,7266,7375,7512,7654,7769,7867,7943,8046,8150,8235,8325,8425,8505,8588,8687,8786,8883,8982,9069,9173,9273,9377,9495,9575,9675", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "3745,3868,3979,4104,4207,4307,4422,4558,4681,4827,4912,5018,5109,5207,5321,5451,5562,5697,5831,5959,6137,6262,6378,6497,6622,6714,6809,6929,7058,7158,7261,7370,7507,7649,7764,7862,7938,8041,8145,8230,8320,8420,8500,8583,8682,8781,8878,8977,9064,9168,9268,9372,9490,9570,9670,9764"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,309,410,513,615,720,10316", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "200,304,405,508,610,715,832,10412"}}]}]}