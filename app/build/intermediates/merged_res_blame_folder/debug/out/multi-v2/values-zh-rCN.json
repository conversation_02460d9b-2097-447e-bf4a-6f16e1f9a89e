{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-62:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "9440,9521", "endColumns": "80,76", "endOffsets": "9516,9593"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,880,945,1018,1093,1161,1234,1300", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,875,940,1013,1088,1156,1229,1295,1411"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,846,2736,2821,2912,2989,3063,8585,8663,8738,8803,8868,8941,9016,9185,9258,9324", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "841,917,2816,2907,2984,3058,3135,8658,8733,8798,8863,8936,9011,9079,9253,9319,9435"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/res/values-zh-rCN/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1784", "endColumns": "98", "endOffsets": "1878"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/res/values-zh-rCN/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1023,1152,1267,1369,1474,1590,1692,1883,1991,2092,2222,2337,2441,2549,2605,2662", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "1018,1147,1262,1364,1469,1585,1687,1779,1986,2087,2217,2332,2436,2544,2600,2657,2731"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4041,4123,4215,4295,4377,4475,4569,4662,4757,4841,4937,5033,5130,5238,5318,5410", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4036,4118,4210,4290,4372,4470,4564,4657,4752,4836,4932,5028,5125,5233,5313,5405,5495"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3140,3244,3347,3451,3553,3645,3733,3837,3945,4048,4163,4245,4341,4425,4514,4620,4734,4835,4945,5053,5161,5277,5384,5485,5589,5695,5780,5875,5980,6089,6179,6277,6375,6485,6600,6700,6791,6864,6954,7043,7126,7208,7300,7380,7462,7560,7654,7747,7842,7926,8022,8118,8215,8323,8403,8495", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "3239,3342,3446,3548,3640,3728,3832,3940,4043,4158,4240,4336,4420,4509,4615,4729,4830,4940,5048,5156,5272,5379,5480,5584,5690,5775,5870,5975,6084,6174,6272,6370,6480,6595,6695,6786,6859,6949,7038,7121,7203,7295,7375,7457,7555,7649,7742,7837,7921,8017,8113,8210,8318,8398,8490,8580"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,298,392,486,579,673,9084", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "192,293,387,481,574,668,764,9180"}}]}]}