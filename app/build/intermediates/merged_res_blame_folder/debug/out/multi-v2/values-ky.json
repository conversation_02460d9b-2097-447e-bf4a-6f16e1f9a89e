{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values-ky/values-ky.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3217,3611,3716,3825", "endColumns": "98,104,108,105", "endOffsets": "3311,3711,3820,3926"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values-ky/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,353,424,492,601,666,796,910,1053,1107,1166,1278,1375,1417,1492,1530,1568,1618,1683,1734", "endColumns": "41,49,61,70,67,108,64,129,113,142,53,58,111,96,41,74,37,37,49,64,50,55", "endOffsets": "240,290,352,423,491,600,665,795,909,1052,1106,1165,1277,1374,1416,1491,1529,1567,1617,1682,1733,1789"}, "to": {"startLines": "94,95,96,99,100,101,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10372,10418,10472,10712,10787,10859,11038,11107,11241,11359,11506,11564,11627,11743,12083,12129,12208,12250,12292,12346,12415,13112", "endColumns": "45,53,65,74,71,112,68,133,117,146,57,62,115,100,45,78,41,41,53,68,54,59", "endOffsets": "10413,10467,10533,10782,10854,10967,11102,11236,11354,11501,11559,11622,11738,11839,12124,12203,12245,12287,12341,12410,12465,13167"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "9,10,30,31,32,36,37,97,98,102,111,112,113,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,3316,3426,3526,3931,4013,10538,10627,10972,11844,11911,11996,12470,12644,12723,12791", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "924,1008,3421,3521,3606,4008,4106,10622,10707,11033,11906,11991,12078,12538,12718,12786,12904"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,103", "endOffsets": "149,253"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "12909,13008", "endColumns": "98,103", "endOffsets": "13003,13107"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values-ky/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2000", "endColumns": "157", "endOffsets": "2153"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,410,517,621,725,12543", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "200,302,405,512,616,720,831,12639"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values-ky/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1013,1119,1268,1397,1504,1649,1776,1891,2158,2327,2434,2584,2714,2851,3015,3079,3139", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "1114,1263,1392,1499,1644,1771,1886,1995,2322,2429,2579,2709,2846,3010,3074,3134,3212"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4690,4775,4887,4976,5060,5160,5261,5357,5454,5541,5652,5751,5851,5999,6089,6208", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4685,4770,4882,4971,5055,5155,5256,5352,5449,5536,5647,5746,5846,5994,6084,6203,6311"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4111,4234,4351,4465,4590,4690,4788,4903,5039,5180,5336,5420,5518,5610,5707,5823,5942,6045,6181,6315,6452,6627,6756,6873,6993,7114,7207,7305,7427,7564,7667,7792,7897,8031,8170,8279,8381,8457,8556,8660,8746,8831,8943,9032,9116,9216,9317,9413,9510,9597,9708,9807,9907,10055,10145,10264", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "4229,4346,4460,4585,4685,4783,4898,5034,5175,5331,5415,5513,5605,5702,5818,5937,6040,6176,6310,6447,6622,6751,6868,6988,7109,7202,7300,7422,7559,7662,7787,7892,8026,8165,8274,8376,8452,8551,8655,8741,8826,8938,9027,9111,9211,9312,9408,9505,9592,9703,9802,9902,10050,10140,10259,10367"}}]}]}