{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values-km/values-km.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,303,401,501,602,714,12469", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "195,298,396,496,597,709,821,12565"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values-km/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "991,1093,1248,1369,1474,1636,1760,1881,2139,2297,2414,2585,2710,2855,3013,3077,3135", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "1088,1243,1364,1469,1631,1755,1876,1985,2292,2409,2580,2705,2850,3008,3072,3130,3207"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4660,4748,4849,4929,5013,5113,5215,5311,5420,5507,5612,5710,5821,5938,6018,6125", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4655,4743,4844,4924,5008,5108,5210,5306,5415,5502,5607,5705,5816,5933,6013,6120,6220"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4096,4214,4333,4439,4555,4657,4763,4886,5030,5158,5310,5401,5501,5601,5711,5835,5960,6065,6191,6317,6445,6607,6729,6843,6956,7079,7180,7280,7406,7545,7649,7754,7866,7991,8119,8236,8344,8420,8517,8613,8701,8789,8890,8970,9054,9154,9256,9352,9461,9548,9653,9751,9862,9979,10059,10166", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "4209,4328,4434,4550,4652,4758,4881,5025,5153,5305,5396,5496,5596,5706,5830,5955,6060,6186,6312,6440,6602,6724,6838,6951,7074,7175,7275,7401,7540,7644,7749,7861,7986,8114,8231,8339,8415,8512,8608,8696,8784,8885,8965,9049,9149,9251,9347,9456,9543,9648,9746,9857,9974,10054,10161,10261"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values-km/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,349,415,484,584,651,783,897,1011,1066,1122,1252,1360,1403,1501,1536,1570,1633,1728,1775", "endColumns": "45,48,54,65,68,99,66,131,113,113,54,55,129,107,42,97,34,33,62,94,46,55", "endOffsets": "244,293,348,414,483,583,650,782,896,1010,1065,1121,1251,1359,1402,1500,1535,1569,1632,1727,1774,1830"}, "to": {"startLines": "94,95,96,99,100,101,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10266,10316,10369,10600,10670,10743,10912,10983,11119,11237,11355,11414,11474,11608,11950,11997,12099,12138,12176,12243,12342,13021", "endColumns": "49,52,58,69,72,103,70,135,117,117,58,59,133,111,46,101,38,37,66,98,50,59", "endOffsets": "10311,10364,10423,10665,10738,10842,10978,11114,11232,11350,11409,11469,11603,11715,11992,12094,12133,12171,12238,12337,12388,13076"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values-km/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1990", "endColumns": "148", "endOffsets": "2134"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "9,10,30,31,32,36,37,97,98,102,111,112,113,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,911,3309,3413,3511,3929,4013,10428,10513,10847,11720,11785,11865,12393,12570,12654,12720", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "906,986,3408,3506,3594,4008,4091,10508,10595,10907,11780,11860,11945,12464,12649,12715,12833"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3212,3599,3696,3829", "endColumns": "96,96,132,99", "endOffsets": "3304,3691,3824,3924"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values-km/values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "12838,12929", "endColumns": "90,91", "endOffsets": "12924,13016"}}]}]}