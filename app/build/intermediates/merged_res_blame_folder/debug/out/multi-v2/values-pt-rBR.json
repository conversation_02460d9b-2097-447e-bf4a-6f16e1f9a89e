{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-61:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,720,10455", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "197,299,398,498,605,715,835,10551"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1021,1126,1274,1401,1509,1676,1806,1928,2178,2348,2456,2620,2750,2907,3064,3133,3199", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "1121,1269,1396,1504,1671,1801,1923,2028,2343,2451,2615,2745,2902,3059,3128,3194,3278"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4647,4736,4837,4917,5001,5102,5208,5300,5399,5487,5599,5700,5804,5923,6003,6103", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4642,4731,4832,4912,4996,5097,5203,5295,5394,5482,5594,5695,5799,5918,5998,6098,6190"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3745,3864,3985,4101,4217,4319,4416,4530,4664,4782,4934,5018,5119,5214,5314,5429,5559,5665,5804,5940,6071,6237,6364,6484,6608,6728,6824,6921,7041,7157,7257,7368,7477,7617,7762,7872,7975,8061,8155,8247,8337,8426,8527,8607,8691,8792,8898,8990,9089,9177,9289,9390,9494,9613,9693,9793", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "3859,3980,4096,4212,4314,4411,4525,4659,4777,4929,5013,5114,5209,5309,5424,5554,5660,5799,5935,6066,6232,6359,6479,6603,6723,6819,6916,7036,7152,7252,7363,7472,7612,7757,7867,7970,8056,8150,8242,8332,8421,8522,8602,8686,8787,8893,8985,9084,9172,9284,9385,9489,9608,9688,9788,9880"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10812,10895", "endColumns": "82,84", "endOffsets": "10890,10975"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2033", "endColumns": "144", "endOffsets": "2173"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,935,3283,3380,3479,3565,3648,9885,9976,10063,10135,10204,10289,10379,10556,10632,10699", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "930,1016,3375,3474,3560,3643,3740,9971,10058,10130,10199,10284,10374,10450,10627,10694,10807"}}]}]}