{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-61:/values-sq/values-sq.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,409,527,629,724,836,974,1090,1236,1320,1420,1512,1611,1729,1853,1958,2095,2229,2373,2562,2700,2823,2947,3073,3166,3262,3387,3528,3623,3734,3843,3982,4127,4238,4337,4414,4508,4602,4690,4773,4878,4964,5047,5146,5247,5342,5440,5528,5634,5734,5837,5965,6050,6164", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "169,285,404,522,624,719,831,969,1085,1231,1315,1415,1507,1606,1724,1848,1953,2090,2224,2368,2557,2695,2818,2942,3068,3161,3257,3382,3523,3618,3729,3838,3977,4122,4233,4332,4409,4503,4597,4685,4768,4873,4959,5042,5141,5242,5337,5435,5523,5629,5729,5832,5960,6045,6159,6266"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3832,3951,4067,4186,4304,4406,4501,4613,4751,4867,5013,5097,5197,5289,5388,5506,5630,5735,5872,6006,6150,6339,6477,6600,6724,6850,6943,7039,7164,7305,7400,7511,7620,7759,7904,8015,8114,8191,8285,8379,8467,8550,8655,8741,8824,8923,9024,9119,9217,9305,9411,9511,9614,9742,9827,9941", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "3946,4062,4181,4299,4401,4496,4608,4746,4862,5008,5092,5192,5284,5383,5501,5625,5730,5867,6001,6145,6334,6472,6595,6719,6845,6938,7034,7159,7300,7395,7506,7615,7754,7899,8010,8109,8186,8280,8374,8462,8545,8650,8736,8819,8918,9019,9114,9212,9300,9406,9506,9609,9737,9822,9936,10043"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,404,501,609,720,10617", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "199,301,399,496,604,715,837,10713"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/res/values-sq/values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,3361,3460,3562,3658,3739,10048,10140,10230,10299,10366,10453,10544,10718,10795,10861", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "932,1015,3455,3557,3653,3734,3827,10135,10225,10294,10361,10448,10539,10612,10790,10856,10977"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/res/values-sq/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1020,1127,1300,1437,1544,1705,1839,1965,2210,2380,2488,2663,2801,2963,3147,3212,3279", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "1122,1295,1432,1539,1700,1834,1960,2076,2375,2483,2658,2796,2958,3142,3207,3274,3356"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/res/values-sq/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2081", "endColumns": "128", "endOffsets": "2205"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/res/values-sq/values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10982,11080", "endColumns": "97,98", "endOffsets": "11075,11174"}}]}]}