{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values/values.xml", "from": {"startLines": "5,7,10,13,16,19,21,23,25,27,29,31,33,35,37,39,41,42,43,44,45,46,47,48", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "167,224,373,620,869,1145,1284,1441,1653,1857,2085,2309,2572,2743,2926,3145,3330,3368,3441,3475,3510,3559,3623,3658", "endLines": "5,7,12,15,18,20,22,24,26,28,30,32,34,36,38,40,41,42,43,44,45,46,47,50", "endColumns": "55,45,11,11,11,20,27,51,19,86,68,62,17,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "222,269,619,868,1144,1283,1440,1652,1856,2084,2308,2571,2742,2925,3144,3329,3367,3440,3474,3509,3558,3622,3657,3822"}, "to": {"startLines": "94,95,242,245,248,253,255,257,260,262,264,266,268,270,272,274,280,281,282,283,284,285,286,303", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6072,6132,17066,17322,17580,17981,18124,18285,18537,18745,18977,19205,19472,19647,19834,20057,20435,20477,20554,20592,20631,20684,20752,21709", "endLines": "94,95,244,247,250,254,256,258,261,263,265,267,269,271,273,275,280,281,282,283,284,285,286,305", "endColumns": "59,49,11,11,11,20,27,51,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "6127,6177,17317,17575,17860,18119,18280,18496,18740,18972,19200,19467,19642,19829,20052,20241,20472,20549,20587,20626,20679,20747,20786,21873"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/7d2fe0265bf6872a3e154648728fc9b6/transformed/navigation-runtime-2.7.7/res/values/values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "93,338,511,514", "startColumns": "4,4,4,4", "startOffsets": "6019,23681,29827,29942", "endLines": "93,344,513,516", "endColumns": "52,24,24,24", "endOffsets": "6067,23980,29937,30052"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,88,89,120,136,137,159,160,163,179,180,251,252,259,276,278,279,287,293,294,297,306,309,312", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3635,3694,3753,3813,3873,3933,3993,4053,4113,4173,4233,4293,4353,4412,4472,4532,4592,4652,4712,4772,4832,4892,4952,5012,5071,5131,5191,5250,5309,5368,5427,5486,5545,5619,5677,5789,5840,7486,8526,8591,11194,11260,11493,12530,12582,17865,17927,18501,20246,20331,20381,20791,21113,21160,21324,21878,21990,22101", "endLines": "52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,88,89,120,136,137,159,160,163,179,180,251,252,259,276,278,279,287,293,294,297,308,311,315", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "3689,3748,3808,3868,3928,3988,4048,4108,4168,4228,4288,4348,4407,4467,4527,4587,4647,4707,4767,4827,4887,4947,5007,5066,5126,5186,5245,5304,5363,5422,5481,5540,5614,5672,5727,5835,5890,7534,8586,8640,11255,11356,11546,12577,12637,17922,17976,18532,20275,20376,20430,20832,21155,21191,21409,21985,22096,22291"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,13,14,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,91,92,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,122,129,130,131,132,133,134,135,292,316,317,321,322,326,336,337,352,358,368,401,431,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,879,944,1681,1750,1956,2026,2094,2166,2236,2297,2371,2444,2505,2566,2628,2692,2754,2815,2883,2983,3043,3109,3182,3251,3308,3360,3422,3494,3570,5949,5984,6356,6411,6474,6529,6587,6645,6706,6769,6826,6877,6927,6988,7045,7111,7145,7180,7607,8015,8082,8154,8223,8292,8366,8438,21042,22296,22413,22614,22724,22925,23542,23614,24165,24368,24669,26400,27400,28082", "endLines": "2,7,8,13,14,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,91,92,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,122,129,130,131,132,133,134,135,292,316,320,321,325,326,336,337,357,367,400,421,463,469", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,939,1005,1745,1808,2021,2089,2161,2231,2292,2366,2439,2500,2561,2623,2687,2749,2810,2878,2978,3038,3104,3177,3246,3303,3355,3417,3489,3565,3630,5979,6014,6406,6469,6524,6582,6640,6701,6764,6821,6872,6922,6983,7040,7106,7140,7175,7210,7672,8077,8149,8218,8287,8361,8433,8521,21108,22408,22609,22719,22920,23049,23609,23676,24363,24664,26395,27076,28077,28244"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "15,16,17,18,19,20,21,22,138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,154,155,470,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1010,1100,1180,1270,1360,1440,1521,1601,8645,8750,8931,9056,9163,9343,9466,9582,9852,10040,10145,10326,10451,10626,10774,10837,10899,28249,30239", "endLines": "15,16,17,18,19,20,21,22,138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,154,155,482,540", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1095,1175,1265,1355,1435,1516,1596,1676,8745,8926,9051,9158,9338,9461,9577,9680,10035,10140,10321,10446,10621,10769,10832,10894,10973,28559,30651"}}, {"source": "/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/packaged_res/debug/values/values.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "127", "endColumns": "58", "endOffsets": "181"}, "to": {"startLines": "302", "startColumns": "4", "startOffsets": "21650", "endColumns": "58", "endOffsets": "21704"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/70d17bb71056a7af8202413617c814bf/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "117", "startColumns": "4", "startOffsets": "7318", "endColumns": "53", "endOffsets": "7367"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7677", "endColumns": "82", "endOffsets": "7755"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "181,182,183,184,185,186,187,188,189,190,193,194,195,196,197,198,199,200,201,202,203,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12642,12730,12816,12897,12981,13050,13115,13198,13304,13390,13510,13564,13633,13694,13763,13852,13947,14021,14118,14211,14309,14458,14549,14637,14733,14831,14895,14963,15050,15144,15211,15283,15355,15456,15565,15641,15710,15758,15824,15888,15945,16002,16074,16124,16178,16249,16320,16390,16459,16517,16593,16664,16738,16824,16874,16944", "endLines": "181,182,183,184,185,186,187,188,189,192,193,194,195,196,197,198,199,200,201,202,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "12725,12811,12892,12976,13045,13110,13193,13299,13385,13505,13559,13628,13689,13758,13847,13942,14016,14113,14206,14304,14453,14544,14632,14728,14826,14890,14958,15045,15139,15206,15278,15350,15451,15560,15636,15705,15753,15819,15883,15940,15997,16069,16119,16173,16244,16315,16385,16454,16512,16588,16659,16733,16819,16869,16939,17004"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/e5a633594e5175518bfd9b799ae16cd6/transformed/fragment-1.5.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "87,98,119,422,427", "startColumns": "4,4,4,4,4", "startOffsets": "5732,6291,7422,27081,27251", "endLines": "87,98,119,426,430", "endColumns": "56,64,63,24,24", "endOffsets": "5784,6351,7481,27246,27395"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "298,299", "startColumns": "4,4", "startOffsets": "21414,21470", "endColumns": "55,54", "endOffsets": "21465,21520"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/ba550fd068fe64f47a741a31b888ffc1/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "7215", "endColumns": "42", "endOffsets": "7253"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/res/values/values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "327,345", "startColumns": "4,4", "startOffsets": "23054,23985", "endLines": "334,351", "endColumns": "8,20", "endOffsets": "23447,24160"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/58e20e4c087b24b68b469747591bff23/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "7372", "endColumns": "49", "endOffsets": "7417"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "121,146", "startColumns": "4,4", "startOffsets": "7539,9685", "endColumns": "67,166", "endOffsets": "7602,9847"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f506b16bfe272be73816844197d4d94f/transformed/navigation-common-2.7.7/res/values/values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "483,496,502,508,517", "startColumns": "4,4,4,4,4", "startOffsets": "28564,29203,29447,29694,30057", "endLines": "495,501,507,510,521", "endColumns": "24,24,24,24,24", "endOffsets": "29198,29442,29689,29822,30234"}}, {"source": "/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/res/values/themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "335", "startColumns": "4", "startOffsets": "23452", "endColumns": "89", "endOffsets": "23537"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f0e7da2fa4ed454db32133de195507b0/transformed/customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "90,96", "startColumns": "4,4", "startOffsets": "5895,6182", "endColumns": "53,66", "endOffsets": "5944,6244"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/0be44cfda288f48cbd9f0a30e1e4f52a/transformed/activity-1.8.2/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "97,116", "startColumns": "4,4", "startOffsets": "6249,7258", "endColumns": "41,59", "endOffsets": "6286,7313"}}, {"source": "/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/res/values/strings.xml", "from": {"startLines": "3,2,4,33,10,38,15,14,13,24,25,26,27,22,23,28,29,18,19,20,21,34,8,7,9,40,39,35,32,6,5", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "78,36,142,1602,452,1804,640,570,508,1136,1194,1269,1325,995,1057,1396,1449,750,802,869,923,1669,361,313,412,1921,1861,1726,1533,239,188", "endColumns": "63,41,45,66,35,56,90,69,61,57,74,55,70,61,78,52,64,51,66,53,71,56,50,47,39,56,59,58,68,73,50", "endOffsets": "137,73,183,1664,483,1856,726,635,565,1189,1264,1320,1391,1052,1131,1444,1509,797,864,918,990,1721,407,356,447,1973,1916,1780,1597,308,234"}, "to": {"startLines": "124,125,126,127,128,156,158,161,162,167,168,169,170,171,172,173,174,175,176,177,178,241,277,288,289,290,291,295,296,300,301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7760,7824,7866,7912,7979,10978,11103,11361,11431,11766,11824,11899,11955,12026,12088,12167,12220,12285,12337,12404,12458,17009,20280,20837,20885,20925,20982,21196,21255,21525,21599", "endColumns": "63,41,45,66,35,56,90,69,61,57,74,55,70,61,78,52,64,51,66,53,71,56,50,47,39,56,59,58,68,73,50", "endOffsets": "7819,7861,7907,7974,8010,11030,11189,11426,11488,11819,11894,11950,12021,12083,12162,12215,12280,12332,12399,12453,12525,17061,20326,20880,20920,20977,21037,21250,21319,21594,21645"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "9,10,11,12,25,26,157,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "630,688,754,817,1813,1884,11035,11551,11618,11697", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "683,749,812,874,1879,1951,11098,11613,11692,11761"}}]}]}