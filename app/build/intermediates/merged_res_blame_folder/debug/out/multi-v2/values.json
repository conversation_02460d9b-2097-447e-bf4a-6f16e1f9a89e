{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-62:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,177,178,179,180,181,182,183,184,185,186,187,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11597,11685,11771,11852,11936,12005,12070,12153,12259,12345,12465,12519,12588,12649,12718,12807,12902,12976,13073,13166,13264,13413,13504,13592,13688,13786,13850,13918,14005,14099,14166,14238,14310,14411,14520,14596,14665,14713,14779,14843,14900,14957,15029,15079,15133,15204,15275,15345,15414,15472,15548,15619,15693,15779,15829,15899", "endLines": "165,166,167,168,169,170,171,172,173,176,177,178,179,180,181,182,183,184,185,186,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "11680,11766,11847,11931,12000,12065,12148,12254,12340,12460,12514,12583,12644,12713,12802,12897,12971,13068,13161,13259,13408,13499,13587,13683,13781,13845,13913,14000,14094,14161,14233,14305,14406,14515,14591,14660,14708,14774,14838,14895,14952,15024,15074,15128,15199,15270,15340,15409,15467,15543,15614,15688,15774,15824,15894,15959"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "109,134", "startColumns": "4,4", "startOffsets": "6777,8923", "endColumns": "67,166", "endOffsets": "6840,9085"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/46c6a361a054e89c36e0f5f8209c30df/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6556", "endColumns": "53", "endOffsets": "6605"}}, {"source": "/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/packaged_res/debug/values/values.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "127", "endColumns": "58", "endOffsets": "181"}, "to": {"startLines": "248", "startColumns": "4", "startOffsets": "17221", "endColumns": "58", "endOffsets": "17275"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,5,6,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,81,82,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,110,117,118,119,120,121,122,123,238,259,260,264,265,269,271,272,280,286,296,329,359,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,370,435,1172,1241,1304,1374,1442,1514,1584,1645,1719,1792,1853,1914,1976,2040,2102,2163,2231,2331,2391,2457,2530,2599,2656,2708,2770,2842,2918,5297,5332,5594,5649,5712,5767,5825,5883,5944,6007,6064,6115,6165,6226,6283,6349,6383,6418,6845,7253,7320,7392,7461,7530,7604,7676,16613,17698,17815,18016,18126,18327,18546,18618,18989,19192,19493,21224,22224,22906", "endLines": "2,3,4,5,6,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,81,82,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,110,117,118,119,120,121,122,123,238,259,263,264,268,269,271,272,285,295,328,349,391,397", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,430,496,1236,1299,1369,1437,1509,1579,1640,1714,1787,1848,1909,1971,2035,2097,2158,2226,2326,2386,2452,2525,2594,2651,2703,2765,2837,2913,2978,5327,5362,5644,5707,5762,5820,5878,5939,6002,6059,6110,6160,6221,6278,6344,6378,6413,6448,6910,7315,7387,7456,7525,7599,7671,7759,16679,17810,18011,18121,18322,18451,18613,18680,19187,19488,21219,21900,22901,23068"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "7,8,9,10,11,12,13,14,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,398,450", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "501,591,671,761,851,931,1012,1092,7883,7988,8169,8294,8401,8581,8704,8820,9090,9278,9383,9564,9689,9864,10012,10075,10137,23073,25063", "endLines": "7,8,9,10,11,12,13,14,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,410,468", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "586,666,756,846,926,1007,1087,1167,7983,8164,8289,8396,8576,8699,8815,8918,9273,9378,9559,9684,9859,10007,10070,10132,10211,23383,25475"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/990754870ae118c24ba9a2f79cf8978e/transformed/fragment-1.5.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "77,86,107,350,355", "startColumns": "4,4,4,4,4", "startOffsets": "5080,5529,6660,21905,22075", "endLines": "77,86,107,354,358", "endColumns": "56,64,63,24,24", "endOffsets": "5132,5589,6719,22070,22219"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6e3888cf5cc1490bda08037d58e828a/transformed/navigation-runtime-2.7.7/res/values/values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "83,273,439,442", "startColumns": "4,4,4,4", "startOffsets": "5367,18685,24651,24766", "endLines": "83,279,441,444", "endColumns": "52,24,24,24", "endOffsets": "5415,18984,24761,24876"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "244,245", "startColumns": "4,4", "startOffsets": "16985,17041", "endColumns": "55,54", "endOffsets": "17036,17091"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/649745a5dede868b3da145ea863db2b8/transformed/activity-1.8.2/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "85,104", "startColumns": "4,4", "startOffsets": "5487,6496", "endColumns": "41,59", "endOffsets": "5524,6551"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "6915", "endColumns": "82", "endOffsets": "6993"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,78,79,108,124,125,146,147,150,163,164,226,227,228,229,231,232,233,239,240,243,249,252,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2983,3042,3101,3161,3221,3281,3341,3401,3461,3521,3581,3641,3701,3760,3820,3880,3940,4000,4060,4120,4180,4240,4300,4360,4419,4479,4539,4598,4657,4716,4775,4834,4893,4967,5025,5137,5188,6724,7764,7829,10364,10430,10663,11485,11537,16021,16083,16137,16173,16258,16308,16362,16684,16731,16895,17280,17392,17503", "endLines": "42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,78,79,108,124,125,146,147,150,163,164,226,227,228,229,231,232,233,239,240,243,251,254,258", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "3037,3096,3156,3216,3276,3336,3396,3456,3516,3576,3636,3696,3755,3815,3875,3935,3995,4055,4115,4175,4235,4295,4355,4414,4474,4534,4593,4652,4711,4770,4829,4888,4962,5020,5075,5183,5238,6772,7824,7878,10425,10526,10716,11532,11592,16078,16132,16168,16202,16303,16357,16403,16726,16762,16980,17387,17498,17693"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/fefc451889439275613f402eb30e02d5/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6610", "endColumns": "49", "endOffsets": "6655"}}, {"source": "/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/res/values/themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "270", "startColumns": "4", "startOffsets": "18456", "endColumns": "89", "endOffsets": "18541"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2be285cad9a2e4526138f0a15781302b/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "103", "startColumns": "4", "startOffsets": "6453", "endColumns": "42", "endOffsets": "6491"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/edebdb5651d24310fc996d456ec0159e/transformed/customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "80,84", "startColumns": "4,4", "startOffsets": "5243,5420", "endColumns": "53,66", "endOffsets": "5292,5482"}}, {"source": "/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/res/values/strings.xml", "from": {"startLines": "3,2,4,33,10,38,15,14,13,24,25,26,27,22,23,28,29,18,19,20,21,34,8,7,9,40,39,35,32,6,5", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "78,36,142,1602,452,1804,640,570,508,1136,1194,1269,1325,995,1057,1396,1449,750,802,869,923,1669,361,313,412,1921,1861,1726,1533,239,188", "endColumns": "63,41,45,66,35,56,90,69,61,57,74,55,70,61,78,52,64,51,66,53,71,56,50,47,39,56,59,58,68,73,50", "endOffsets": "137,73,183,1664,483,1856,726,635,565,1189,1264,1320,1391,1052,1131,1444,1509,797,864,918,990,1721,407,356,447,1973,1916,1780,1597,308,234"}, "to": {"startLines": "112,113,114,115,116,144,145,148,149,151,152,153,154,155,156,157,158,159,160,161,162,225,230,234,235,236,237,241,242,246,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6998,7062,7104,7150,7217,10216,10273,10531,10601,10721,10779,10854,10910,10981,11043,11122,11175,11240,11292,11359,11413,15964,16207,16408,16456,16496,16553,16767,16826,17096,17170", "endColumns": "63,41,45,66,35,56,90,69,61,57,74,55,70,61,78,52,64,51,66,53,71,56,50,47,39,56,59,58,68,73,50", "endOffsets": "7057,7099,7145,7212,7248,10268,10359,10596,10658,10774,10849,10905,10976,11038,11117,11170,11235,11287,11354,11408,11480,16016,16253,16451,16491,16548,16608,16821,16890,17165,17216"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/7fc51f9aa0b2d87d1dbaf52476c22433/transformed/navigation-common-2.7.7/res/values/values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "411,424,430,436,445", "startColumns": "4,4,4,4,4", "startOffsets": "23388,24027,24271,24518,24881", "endLines": "423,429,435,438,449", "endColumns": "24,24,24,24,24", "endOffsets": "24022,24266,24513,24646,25058"}}]}]}