{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-62:/values-tr/values-tr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,499,601,707,10303", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "197,299,397,494,596,702,813,10399"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/res/values-tr/values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10668,10752", "endColumns": "83,86", "endOffsets": "10747,10834"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "818,911,3246,3341,3441,3525,3608,9758,9846,9930,9998,10064,10144,10232,10404,10482,10550", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "906,990,3336,3436,3520,3603,3703,9841,9925,9993,10059,10139,10227,10298,10477,10545,10663"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/res/values-tr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2011", "endColumns": "146", "endOffsets": "2153"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/res/values-tr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "995,1105,1260,1396,1501,1648,1778,1905,2158,2330,2437,2594,2728,2873,3040,3102,3166", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "1100,1255,1391,1496,1643,1773,1900,2006,2325,2432,2589,2723,2868,3035,3097,3161,3241"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4554,4639,4756,4836,4920,5020,5120,5216,5311,5399,5505,5605,5704,5825,5905,6012", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4549,4634,4751,4831,4915,5015,5115,5211,5306,5394,5500,5600,5699,5820,5900,6007,6100"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3708,3823,3936,4054,4169,4265,4361,4474,4607,4729,4869,4954,5052,5141,5238,5353,5474,5577,5714,5850,5972,6143,6261,6377,6495,6610,6700,6798,6922,7051,7152,7254,7360,7496,7636,7748,7850,7926,8023,8121,8207,8292,8409,8489,8573,8673,8773,8869,8964,9052,9158,9258,9357,9478,9558,9665", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "3818,3931,4049,4164,4260,4356,4469,4602,4724,4864,4949,5047,5136,5233,5348,5469,5572,5709,5845,5967,6138,6256,6372,6490,6605,6695,6793,6917,7046,7147,7249,7355,7491,7631,7743,7845,7921,8018,8116,8202,8287,8404,8484,8568,8668,8768,8864,8959,9047,9153,9253,9352,9473,9553,9660,9753"}}]}]}