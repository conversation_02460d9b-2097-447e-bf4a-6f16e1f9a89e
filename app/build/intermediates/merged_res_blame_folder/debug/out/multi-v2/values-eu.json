{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-61:/values-eu/values-eu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4832,4920,5030,5110,5195,5290,5393,5484,5583,5672,5780,5880,5986,6104,6184,6288", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4827,4915,5025,5105,5190,5285,5388,5479,5578,5667,5775,5875,5981,6099,6179,6283,6378"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3743,3890,4037,4164,4309,4438,4536,4651,4791,4910,5055,5139,5244,5340,5440,5559,5680,5790,5933,6077,6212,6403,6528,6650,6774,6896,6993,7090,7218,7353,7451,7554,7660,7807,7958,8066,8166,8242,8338,8433,8520,8608,8718,8798,8883,8978,9081,9172,9271,9360,9468,9568,9674,9792,9872,9976", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "3885,4032,4159,4304,4433,4531,4646,4786,4905,5050,5134,5239,5335,5435,5554,5675,5785,5928,6072,6207,6398,6523,6645,6769,6891,6988,7085,7213,7348,7446,7549,7655,7802,7953,8061,8161,8237,8333,8428,8515,8603,8713,8793,8878,8973,9076,9167,9266,9355,9463,9563,9669,9787,9867,9971,10066"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/res/values-eu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1008,1112,1284,1408,1517,1669,1794,1918,2169,2347,2455,2618,2746,2900,3060,3126,3191", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "1107,1279,1403,1512,1664,1789,1913,2021,2342,2450,2613,2741,2895,3055,3121,3186,3278"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/res/values-eu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2026", "endColumns": "142", "endOffsets": "2164"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "11009,11095", "endColumns": "85,88", "endOffsets": "11090,11179"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,406,509,614,717,10643", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "198,301,401,504,609,712,831,10739"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,927,3283,3384,3488,3580,3656,10071,10160,10244,10319,10391,10479,10569,10744,10821,10889", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "922,1003,3379,3483,3575,3651,3738,10155,10239,10314,10386,10474,10564,10638,10816,10884,11004"}}]}]}