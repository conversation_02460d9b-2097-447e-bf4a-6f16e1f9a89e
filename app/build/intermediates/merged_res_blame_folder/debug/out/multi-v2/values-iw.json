{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-62:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,495,596,696,9984", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "194,296,393,490,591,691,797,10080"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/res/values-iw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "975,1078,1232,1357,1461,1600,1725,1837,2058,2194,2298,2443,2566,2700,2845,2905,2965", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "1073,1227,1352,1456,1595,1720,1832,1935,2189,2293,2438,2561,2695,2840,2900,2960,3041"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/res/values-iw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1940", "endColumns": "117", "endOffsets": "2053"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/res/values-iw/values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,87", "endOffsets": "137,225"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10349,10436", "endColumns": "86,87", "endOffsets": "10431,10519"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,942,1008,1086,1168,1237,1311,1382", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,937,1003,1081,1163,1232,1306,1377,1496"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,891,3046,3139,3234,3317,3394,9458,9544,9623,9689,9755,9833,9915,10085,10159,10230", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "886,970,3134,3229,3312,3389,3474,9539,9618,9684,9750,9828,9910,9979,10154,10225,10344"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,406,521,620,715,828,955,1070,1210,1295,1393,1484,1580,1697,1817,1920,2056,2191,2312,2465,2583,2693,2808,2926,3018,3115,3227,3351,3449,3548,3652,3786,3927,4034,4134,4215,4320,4424,4510,4595,4698,4778,4862,4963,5062,5153,5248,5334,5436,5535,5632,5757,5837,5938", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "167,283,401,516,615,710,823,950,1065,1205,1290,1388,1479,1575,1692,1812,1915,2051,2186,2307,2460,2578,2688,2803,2921,3013,3110,3222,3346,3444,3543,3647,3781,3922,4029,4129,4210,4315,4419,4505,4590,4693,4773,4857,4958,5057,5148,5243,5329,5431,5530,5627,5752,5832,5933,6029"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3479,3596,3712,3830,3945,4044,4139,4252,4379,4494,4634,4719,4817,4908,5004,5121,5241,5344,5480,5615,5736,5889,6007,6117,6232,6350,6442,6539,6651,6775,6873,6972,7076,7210,7351,7458,7558,7639,7744,7848,7934,8019,8122,8202,8286,8387,8486,8577,8672,8758,8860,8959,9056,9181,9261,9362", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "3591,3707,3825,3940,4039,4134,4247,4374,4489,4629,4714,4812,4903,4999,5116,5236,5339,5475,5610,5731,5884,6002,6112,6227,6345,6437,6534,6646,6770,6868,6967,7071,7205,7346,7453,7553,7634,7739,7843,7929,8014,8117,8197,8281,8382,8481,8572,8667,8753,8855,8954,9051,9176,9256,9357,9453"}}]}]}