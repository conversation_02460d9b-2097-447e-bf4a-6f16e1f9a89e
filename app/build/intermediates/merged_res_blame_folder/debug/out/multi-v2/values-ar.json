{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-61:/values-ar/values-ar.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/res/values-ar/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1943", "endColumns": "129", "endOffsets": "2068"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/res/values-ar/values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4656,4741,4841,4920,5004,5105,5208,5307,5405,5492,5598,5698,5798,5927,6006,6107", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4651,4736,4836,4915,4999,5100,5203,5302,5400,5487,5593,5693,5793,5922,6001,6102,6195"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3550,3668,3785,3895,4011,4109,4214,4337,4474,4595,4738,4825,4930,5022,5122,5240,5366,5476,5622,5766,5903,6055,6181,6301,6424,6542,6635,6733,6856,6980,7080,7183,7291,7436,7586,7693,7795,7875,7969,8062,8151,8236,8336,8415,8499,8600,8703,8802,8900,8987,9093,9193,9293,9422,9501,9602", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "3663,3780,3890,4006,4104,4209,4332,4469,4590,4733,4820,4925,5017,5117,5235,5361,5471,5617,5761,5898,6050,6176,6296,6419,6537,6630,6728,6851,6975,7075,7178,7286,7431,7581,7688,7790,7870,7964,8057,8146,8231,8331,8410,8494,8595,8698,8797,8895,8982,9088,9188,9288,9417,9496,9597,9690"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/res/values-ar/values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,978,1045,1126,1209,1279,1355,1429", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,973,1040,1121,1204,1274,1350,1424,1545"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "817,906,3085,3180,3278,3363,3444,9695,9779,9860,9931,9998,10079,10162,10333,10409,10483", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "901,984,3175,3273,3358,3439,3545,9774,9855,9926,9993,10074,10157,10227,10404,10478,10599"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/res/values-ar/values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,198,300,395,498,601,703,10232", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "193,295,390,493,596,698,812,10328"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/res/values-ar/values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,85", "endOffsets": "134,220"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10604,10688", "endColumns": "83,85", "endOffsets": "10683,10769"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/res/values-ar/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "989,1093,1237,1359,1464,1602,1730,1841,2073,2210,2314,2464,2586,2725,2871,2935,3001", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "1088,1232,1354,1459,1597,1725,1836,1938,2205,2309,2459,2581,2720,2866,2930,2996,3080"}}]}]}