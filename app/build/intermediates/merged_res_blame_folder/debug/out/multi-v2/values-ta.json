{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values-ta/values-ta.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values-ta/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,246,294,350,414,490,595,658,807,927,1070,1121,1177,1302,1403,1444,1527,1561,1596,1651,1731,1774", "endColumns": "46,47,55,63,75,104,62,148,119,142,50,55,124,100,40,82,33,34,54,79,42,55", "endOffsets": "245,293,349,413,489,594,657,806,926,1069,1120,1176,1301,1402,1443,1526,1560,1595,1650,1730,1773,1829"}, "to": {"startLines": "94,95,96,99,100,101,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10638,10689,10741,10987,11055,11135,11324,11391,11544,11668,11815,11870,11930,12059,12411,12456,12543,12581,12620,12679,12763,13461", "endColumns": "50,51,59,67,79,108,66,152,123,146,54,59,128,104,44,86,37,38,58,83,46,59", "endOffsets": "10684,10736,10796,11050,11130,11239,11386,11539,11663,11810,11865,11925,12054,12159,12451,12538,12576,12615,12674,12758,12805,13516"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4876,4969,5082,5162,5250,5349,5469,5564,5669,5758,5880,5984,6091,6224,6304,6415", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4871,4964,5077,5157,5245,5344,5464,5559,5664,5753,5875,5979,6086,6219,6299,6410,6512"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4176,4302,4428,4549,4673,4774,4870,4983,5134,5265,5406,5490,5594,5694,5802,5919,6042,6151,6297,6441,6575,6781,6910,7031,7156,7302,7403,7501,7647,7783,7889,8002,8109,8255,8407,8516,8628,8706,8808,8911,8997,9090,9203,9283,9371,9470,9590,9685,9790,9879,10001,10105,10212,10345,10425,10536", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "4297,4423,4544,4668,4769,4865,4978,5129,5260,5401,5485,5589,5689,5797,5914,6037,6146,6292,6436,6570,6776,6905,7026,7151,7297,7398,7496,7642,7778,7884,7997,8104,8250,8402,8511,8623,8701,8803,8906,8992,9085,9198,9278,9366,9465,9585,9680,9785,9874,9996,10100,10207,10340,10420,10531,10633"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values-ta/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2016", "endColumns": "160", "endOffsets": "2172"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "2,3,4,5,6,7,8,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,403,501,608,723,12900", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "196,299,398,496,603,718,846,12996"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "9,10,30,31,32,36,37,97,98,102,111,112,113,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "851,948,3385,3479,3580,3984,4067,10801,10892,11244,12164,12243,12325,12810,13001,13081,13150", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "943,1027,3474,3575,3666,4062,4171,10887,10982,11319,12238,12320,12406,12895,13076,13145,13265"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values-ta/values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "13270,13363", "endColumns": "92,97", "endOffsets": "13358,13456"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3268,3671,3773,3880", "endColumns": "116,101,106,103", "endOffsets": "3380,3768,3875,3979"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values-ta/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1032,1137,1289,1416,1522,1674,1802,1915,2177,2357,2464,2617,2752,2906,3062,3124,3187", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "1132,1284,1411,1517,1669,1797,1910,2011,2352,2459,2612,2747,2901,3057,3119,3182,3263"}}]}]}