{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values-am/values-am.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4319,4404,4506,4587,4670,4770,4867,4962,5057,5142,5244,5343,5442,5560,5641,5742", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4314,4399,4501,4582,4665,4765,4862,4957,5052,5137,5239,5338,5437,5555,5636,5737,5834"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3783,3894,4004,4112,4219,4313,4403,4510,4638,4748,4877,4959,5057,5144,5237,5347,5466,5569,5692,5817,5941,6089,6205,6318,6432,6547,6635,6730,6840,6959,7054,7156,7258,7378,7504,7608,7704,7778,7871,7963,8047,8132,8234,8315,8398,8498,8595,8690,8785,8870,8972,9071,9170,9288,9369,9470", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "3889,3999,4107,4214,4308,4398,4505,4633,4743,4872,4954,5052,5139,5232,5342,5461,5564,5687,5812,5936,6084,6200,6313,6427,6542,6630,6725,6835,6954,7049,7151,7253,7373,7499,7603,7699,7773,7866,7958,8042,8127,8229,8310,8393,8493,8590,8685,8780,8865,8967,9066,9165,9283,9364,9465,9562"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values-am/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,286,337,398,461,546,606,696,776,876,926,984,1087,1158,1195,1269,1301,1337,1383,1447,1486", "endColumns": "39,46,50,60,62,84,59,89,79,99,49,57,102,70,36,73,31,35,45,63,38,55", "endOffsets": "238,285,336,397,460,545,605,695,775,875,925,983,1086,1157,1194,1268,1300,1336,1382,1446,1485,1541"}, "to": {"startLines": "94,95,96,99,100,101,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9567,9611,9662,9877,9942,10009,10164,10228,10322,10406,10510,10564,10626,10733,11033,11074,11152,11188,11228,11278,11346,11994", "endColumns": "43,50,54,64,66,88,63,93,83,103,53,61,106,74,40,77,35,39,49,67,42,59", "endOffsets": "9606,9657,9712,9937,10004,10093,10223,10317,10401,10505,10559,10621,10728,10803,11069,11147,11183,11223,11273,11341,11384,12049"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "9,10,30,31,32,36,37,97,98,102,111,112,113,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,875,3054,3146,3242,3622,3700,9717,9799,10098,10808,10874,10952,11389,11560,11640,11705", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "870,947,3141,3237,3319,3695,3778,9794,9872,10159,10869,10947,11028,11454,11635,11700,11816"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values-am/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1877", "endColumns": "131", "endOffsets": "2004"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,198,298,395,494,590,692,11459", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "193,293,390,489,585,687,787,11555"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values-am/values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11821,11908", "endColumns": "86,85", "endOffsets": "11903,11989"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values-am/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "952,1054,1193,1315,1417,1544,1667,1775,2009,2137,2240,2385,2508,2643,2770,2830,2887", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "1049,1188,1310,1412,1539,1662,1770,1872,2132,2235,2380,2503,2638,2765,2825,2882,2953"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2958,3324,3419,3525", "endColumns": "95,94,105,96", "endOffsets": "3049,3414,3520,3617"}}]}]}