{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-62:/values-kk/values-kk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/res/values-kk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1981", "endColumns": "163", "endOffsets": "2140"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,406,522,624,725,843,981,1106,1231,1315,1418,1508,1605,1721,1845,1953,2095,2235,2367,2526,2649,2764,2883,2998,3089,3187,3310,3445,3549,3660,3766,3905,4050,4158,4258,4344,4437,4530,4616,4700,4804,4893,4978,5079,5183,5280,5376,5463,5567,5666,5764,5901,5991,6102", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "168,286,401,517,619,720,838,976,1101,1226,1310,1413,1503,1600,1716,1840,1948,2090,2230,2362,2521,2644,2759,2878,2993,3084,3182,3305,3440,3544,3655,3761,3900,4045,4153,4253,4339,4432,4525,4611,4695,4799,4888,4973,5074,5178,5275,5371,5458,5562,5661,5759,5896,5986,6097,6198"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3680,3798,3916,4031,4147,4249,4350,4468,4606,4731,4856,4940,5043,5133,5230,5346,5470,5578,5720,5860,5992,6151,6274,6389,6508,6623,6714,6812,6935,7070,7174,7285,7391,7530,7675,7783,7883,7969,8062,8155,8241,8325,8429,8518,8603,8704,8808,8905,9001,9088,9192,9291,9389,9526,9616,9727", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "3793,3911,4026,4142,4244,4345,4463,4601,4726,4851,4935,5038,5128,5225,5341,5465,5573,5715,5855,5987,6146,6269,6384,6503,6618,6709,6807,6930,7065,7169,7280,7386,7525,7670,7778,7878,7964,8057,8150,8236,8320,8424,8513,8598,8699,8803,8900,8996,9083,9187,9286,9384,9521,9611,9722,9823"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/res/values-kk/values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10743,10835", "endColumns": "91,95", "endOffsets": "10830,10926"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,990,1058,1141,1226,1299,1375,1445", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,985,1053,1136,1221,1294,1370,1440,1558"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "819,912,3212,3317,3419,3506,3587,9828,9918,10000,10069,10137,10220,10305,10479,10555,10625", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "907,990,3312,3414,3501,3582,3675,9913,9995,10064,10132,10215,10300,10373,10550,10620,10738"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,404,507,611,708,10378", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "195,297,399,502,606,703,814,10474"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/res/values-kk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "995,1098,1251,1377,1483,1623,1749,1872,2145,2310,2416,2573,2702,2855,3012,3075,3134", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "1093,1246,1372,1478,1618,1744,1867,1976,2305,2411,2568,2697,2850,3007,3070,3129,3207"}}]}]}