{"logs": [{"outputFile": "com.livewallpaper.app-mergeDebugResources-71:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "9,10,30,31,32,36,37,97,98,102,111,112,113,121,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,842,2825,2913,3004,3368,3442,9092,9170,9447,10063,10126,10199,10543,10711,10786,10851", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "837,911,2908,2999,3077,3437,3514,9165,9239,9505,10121,10194,10269,10605,10781,10846,10962"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "916,1017,1145,1260,1362,1469,1585,1687,1888,1998,2099,2228,2343,2450,2558,2613,2670", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "1012,1140,1255,1357,1464,1580,1682,1775,1993,2094,2223,2338,2445,2553,2608,2665,2737"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1780", "endColumns": "107", "endOffsets": "1883"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "10967,11048", "endColumns": "80,76", "endOffsets": "11043,11120"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,242,285,334,394,455,525,585,656,725,814,864,918,990,1046,1078,1121,1151,1181,1215,1255,1287", "endColumns": "38,42,48,59,60,69,59,70,68,88,49,53,71,55,31,42,29,29,33,39,31,55", "endOffsets": "241,284,333,393,454,524,584,655,724,813,863,917,989,1045,1077,1120,1150,1180,1214,1254,1286,1342"}, "to": {"startLines": "94,95,96,99,100,101,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8949,8992,9039,9244,9308,9373,9510,9574,9649,9722,9815,9869,9927,10003,10274,10310,10357,10391,10425,10463,10507,11125", "endColumns": "42,46,52,63,64,73,63,74,72,92,53,57,75,59,35,46,33,33,37,43,35,59", "endOffsets": "8987,9034,9087,9303,9368,9442,9569,9644,9717,9810,9864,9922,9998,10058,10305,10352,10386,10420,10458,10502,10538,11180"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2742,3082,3174,3275", "endColumns": "82,91,100,92", "endOffsets": "2820,3169,3270,3363"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,10610", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,10706"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4924,5020,5115,5223,5303,5395", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4029,4111,4200,4280,4362,4459,4553,4646,4739,4823,4919,5015,5110,5218,5298,5390,5480"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3519,3622,3724,3828,3929,4020,4109,4214,4319,4424,4540,4622,4718,4802,4890,4995,5108,5209,5317,5423,5531,5647,5752,5854,5959,6065,6150,6245,6350,6459,6549,6651,6749,6858,6972,7072,7163,7236,7326,7415,7498,7580,7669,7749,7831,7928,8022,8115,8208,8292,8388,8484,8579,8687,8767,8859", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "3617,3719,3823,3924,4015,4104,4209,4314,4419,4535,4617,4713,4797,4885,4990,5103,5204,5312,5418,5526,5642,5747,5849,5954,6060,6145,6240,6345,6454,6544,6646,6744,6853,6967,7067,7158,7231,7321,7410,7493,7575,7664,7744,7826,7923,8017,8110,8203,8287,8383,8479,8574,8682,8762,8854,8944"}}]}]}