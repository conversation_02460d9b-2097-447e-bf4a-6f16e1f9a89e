# 场景资源目录

这个目录包含动态壁纸的场景图片资源。

## 目录结构

```
scenes/
├── forest/          # 森林场景
│   ├── forest_dawn_bg.jpg
│   ├── forest_dawn_mg.png
│   ├── forest_dawn_fg.png
│   ├── forest_day_bg.jpg
│   ├── forest_day_mg.png
│   ├── forest_day_fg.png
│   ├── forest_dusk_bg.jpg
│   ├── forest_dusk_mg.png
│   ├── forest_dusk_fg.png
│   ├── forest_night_bg.jpg
│   ├── forest_night_mg.png
│   └── forest_night_fg.png
├── ocean/           # 海洋场景
│   ├── ocean_sunrise_bg.jpg
│   ├── ocean_sunrise_mg.png
│   ├── ocean_sunrise_fg.png
│   ├── ocean_day_bg.jpg
│   ├── ocean_day_mg.png
│   ├── ocean_day_fg.png
│   ├── ocean_sunset_bg.jpg
│   ├── ocean_sunset_mg.png
│   └── ocean_sunset_fg.png
├── mountain/        # 山脉场景
│   ├── mountain_dawn_bg.jpg
│   ├── mountain_dawn_mg.png
│   ├── mountain_dawn_fg.png
│   ├── mountain_snow_bg.jpg
│   ├── mountain_snow_mg.png
│   └── mountain_snow_fg.png
├── city/            # 城市场景
│   ├── city_dawn_bg.jpg
│   ├── city_dawn_mg.png
│   ├── city_dawn_fg.png
│   ├── city_night_bg.jpg
│   ├── city_night_mg.png
│   └── city_night_fg.png
├── abstract/        # 抽象艺术场景
│   ├── abstract_flow_bg.jpg
│   ├── abstract_flow_mg.png
│   ├── abstract_flow_fg.png
│   ├── abstract_geometric_bg.jpg
│   ├── abstract_geometric_mg.png
│   └── abstract_geometric_fg.png
└── space/           # 太空场景
    ├── space_nebula_bg.jpg
    ├── space_nebula_mg.png
    ├── space_nebula_fg.png
    ├── space_earth_bg.jpg
    ├── space_earth_mg.png
    └── space_earth_fg.png
```

## 图层说明

- **bg (Background)**: 背景图层，通常是静态的远景
- **mg (Middleground)**: 中景图层，可能包含一些动态元素
- **fg (Foreground)**: 前景图层，通常包含近景元素，支持视差效果

## 图片要求

- 背景图片：JPG格式，建议分辨率1920x1080或更高
- 中景/前景图片：PNG格式（支持透明度），建议分辨率1920x1080或更高
- 文件大小：单个文件不超过2MB，总体控制在合理范围内

## 注意事项

1. 所有图片资源应该是免版权或已获得使用许可的
2. 图片应该针对移动设备屏幕进行优化
3. 考虑不同屏幕比例的适配
4. 图层之间应该有良好的视觉层次感

## 当前状态

目前使用程序生成的占位图片，实际项目中应该替换为高质量的艺术资源。
