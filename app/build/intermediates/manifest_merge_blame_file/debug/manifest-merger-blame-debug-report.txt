1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livewallpaper.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
14
15    <!-- 位置权限 -->
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
18
19    <!-- 读取外部存储权限（用于自定义壁纸） -->
20    <uses-permission
20-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
22        android:maxSdkVersion="32" />
22-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
24
25    <!-- 通知监听权限（用于音乐可视化） -->
26    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
27
28    <!-- 壁纸权限 -->
29    <uses-permission android:name="android.permission.BIND_WALLPAPER" />
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
30
31    <permission
31-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
32        android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
36
37    <application
37-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
38        android:name="com.livewallpaper.app.MainApplication"
38-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:9-40
39        android:allowBackup="true"
39-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:26:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
41        android:dataExtractionRules="@xml/data_extraction_rules"
41-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:9-65
42        android:debuggable="true"
43        android:extractNativeLibs="false"
44        android:fullBackupContent="@xml/backup_rules"
44-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-54
45        android:icon="@mipmap/ic_launcher"
45-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-43
46        android:label="@string/app_name"
46-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-41
47        android:roundIcon="@mipmap/ic_launcher_round"
47-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
48        android:supportsRtl="true"
48-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-35
49        android:theme="@style/Theme.LiveWallpaper" >
49-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-51
50
51        <!-- 主Activity -->
52        <activity
52-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:37:9-46:20
53            android:name="com.livewallpaper.app.MainActivity"
53-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:38:13-41
54            android:exported="true"
54-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:39:13-36
55            android:label="@string/app_name"
55-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:13-45
56            android:theme="@style/Theme.LiveWallpaper" >
56-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-55
57            <intent-filter>
57-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-45:29
58                <action android:name="android.intent.action.MAIN" />
58-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
58-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:17-77
60-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:27-74
61            </intent-filter>
62        </activity>
63
64        <!-- 壁纸预览Activity -->
65        <activity
65-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:49:9-57:20
66            android:name="com.livewallpaper.app.WallpaperPreviewActivity"
66-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:50:13-53
67            android:exported="true"
67-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:51:13-36
68            android:label="@string/wallpaper_preview"
68-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:13-54
69            android:theme="@style/Theme.LiveWallpaper" >
69-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-55
70            <intent-filter>
70-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-56:29
71                <action android:name="android.intent.action.MAIN" />
71-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
71-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
72            </intent-filter>
73        </activity>
74
75        <!-- 时间系统测试Activity -->
76        <activity
76-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:60:9-64:58
77            android:name="com.livewallpaper.app.TimeTestActivity"
77-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:61:13-45
78            android:exported="false"
78-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:62:13-37
79            android:label="时间系统测试"
79-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:13-35
80            android:theme="@style/Theme.LiveWallpaper" />
80-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-55
81
82        <!-- 场景系统测试Activity -->
83        <activity
83-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:9-71:58
84            android:name="com.livewallpaper.app.SceneTestActivity"
84-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:68:13-46
85            android:exported="false"
85-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:69:13-37
86            android:label="场景系统测试"
86-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:13-35
87            android:theme="@style/Theme.LiveWallpaper" />
87-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-55
88
89        <!-- 天气系统测试Activity -->
90        <activity
90-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:9-78:58
91            android:name="com.livewallpaper.app.WeatherTestActivity"
91-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:75:13-48
92            android:exported="false"
92-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:76:13-37
93            android:label="天气系统测试"
93-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:13-35
94            android:theme="@style/Theme.LiveWallpaper" />
94-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-55
95
96        <!-- 音乐系统测试Activity -->
97        <activity
97-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:9-85:58
98            android:name="com.livewallpaper.app.MusicTestActivity"
98-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:82:13-46
99            android:exported="false"
99-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:83:13-37
100            android:label="音乐系统测试"
100-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:13-35
101            android:theme="@style/Theme.LiveWallpaper" />
101-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-55
102
103        <!-- 设置Activity -->
104        <activity
104-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:9-92:58
105            android:name="com.livewallpaper.app.SettingsActivity"
105-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:89:13-45
106            android:exported="false"
106-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:90:13-37
107            android:label="设置"
107-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:13-31
108            android:theme="@style/Theme.LiveWallpaper" />
108-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-55
109
110        <!-- 性能测试Activity -->
111        <activity
111-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:9-99:58
112            android:name="com.livewallpaper.app.PerformanceTestActivity"
112-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:96:13-52
113            android:exported="false"
113-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:97:13-37
114            android:label="性能监控"
114-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:13-33
115            android:theme="@style/Theme.LiveWallpaper" />
115-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-55
116
117        <!-- 关于Activity -->
118        <activity
118-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:9-106:58
119            android:name="com.livewallpaper.app.AboutActivity"
119-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:103:13-42
120            android:exported="false"
120-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:104:13-37
121            android:label="关于"
121-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:13-31
122            android:theme="@style/Theme.LiveWallpaper" />
122-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-55
123
124        <!-- 隐私政策Activity -->
125        <activity
125-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:9-113:58
126            android:name="com.livewallpaper.app.PrivacyPolicyActivity"
126-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:110:13-50
127            android:exported="false"
127-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:111:13-37
128            android:label="隐私政策"
128-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:13-33
129            android:theme="@style/Theme.LiveWallpaper" />
129-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-55
130
131        <!-- 服务条款Activity -->
132        <activity
132-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:9-120:58
133            android:name="com.livewallpaper.app.TermsOfServiceActivity"
133-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:117:13-51
134            android:exported="false"
134-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:118:13-37
135            android:label="服务条款"
135-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:13-33
136            android:theme="@style/Theme.LiveWallpaper" />
136-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-55
137
138        <!-- 开源许可Activity -->
139        <activity
139-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:9-127:58
140            android:name="com.livewallpaper.app.OpenSourceLicensesActivity"
140-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:124:13-55
141            android:exported="false"
141-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:125:13-37
142            android:label="开源许可"
142-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:13-33
143            android:theme="@style/Theme.LiveWallpaper" />
143-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
144
145        <!-- 音乐通知监听服务 -->
146        <service
146-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:9-137:19
147            android:name="com.livewallpaper.core.service.MusicNotificationListenerService"
147-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:131:13-91
148            android:exported="false"
148-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:132:13-37
149            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
149-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:13-87
150            <intent-filter>
150-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-136:29
151                <action android:name="android.service.notification.NotificationListenerService" />
151-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:17-99
151-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:25-96
152            </intent-filter>
153        </service>
154
155        <!-- 壁纸服务 -->
156        <service
156-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-23:19
157            android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
157-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-85
158            android:enabled="true"
158-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-35
159            android:exported="true"
159-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-36
160            android:label="@string/wallpaper_service_label"
160-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-60
161            android:permission="android.permission.BIND_WALLPAPER" >
161-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-67
162            <intent-filter>
162-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:29
163                <action android:name="android.service.wallpaper.WallpaperService" />
163-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-85
163-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:25-82
164            </intent-filter>
165
166            <meta-data
166-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-22:53
167                android:name="android.service.wallpaper"
167-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:17-57
168                android:resource="@xml/wallpaper" />
168-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-50
169        </service>
170
171        <activity
171-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
172            android:name="com.google.android.gms.common.api.GoogleApiActivity"
172-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:19-85
173            android:exported="false"
173-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:22:19-43
174            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
174-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:21:19-78
175
176        <meta-data
176-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
177            android:name="com.google.android.gms.version"
177-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
178            android:value="@integer/google_play_services_version" />
178-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
179
180        <activity
180-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
181            android:name="androidx.compose.ui.tooling.PreviewActivity"
181-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
182            android:exported="true" />
182-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
183        <activity
183-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
184            android:name="androidx.activity.ComponentActivity"
184-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
185            android:exported="true" />
185-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
186
187        <provider
187-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
188            android:name="androidx.startup.InitializationProvider"
188-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
189            android:authorities="com.livewallpaper.app.debug.androidx-startup"
189-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
190            android:exported="false" >
190-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
191            <meta-data
191-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
192                android:name="androidx.emoji2.text.EmojiCompatInitializer"
192-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
193                android:value="androidx.startup" />
193-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
194            <meta-data
194-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
195                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
195-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
196                android:value="androidx.startup" />
196-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
197            <meta-data
197-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
198                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
198-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
199                android:value="androidx.startup" />
199-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
200        </provider>
201
202        <service
202-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
203            android:name="androidx.room.MultiInstanceInvalidationService"
203-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
204            android:directBootAware="true"
204-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
205            android:exported="false" />
205-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
206
207        <receiver
207-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
208            android:name="androidx.profileinstaller.ProfileInstallReceiver"
208-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
209            android:directBootAware="false"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
210            android:enabled="true"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
211            android:exported="true"
211-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
212            android:permission="android.permission.DUMP" >
212-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
214                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
214-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
214-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
217                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
217-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
217-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
220                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
220-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
220-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
221            </intent-filter>
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
223                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
223-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
223-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
224            </intent-filter>
225        </receiver>
226    </application>
227
228</manifest>
