1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livewallpaper.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
14
15    <!-- 位置权限 -->
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
18
19    <!-- 读取外部存储权限（用于自定义壁纸） -->
20    <uses-permission
20-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
22        android:maxSdkVersion="32" />
22-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
24
25    <!-- 通知监听权限（用于音乐可视化） -->
26    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
27
28    <!-- 壁纸权限 -->
29    <uses-permission android:name="android.permission.BIND_WALLPAPER" />
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
30
31    <!-- 壁纸服务 -->
32    <service
32-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-21:15
33        android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
33-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-81
34        android:enabled="true"
34-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-31
35        android:exported="true"
35-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:9-32
36        android:label="@string/wallpaper_service_label"
36-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-56
37        android:permission="android.permission.BIND_WALLPAPER" >
37-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:9-63
38        <intent-filter>
38-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-16:25
39            <action android:name="android.service.wallpaper.WallpaperService" />
39-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-81
39-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:21-78
40        </intent-filter>
41
42        <meta-data
42-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:18:9-20:49
43            android:name="android.service.wallpaper"
43-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:19:13-53
44            android:resource="@xml/wallpaper" />
44-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-46
45    </service>
46
47    <permission
47-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
48        android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
52
53    <application
53-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
54        android:name="com.livewallpaper.app.MainApplication"
54-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:9-40
55        android:allowBackup="true"
55-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:26:9-35
56        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
56-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
57        android:dataExtractionRules="@xml/data_extraction_rules"
57-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:9-65
58        android:debuggable="true"
59        android:extractNativeLibs="false"
60        android:fullBackupContent="@xml/backup_rules"
60-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-54
61        android:icon="@mipmap/ic_launcher"
61-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-43
62        android:label="@string/app_name"
62-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-41
63        android:roundIcon="@mipmap/ic_launcher_round"
63-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
64        android:supportsRtl="true"
64-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-35
65        android:testOnly="true"
66        android:theme="@style/Theme.LiveWallpaper" >
66-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-51
67
68        <!-- 主Activity -->
69        <activity
69-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:37:9-46:20
70            android:name="com.livewallpaper.app.MainActivity"
70-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:38:13-41
71            android:exported="true"
71-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:39:13-36
72            android:label="@string/app_name"
72-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:13-45
73            android:theme="@style/Theme.LiveWallpaper" >
73-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-55
74            <intent-filter>
74-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-45:29
75                <action android:name="android.intent.action.MAIN" />
75-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
75-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:17-77
77-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:27-74
78            </intent-filter>
79        </activity>
80
81        <!-- 壁纸预览Activity -->
82        <activity
82-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:49:9-57:20
83            android:name="com.livewallpaper.app.WallpaperPreviewActivity"
83-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:50:13-53
84            android:exported="true"
84-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:51:13-36
85            android:label="@string/wallpaper_preview"
85-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:13-54
86            android:theme="@style/Theme.LiveWallpaper" >
86-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-55
87            <intent-filter>
87-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-56:29
88                <action android:name="android.intent.action.MAIN" />
88-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
88-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
89            </intent-filter>
90        </activity>
91
92        <!-- 时间系统测试Activity -->
93        <activity
93-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:60:9-64:58
94            android:name="com.livewallpaper.app.TimeTestActivity"
94-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:61:13-45
95            android:exported="false"
95-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:62:13-37
96            android:label="时间系统测试"
96-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:13-35
97            android:theme="@style/Theme.LiveWallpaper" />
97-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-55
98
99        <!-- 场景系统测试Activity -->
100        <activity
100-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:9-71:58
101            android:name="com.livewallpaper.app.SceneTestActivity"
101-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:68:13-46
102            android:exported="false"
102-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:69:13-37
103            android:label="场景系统测试"
103-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:13-35
104            android:theme="@style/Theme.LiveWallpaper" />
104-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-55
105
106        <!-- 天气系统测试Activity -->
107        <activity
107-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:9-78:58
108            android:name="com.livewallpaper.app.WeatherTestActivity"
108-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:75:13-48
109            android:exported="false"
109-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:76:13-37
110            android:label="天气系统测试"
110-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:13-35
111            android:theme="@style/Theme.LiveWallpaper" />
111-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-55
112
113        <!-- 音乐系统测试Activity -->
114        <activity
114-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:9-85:58
115            android:name="com.livewallpaper.app.MusicTestActivity"
115-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:82:13-46
116            android:exported="false"
116-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:83:13-37
117            android:label="音乐系统测试"
117-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:13-35
118            android:theme="@style/Theme.LiveWallpaper" />
118-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-55
119
120        <!-- 设置Activity -->
121        <activity
121-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:9-92:58
122            android:name="com.livewallpaper.app.SettingsActivity"
122-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:89:13-45
123            android:exported="false"
123-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:90:13-37
124            android:label="设置"
124-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:13-31
125            android:theme="@style/Theme.LiveWallpaper" />
125-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-55
126
127        <!-- 性能测试Activity -->
128        <activity
128-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:9-99:58
129            android:name="com.livewallpaper.app.PerformanceTestActivity"
129-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:96:13-52
130            android:exported="false"
130-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:97:13-37
131            android:label="性能监控"
131-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:13-33
132            android:theme="@style/Theme.LiveWallpaper" />
132-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-55
133
134        <!-- 关于Activity -->
135        <activity
135-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:9-106:58
136            android:name="com.livewallpaper.app.AboutActivity"
136-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:103:13-42
137            android:exported="false"
137-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:104:13-37
138            android:label="关于"
138-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:13-31
139            android:theme="@style/Theme.LiveWallpaper" />
139-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-55
140
141        <!-- 隐私政策Activity -->
142        <activity
142-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:9-113:58
143            android:name="com.livewallpaper.app.PrivacyPolicyActivity"
143-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:110:13-50
144            android:exported="false"
144-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:111:13-37
145            android:label="隐私政策"
145-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:13-33
146            android:theme="@style/Theme.LiveWallpaper" />
146-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-55
147
148        <!-- 服务条款Activity -->
149        <activity
149-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:9-120:58
150            android:name="com.livewallpaper.app.TermsOfServiceActivity"
150-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:117:13-51
151            android:exported="false"
151-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:118:13-37
152            android:label="服务条款"
152-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:13-33
153            android:theme="@style/Theme.LiveWallpaper" />
153-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-55
154
155        <!-- 开源许可Activity -->
156        <activity
156-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:9-127:58
157            android:name="com.livewallpaper.app.OpenSourceLicensesActivity"
157-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:124:13-55
158            android:exported="false"
158-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:125:13-37
159            android:label="开源许可"
159-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:13-33
160            android:theme="@style/Theme.LiveWallpaper" />
160-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
161
162        <!-- 音乐通知监听服务 -->
163        <service
163-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:9-137:19
164            android:name="com.livewallpaper.core.service.MusicNotificationListenerService"
164-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:131:13-91
165            android:exported="false"
165-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:132:13-37
166            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
166-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:13-87
167            <intent-filter>
167-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-136:29
168                <action android:name="android.service.notification.NotificationListenerService" />
168-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:17-99
168-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:25-96
169            </intent-filter>
170        </service>
171
172        <activity
172-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
173            android:name="com.google.android.gms.common.api.GoogleApiActivity"
173-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:19-85
174            android:exported="false"
174-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:22:19-43
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
175-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:21:19-78
176
177        <meta-data
177-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
178            android:name="com.google.android.gms.version"
178-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
179            android:value="@integer/google_play_services_version" />
179-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
180
181        <activity
181-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
182            android:name="androidx.compose.ui.tooling.PreviewActivity"
182-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
183            android:exported="true" />
183-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
184        <activity
184-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
185            android:name="androidx.activity.ComponentActivity"
185-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
186            android:exported="true" />
186-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
187
188        <provider
188-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
189            android:name="androidx.startup.InitializationProvider"
189-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
190            android:authorities="com.livewallpaper.app.debug.androidx-startup"
190-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
191            android:exported="false" >
191-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
192            <meta-data
192-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
193                android:name="androidx.emoji2.text.EmojiCompatInitializer"
193-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
194                android:value="androidx.startup" />
194-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
195            <meta-data
195-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
196                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
196-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
197                android:value="androidx.startup" />
197-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
198            <meta-data
198-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
199                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
199-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
200                android:value="androidx.startup" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
201        </provider>
202
203        <service
203-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
204            android:name="androidx.room.MultiInstanceInvalidationService"
204-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
205            android:directBootAware="true"
205-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
206            android:exported="false" />
206-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
207
208        <receiver
208-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
209            android:name="androidx.profileinstaller.ProfileInstallReceiver"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
210            android:directBootAware="false"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
211            android:enabled="true"
211-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
212            android:exported="true"
212-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
213            android:permission="android.permission.DUMP" >
213-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
215                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
215-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
218                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
218-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
218-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
221                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
221-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
221-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
222            </intent-filter>
223            <intent-filter>
223-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
224                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
224-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
224-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
225            </intent-filter>
226        </receiver>
227    </application>
228
229</manifest>
