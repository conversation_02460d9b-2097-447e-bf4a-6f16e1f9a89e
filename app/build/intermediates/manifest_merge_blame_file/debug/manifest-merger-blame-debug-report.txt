1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livewallpaper.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
14
15    <!-- 位置权限 -->
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
18
19    <!-- 读取外部存储权限（用于自定义壁纸） -->
20    <uses-permission
20-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
22        android:maxSdkVersion="32" />
22-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
24
25    <!-- 通知监听权限（用于音乐可视化） -->
26    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
27
28    <!-- 壁纸权限 -->
29    <uses-permission android:name="android.permission.BIND_WALLPAPER" />
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
30
31    <permission
31-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
32        android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
36
37    <application
37-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
38        android:name="com.livewallpaper.app.MainApplication"
38-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:9-40
39        android:allowBackup="true"
39-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:26:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
41        android:dataExtractionRules="@xml/data_extraction_rules"
41-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:9-65
42        android:debuggable="true"
43        android:extractNativeLibs="false"
44        android:fullBackupContent="@xml/backup_rules"
44-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-54
45        android:icon="@mipmap/ic_launcher"
45-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-43
46        android:label="@string/app_name"
46-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-41
47        android:roundIcon="@mipmap/ic_launcher_round"
47-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
48        android:supportsRtl="true"
48-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-35
49        android:testOnly="true"
50        android:theme="@style/Theme.LiveWallpaper" >
50-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-51
51
52        <!-- 主Activity -->
53        <activity
53-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:37:9-46:20
54            android:name="com.livewallpaper.app.MainActivity"
54-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:38:13-41
55            android:exported="true"
55-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:39:13-36
56            android:label="@string/app_name"
56-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:13-45
57            android:theme="@style/Theme.LiveWallpaper" >
57-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-55
58            <intent-filter>
58-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-45:29
59                <action android:name="android.intent.action.MAIN" />
59-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
59-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
60
61                <category android:name="android.intent.category.LAUNCHER" />
61-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:17-77
61-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:27-74
62            </intent-filter>
63        </activity>
64
65        <!-- 壁纸预览Activity -->
66        <activity
66-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:49:9-57:20
67            android:name="com.livewallpaper.app.WallpaperPreviewActivity"
67-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:50:13-53
68            android:exported="true"
68-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:51:13-36
69            android:label="@string/wallpaper_preview"
69-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:13-54
70            android:theme="@style/Theme.LiveWallpaper" >
70-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-55
71            <intent-filter>
71-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-56:29
72                <action android:name="android.intent.action.MAIN" />
72-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
72-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
73            </intent-filter>
74        </activity>
75
76        <!-- 时间系统测试Activity -->
77        <activity
77-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:60:9-64:58
78            android:name="com.livewallpaper.app.TimeTestActivity"
78-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:61:13-45
79            android:exported="false"
79-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:62:13-37
80            android:label="时间系统测试"
80-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:13-35
81            android:theme="@style/Theme.LiveWallpaper" />
81-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-55
82
83        <!-- 场景系统测试Activity -->
84        <activity
84-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:9-71:58
85            android:name="com.livewallpaper.app.SceneTestActivity"
85-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:68:13-46
86            android:exported="false"
86-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:69:13-37
87            android:label="场景系统测试"
87-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:13-35
88            android:theme="@style/Theme.LiveWallpaper" />
88-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-55
89
90        <!-- 天气系统测试Activity -->
91        <activity
91-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:9-78:58
92            android:name="com.livewallpaper.app.WeatherTestActivity"
92-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:75:13-48
93            android:exported="false"
93-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:76:13-37
94            android:label="天气系统测试"
94-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:13-35
95            android:theme="@style/Theme.LiveWallpaper" />
95-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-55
96
97        <!-- 音乐系统测试Activity -->
98        <activity
98-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:9-85:58
99            android:name="com.livewallpaper.app.MusicTestActivity"
99-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:82:13-46
100            android:exported="false"
100-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:83:13-37
101            android:label="音乐系统测试"
101-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:13-35
102            android:theme="@style/Theme.LiveWallpaper" />
102-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-55
103
104        <!-- 设置Activity -->
105        <activity
105-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:9-92:58
106            android:name="com.livewallpaper.app.SettingsActivity"
106-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:89:13-45
107            android:exported="false"
107-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:90:13-37
108            android:label="设置"
108-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:13-31
109            android:theme="@style/Theme.LiveWallpaper" />
109-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-55
110
111        <!-- 性能测试Activity -->
112        <activity
112-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:9-99:58
113            android:name="com.livewallpaper.app.PerformanceTestActivity"
113-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:96:13-52
114            android:exported="false"
114-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:97:13-37
115            android:label="性能监控"
115-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:13-33
116            android:theme="@style/Theme.LiveWallpaper" />
116-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-55
117
118        <!-- 关于Activity -->
119        <activity
119-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:9-106:58
120            android:name="com.livewallpaper.app.AboutActivity"
120-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:103:13-42
121            android:exported="false"
121-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:104:13-37
122            android:label="关于"
122-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:13-31
123            android:theme="@style/Theme.LiveWallpaper" />
123-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-55
124
125        <!-- 隐私政策Activity -->
126        <activity
126-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:9-113:58
127            android:name="com.livewallpaper.app.PrivacyPolicyActivity"
127-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:110:13-50
128            android:exported="false"
128-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:111:13-37
129            android:label="隐私政策"
129-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:13-33
130            android:theme="@style/Theme.LiveWallpaper" />
130-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-55
131
132        <!-- 服务条款Activity -->
133        <activity
133-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:9-120:58
134            android:name="com.livewallpaper.app.TermsOfServiceActivity"
134-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:117:13-51
135            android:exported="false"
135-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:118:13-37
136            android:label="服务条款"
136-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:13-33
137            android:theme="@style/Theme.LiveWallpaper" />
137-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-55
138
139        <!-- 开源许可Activity -->
140        <activity
140-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:9-127:58
141            android:name="com.livewallpaper.app.OpenSourceLicensesActivity"
141-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:124:13-55
142            android:exported="false"
142-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:125:13-37
143            android:label="开源许可"
143-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:13-33
144            android:theme="@style/Theme.LiveWallpaper" />
144-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
145
146        <!-- 音乐通知监听服务 -->
147        <service
147-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:9-137:19
148            android:name="com.livewallpaper.core.service.MusicNotificationListenerService"
148-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:131:13-91
149            android:exported="false"
149-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:132:13-37
150            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
150-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:13-87
151            <intent-filter>
151-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-136:29
152                <action android:name="android.service.notification.NotificationListenerService" />
152-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:17-99
152-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:25-96
153            </intent-filter>
154        </service>
155
156        <!-- 壁纸服务 -->
157        <service
157-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-23:19
158            android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
158-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-85
159            android:enabled="true"
159-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-35
160            android:exported="true"
160-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-36
161            android:label="@string/wallpaper_service_label"
161-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-60
162            android:permission="android.permission.BIND_WALLPAPER" >
162-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-67
163            <intent-filter>
163-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:29
164                <action android:name="android.service.wallpaper.WallpaperService" />
164-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-85
164-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:25-82
165            </intent-filter>
166
167            <meta-data
167-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-22:53
168                android:name="android.service.wallpaper"
168-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:17-57
169                android:resource="@xml/wallpaper" />
169-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-50
170        </service>
171
172        <activity
172-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
173            android:name="com.google.android.gms.common.api.GoogleApiActivity"
173-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:19-85
174            android:exported="false"
174-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:22:19-43
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
175-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:21:19-78
176
177        <meta-data
177-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
178            android:name="com.google.android.gms.version"
178-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
179            android:value="@integer/google_play_services_version" />
179-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
180
181        <activity
181-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
182            android:name="androidx.compose.ui.tooling.PreviewActivity"
182-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
183            android:exported="true" />
183-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
184        <activity
184-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
185            android:name="androidx.activity.ComponentActivity"
185-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
186            android:exported="true" />
186-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
187
188        <provider
188-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
189            android:name="androidx.startup.InitializationProvider"
189-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
190            android:authorities="com.livewallpaper.app.debug.androidx-startup"
190-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
191            android:exported="false" >
191-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
192            <meta-data
192-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
193                android:name="androidx.emoji2.text.EmojiCompatInitializer"
193-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
194                android:value="androidx.startup" />
194-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
195            <meta-data
195-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
196                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
196-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
197                android:value="androidx.startup" />
197-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
198            <meta-data
198-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
199                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
199-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
200                android:value="androidx.startup" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
201        </provider>
202
203        <service
203-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
204            android:name="androidx.room.MultiInstanceInvalidationService"
204-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
205            android:directBootAware="true"
205-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
206            android:exported="false" />
206-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
207
208        <receiver
208-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
209            android:name="androidx.profileinstaller.ProfileInstallReceiver"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
210            android:directBootAware="false"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
211            android:enabled="true"
211-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
212            android:exported="true"
212-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
213            android:permission="android.permission.DUMP" >
213-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
215                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
215-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
218                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
218-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
218-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
221                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
221-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
221-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
222            </intent-filter>
223            <intent-filter>
223-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
224                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
224-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
224-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
225            </intent-filter>
226        </receiver>
227    </application>
228
229</manifest>
