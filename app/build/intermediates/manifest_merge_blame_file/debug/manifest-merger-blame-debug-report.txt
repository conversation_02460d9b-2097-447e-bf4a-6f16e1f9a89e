1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.livewallpaper.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
12-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
13-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
14
15    <!-- 位置权限 -->
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
16-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
17-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
18
19    <!-- 读取外部存储权限（用于自定义壁纸） -->
20    <uses-permission
20-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
22        android:maxSdkVersion="32" />
22-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
23-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
24
25    <!-- 通知监听权限（用于音乐可视化） -->
26    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
26-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
27
28    <!-- 壁纸权限 -->
29    <uses-permission android:name="android.permission.BIND_WALLPAPER" />
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
29-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
30
31    <!-- 计费权限 -->
32    <uses-permission android:name="com.android.vending.BILLING" />
32-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:5-67
32-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:22-64
33
34    <queries>
34-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:12:5-19:15
35        <intent>
35-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:13:9-15:18
36            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
36-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:14:13-91
36-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:14:21-88
37        </intent>
38        <intent>
38-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:16:9-18:18
39            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
39-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:17:13-116
39-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:17:21-113
40        </intent>
41        <!-- For browser content -->
42        <intent>
42-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:38:9-44:18
43            <action android:name="android.intent.action.VIEW" />
43-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:39:13-65
43-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:39:21-62
44
45            <category android:name="android.intent.category.BROWSABLE" />
45-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:41:13-74
45-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:41:23-71
46
47            <data android:scheme="https" />
47-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:13-44
47-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:19-41
48        </intent> <!-- End of browser content -->
49        <!-- For CustomTabsService -->
50        <intent>
50-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:47:9-49:18
51            <action android:name="android.support.customtabs.action.CustomTabsService" />
51-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:48:13-90
51-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:48:21-87
52        </intent> <!-- End of CustomTabsService -->
53        <!-- For MRAID capabilities -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:52:9-56:18
55            <action android:name="android.intent.action.INSERT" />
55-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:53:13-67
55-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:53:21-64
56
57            <data android:mimeType="vnd.android.cursor.dir/event" />
57-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:13-44
58        </intent>
59        <intent>
59-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:57:9-61:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:39:13-65
60-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:39:21-62
61
62            <data android:scheme="sms" />
62-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:13-44
62-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:19-41
63        </intent>
64        <intent>
64-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:62:9-66:18
65            <action android:name="android.intent.action.DIAL" />
65-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:63:13-65
65-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:63:21-62
66
67            <data android:path="tel:" />
67-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:13-44
68        </intent>
69    </queries>
70
71    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
71-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:26:5-79
71-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:26:22-76
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
72-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:27:5-82
72-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:27:22-79
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:28:5-88
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:28:22-85
74    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
74-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:29:5-83
74-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:29:22-80
75    <uses-permission android:name="android.permission.WAKE_LOCK" />
75-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:5-68
75-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:22-65
76    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
76-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:5-77
76-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:22-74
77
78    <permission
78-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
79        android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
79-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
80        android:protectionLevel="signature" />
80-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
81
82    <uses-permission android:name="com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
82-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
82-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
83
84    <application
84-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:5-156:19
85        android:name="com.livewallpaper.app.MainApplication"
85-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-40
86        android:allowBackup="true"
86-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-35
87        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
87-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
88        android:dataExtractionRules="@xml/data_extraction_rules"
88-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-65
89        android:debuggable="true"
90        android:extractNativeLibs="false"
91        android:fullBackupContent="@xml/backup_rules"
91-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
92        android:icon="@mipmap/ic_launcher"
92-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-43
93        android:label="@string/app_name"
93-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-41
94        android:roundIcon="@mipmap/ic_launcher_round"
94-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:34:9-54
95        android:supportsRtl="true"
95-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:35:9-35
96        android:theme="@style/Theme.LiveWallpaper" >
96-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:36:9-51
97
98        <!-- 主Activity -->
99        <activity
99-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:9-49:20
100            android:name="com.livewallpaper.app.MainActivity"
100-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-41
101            android:exported="true"
101-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-36
102            android:label="@string/app_name"
102-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:13-45
103            android:theme="@style/Theme.LiveWallpaper" >
103-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:13-55
104            <intent-filter>
104-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:45:13-48:29
105                <action android:name="android.intent.action.MAIN" />
105-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:46:17-69
105-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:46:25-66
106
107                <category android:name="android.intent.category.LAUNCHER" />
107-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:47:17-77
107-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:47:27-74
108            </intent-filter>
109        </activity>
110
111        <!-- 壁纸预览Activity -->
112        <activity
112-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:9-60:20
113            android:name="com.livewallpaper.app.WallpaperPreviewActivity"
113-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-53
114            android:exported="true"
114-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-36
115            android:label="@string/wallpaper_preview"
115-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:55:13-54
116            android:theme="@style/Theme.LiveWallpaper" >
116-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:56:13-55
117            <intent-filter>
117-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:57:13-59:29
118                <action android:name="android.intent.action.MAIN" />
118-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:46:17-69
118-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:46:25-66
119            </intent-filter>
120        </activity>
121
122        <!-- 时间系统测试Activity -->
123        <activity
123-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:9-67:58
124            android:name="com.livewallpaper.app.TimeTestActivity"
124-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-45
125            android:exported="false"
125-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:65:13-37
126            android:label="时间系统测试"
126-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:66:13-35
127            android:theme="@style/Theme.LiveWallpaper" />
127-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:13-55
128
129        <!-- 场景系统测试Activity -->
130        <activity
130-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:9-74:58
131            android:name="com.livewallpaper.app.SceneTestActivity"
131-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-46
132            android:exported="false"
132-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:72:13-37
133            android:label="场景系统测试"
133-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:73:13-35
134            android:theme="@style/Theme.LiveWallpaper" />
134-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:13-55
135
136        <!-- 天气系统测试Activity -->
137        <activity
137-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:9-81:58
138            android:name="com.livewallpaper.app.WeatherTestActivity"
138-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-48
139            android:exported="false"
139-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:79:13-37
140            android:label="天气系统测试"
140-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:80:13-35
141            android:theme="@style/Theme.LiveWallpaper" />
141-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:13-55
142
143        <!-- 音乐系统测试Activity -->
144        <activity
144-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:9-88:58
145            android:name="com.livewallpaper.app.MusicTestActivity"
145-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-46
146            android:exported="false"
146-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:86:13-37
147            android:label="音乐系统测试"
147-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:87:13-35
148            android:theme="@style/Theme.LiveWallpaper" />
148-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:13-55
149
150        <!-- 设置Activity -->
151        <activity
151-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:9-95:58
152            android:name="com.livewallpaper.app.SettingsActivity"
152-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-45
153            android:exported="false"
153-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:93:13-37
154            android:label="设置"
154-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:94:13-31
155            android:theme="@style/Theme.LiveWallpaper" />
155-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:13-55
156
157        <!-- 性能测试Activity -->
158        <activity
158-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:9-102:58
159            android:name="com.livewallpaper.app.PerformanceTestActivity"
159-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-52
160            android:exported="false"
160-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:100:13-37
161            android:label="性能监控"
161-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:101:13-33
162            android:theme="@style/Theme.LiveWallpaper" />
162-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:13-55
163
164        <!-- 关于Activity -->
165        <activity
165-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:9-109:58
166            android:name="com.livewallpaper.app.AboutActivity"
166-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-42
167            android:exported="false"
167-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:107:13-37
168            android:label="关于"
168-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:108:13-31
169            android:theme="@style/Theme.LiveWallpaper" />
169-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:13-55
170
171        <!-- 隐私政策Activity -->
172        <activity
172-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:9-116:58
173            android:name="com.livewallpaper.app.PrivacyPolicyActivity"
173-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-50
174            android:exported="false"
174-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:114:13-37
175            android:label="隐私政策"
175-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:115:13-33
176            android:theme="@style/Theme.LiveWallpaper" />
176-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:13-55
177
178        <!-- 服务条款Activity -->
179        <activity
179-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:9-123:58
180            android:name="com.livewallpaper.app.TermsOfServiceActivity"
180-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-51
181            android:exported="false"
181-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:121:13-37
182            android:label="服务条款"
182-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:122:13-33
183            android:theme="@style/Theme.LiveWallpaper" />
183-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:13-55
184
185        <!-- 开源许可Activity -->
186        <activity
186-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:9-130:58
187            android:name="com.livewallpaper.app.OpenSourceLicensesActivity"
187-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
188            android:exported="false"
188-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:128:13-37
189            android:label="开源许可"
189-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:129:13-33
190            android:theme="@style/Theme.LiveWallpaper" />
190-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:13-55
191
192        <!-- 高级功能Activity -->
193        <activity
193-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:9-137:58
194            android:name="com.livewallpaper.app.PremiumActivity"
194-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-44
195            android:exported="false"
195-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:13-37
196            android:label="高级功能"
196-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:136:13-33
197            android:theme="@style/Theme.LiveWallpaper" />
197-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:137:13-55
198
199        <!-- 自定义场景Activity -->
200        <activity
200-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:140:9-144:58
201            android:name="com.livewallpaper.app.CustomSceneActivity"
201-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:141:13-48
202            android:exported="false"
202-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:142:13-37
203            android:label="自定义场景"
203-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:143:13-34
204            android:theme="@style/Theme.LiveWallpaper" />
204-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:144:13-55
205
206        <!-- 音乐通知监听服务 -->
207        <service
207-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:147:9-154:19
208            android:name="com.livewallpaper.core.service.MusicNotificationListenerService"
208-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:148:13-91
209            android:exported="false"
209-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:149:13-37
210            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
210-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:150:13-87
211            <intent-filter>
211-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:151:13-153:29
212                <action android:name="android.service.notification.NotificationListenerService" />
212-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:152:17-99
212-->/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:152:25-96
213            </intent-filter>
214        </service>
215
216        <!-- 壁纸服务 -->
217        <service
217-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-23:19
218            android:name="com.livewallpaper.features.wallpaper.LiveWallpaperService"
218-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-85
219            android:enabled="true"
219-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-35
220            android:exported="true"
220-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-36
221            android:label="@string/wallpaper_service_label"
221-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-60
222            android:permission="android.permission.BIND_WALLPAPER" >
222-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-67
223            <intent-filter>
223-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:29
224                <action android:name="android.service.wallpaper.WallpaperService" />
224-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-85
224-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:25-82
225            </intent-filter>
226
227            <meta-data
227-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-22:53
228                android:name="android.service.wallpaper"
228-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:17-57
229                android:resource="@xml/wallpaper" />
229-->[:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-50
230        </service>
231
232        <meta-data
232-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:22:9-24:37
233            android:name="com.google.android.play.billingclient.version"
233-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:23:13-73
234            android:value="7.1.1" />
234-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:24:13-34
235
236        <activity
236-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:26:9-30:75
237            android:name="com.android.billingclient.api.ProxyBillingActivity"
237-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:27:13-78
238            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
238-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:28:13-96
239            android:exported="false"
239-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:29:13-37
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
240-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:30:13-72
241        <activity
241-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:31:9-35:75
242            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
242-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:32:13-80
243            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
243-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:33:13-96
244            android:exported="false"
244-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:34:13-37
245            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
245-->[com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:35:13-72
246        <activity
246-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:73:9-78:43
247            android:name="com.google.android.gms.ads.AdActivity"
247-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:74:13-65
248            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
248-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:75:13-122
249            android:exported="false"
249-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:76:13-37
250            android:theme="@android:style/Theme.Translucent" />
250-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:77:13-61
251
252        <provider
252-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:80:9-85:43
253            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
253-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:81:13-76
254            android:authorities="com.livewallpaper.app.debug.mobileadsinitprovider"
254-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:82:13-73
255            android:exported="false"
255-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:83:13-37
256            android:initOrder="100" />
256-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:84:13-36
257
258        <service
258-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:87:9-91:43
259            android:name="com.google.android.gms.ads.AdService"
259-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:88:13-64
260            android:enabled="true"
260-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:89:13-35
261            android:exported="false" />
261-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:90:13-37
262
263        <activity
263-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:93:9-97:43
264            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
264-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:94:13-82
265            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
265-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:95:13-122
266            android:exported="false" />
266-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:96:13-37
267        <activity
267-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:98:9-105:43
268            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
268-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:99:13-82
269            android:excludeFromRecents="true"
269-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:100:13-46
270            android:exported="false"
270-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:101:13-37
271            android:launchMode="singleTask"
271-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:102:13-44
272            android:taskAffinity=""
272-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:103:13-36
273            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
273-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:104:13-72
274
275        <property
275-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:107:9-109:62
276            android:name="android.adservices.AD_SERVICES_CONFIG"
276-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:108:13-65
277            android:resource="@xml/gma_ad_services_config" />
277-->[com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:109:13-59
278
279        <activity
279-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
280            android:name="com.google.android.gms.common.api.GoogleApiActivity"
280-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
281            android:exported="false"
281-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
282            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
282-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
283
284        <meta-data
284-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
285            android:name="com.google.android.gms.version"
285-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
286            android:value="@integer/google_play_services_version" />
286-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
287
288        <provider
288-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:31:9-39:20
289            android:name="androidx.startup.InitializationProvider"
289-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:32:13-67
290            android:authorities="com.livewallpaper.app.debug.androidx-startup"
290-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:33:13-68
291            android:exported="false" >
291-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:34:13-37
292            <meta-data
292-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:36:13-38:52
293                android:name="androidx.work.WorkManagerInitializer"
293-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:37:17-68
294                android:value="androidx.startup" />
294-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:38:17-49
295            <meta-data
295-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
296                android:name="androidx.emoji2.text.EmojiCompatInitializer"
296-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
297                android:value="androidx.startup" />
297-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
298            <meta-data
298-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
299                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
299-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
300                android:value="androidx.startup" />
300-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
301            <meta-data
301-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
302                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
302-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
303                android:value="androidx.startup" />
303-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
304        </provider>
305
306        <service
306-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:41:9-46:35
307            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
307-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:42:13-88
308            android:directBootAware="false"
308-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:43:13-44
309            android:enabled="@bool/enable_system_alarm_service_default"
309-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:44:13-72
310            android:exported="false" />
310-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:45:13-37
311        <service
311-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:47:9-53:35
312            android:name="androidx.work.impl.background.systemjob.SystemJobService"
312-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:48:13-84
313            android:directBootAware="false"
313-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:49:13-44
314            android:enabled="@bool/enable_system_job_service_default"
314-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:50:13-70
315            android:exported="true"
315-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:51:13-36
316            android:permission="android.permission.BIND_JOB_SERVICE" />
316-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:52:13-69
317        <service
317-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:54:9-59:35
318            android:name="androidx.work.impl.foreground.SystemForegroundService"
318-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:55:13-81
319            android:directBootAware="false"
319-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:56:13-44
320            android:enabled="@bool/enable_system_foreground_service_default"
320-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:57:13-77
321            android:exported="false" />
321-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:58:13-37
322
323        <receiver
323-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:61:9-66:35
324            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
324-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:62:13-88
325            android:directBootAware="false"
325-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:63:13-44
326            android:enabled="true"
326-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:64:13-35
327            android:exported="false" />
327-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:65:13-37
328        <receiver
328-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:67:9-77:20
329            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
329-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:68:13-106
330            android:directBootAware="false"
330-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:69:13-44
331            android:enabled="false"
331-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:70:13-36
332            android:exported="false" >
332-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:71:13-37
333            <intent-filter>
333-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:73:13-76:29
334                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
334-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:17-87
334-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:25-84
335                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
335-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:17-90
335-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:25-87
336            </intent-filter>
337        </receiver>
338        <receiver
338-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:78:9-88:20
339            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
339-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:79:13-104
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:80:13-44
341            android:enabled="false"
341-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:81:13-36
342            android:exported="false" >
342-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:82:13-37
343            <intent-filter>
343-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:84:13-87:29
344                <action android:name="android.intent.action.BATTERY_OKAY" />
344-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:17-77
344-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:25-74
345                <action android:name="android.intent.action.BATTERY_LOW" />
345-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:17-76
345-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:25-73
346            </intent-filter>
347        </receiver>
348        <receiver
348-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:89:9-99:20
349            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
349-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:90:13-104
350            android:directBootAware="false"
350-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:91:13-44
351            android:enabled="false"
351-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:92:13-36
352            android:exported="false" >
352-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:93:13-37
353            <intent-filter>
353-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:95:13-98:29
354                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
354-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:17-83
354-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:25-80
355                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
355-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:17-82
355-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:25-79
356            </intent-filter>
357        </receiver>
358        <receiver
358-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:100:9-109:20
359            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
359-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:101:13-103
360            android:directBootAware="false"
360-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:102:13-44
361            android:enabled="false"
361-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:103:13-36
362            android:exported="false" >
362-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:104:13-37
363            <intent-filter>
363-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:106:13-108:29
364                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
364-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:17-79
364-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:25-76
365            </intent-filter>
366        </receiver>
367        <receiver
367-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:110:9-121:20
368            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
368-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:111:13-88
369            android:directBootAware="false"
369-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:112:13-44
370            android:enabled="false"
370-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:113:13-36
371            android:exported="false" >
371-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:114:13-37
372            <intent-filter>
372-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:116:13-120:29
373                <action android:name="android.intent.action.BOOT_COMPLETED" />
373-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:17-79
373-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:25-76
374                <action android:name="android.intent.action.TIME_SET" />
374-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:17-73
374-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:25-70
375                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
375-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:17-81
375-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:25-78
376            </intent-filter>
377        </receiver>
378        <receiver
378-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:122:9-131:20
379            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
379-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:123:13-99
380            android:directBootAware="false"
380-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:124:13-44
381            android:enabled="@bool/enable_system_alarm_service_default"
381-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:125:13-72
382            android:exported="false" >
382-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:126:13-37
383            <intent-filter>
383-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:128:13-130:29
384                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
384-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:17-98
384-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:25-95
385            </intent-filter>
386        </receiver>
387        <receiver
387-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:132:9-142:20
388            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
388-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:133:13-78
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:134:13-44
390            android:enabled="true"
390-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:135:13-35
391            android:exported="true"
391-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:136:13-36
392            android:permission="android.permission.DUMP" >
392-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:137:13-57
393            <intent-filter>
393-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:139:13-141:29
394                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
394-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:17-88
394-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:25-85
395            </intent-filter>
396        </receiver>
397
398        <activity
398-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
399            android:name="androidx.compose.ui.tooling.PreviewActivity"
399-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
400            android:exported="true" />
400-->[androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
401        <activity
401-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
402            android:name="androidx.activity.ComponentActivity"
402-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
403            android:exported="true" />
403-->[androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
404
405        <uses-library
405-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
406            android:name="android.ext.adservices"
406-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
407            android:required="false" />
407-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
408
409        <service
409-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
410            android:name="androidx.room.MultiInstanceInvalidationService"
410-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
411            android:directBootAware="true"
411-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
412            android:exported="false" />
412-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
413
414        <receiver
414-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
415            android:name="androidx.profileinstaller.ProfileInstallReceiver"
415-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
416            android:directBootAware="false"
416-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
417            android:enabled="true"
417-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
418            android:exported="true"
418-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
419            android:permission="android.permission.DUMP" >
419-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
420            <intent-filter>
420-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
421                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
421-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
421-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
422            </intent-filter>
423            <intent-filter>
423-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
424                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
424-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
424-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
425            </intent-filter>
426            <intent-filter>
426-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
427                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
427-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
427-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
428            </intent-filter>
429            <intent-filter>
429-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
430                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
430-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
430-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
431            </intent-filter>
432        </receiver>
433
434        <service
434-->[com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:28:9-34:19
435            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
435-->[com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:29:13-103
436            android:exported="false" >
436-->[com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:30:13-37
437            <meta-data
437-->[com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:31:13-33:39
438                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
438-->[com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:32:17-94
439                android:value="cct" />
439-->[com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:33:17-36
440        </service>
441        <service
441-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:26:9-30:19
442            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
442-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:27:13-117
443            android:exported="false"
443-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:28:13-37
444            android:permission="android.permission.BIND_JOB_SERVICE" >
444-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:29:13-69
445        </service>
446
447        <receiver
447-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:32:9-34:40
448            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
448-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:33:13-132
449            android:exported="false" />
449-->[com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:34:13-37
450    </application>
451
452</manifest>
