<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":features:music" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":features:weather" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":features:wallpaper" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/library_assets/debug/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/assets"><file name="scenes/README.md" path="/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/assets/scenes/README.md"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/LiveWallpaper/app/build/intermediates/shader_assets/debug/out"/></dataSet></merger>