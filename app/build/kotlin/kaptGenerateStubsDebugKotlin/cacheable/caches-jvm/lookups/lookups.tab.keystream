  Manifest android  Activity android.app  Application android.app  WallpaperManager android.app  Bundle android.app.Activity  
ComponentName android.content  Intent android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  PackageManager android.content.pm  Uri android.net  Build 
android.os  Bundle 
android.os  Settings android.provider  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  Image androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  ClosedFloatingPointRange "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Scene "androidx.compose.foundation.layout  
SceneCategory "androidx.compose.foundation.layout  Season "androidx.compose.foundation.layout  
SettingOption "androidx.compose.foundation.layout  SettingsCategory "androidx.compose.foundation.layout  	TimeOfDay "androidx.compose.foundation.layout  WallpaperSettings "androidx.compose.foundation.layout  WeatherType "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Icons androidx.compose.material.icons  	ArrowBack &androidx.compose.material.icons.filled  ClosedFloatingPointRange &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  
ExpandLess &androidx.compose.material.icons.filled  
ExpandMore &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  
SettingOption &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsCategory &androidx.compose.material.icons.filled  WallpaperSettings &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  ClosedFloatingPointRange androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Scene androidx.compose.material3  
SceneCategory androidx.compose.material3  Season androidx.compose.material3  
SettingOption androidx.compose.material3  SettingsCategory androidx.compose.material3  	TimeOfDay androidx.compose.material3  
Typography androidx.compose.material3  WallpaperSettings androidx.compose.material3  WeatherType androidx.compose.material3  androidx androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  ClosedFloatingPointRange androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Scene androidx.compose.runtime  
SceneCategory androidx.compose.runtime  Season androidx.compose.runtime  
SettingOption androidx.compose.runtime  SettingsCategory androidx.compose.runtime  
SideEffect androidx.compose.runtime  	TimeOfDay androidx.compose.runtime  WallpaperSettings androidx.compose.runtime  WeatherType androidx.compose.runtime  androidx androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  WindowCompat androidx.core.view  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Any androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  DirtyRegionManager androidx.lifecycle.ViewModel  DirtyRegionStats androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LocationManager androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  MusicManager androidx.lifecycle.ViewModel  
MusicState androidx.lifecycle.ViewModel  MusicVisualizationData androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  ObjectPoolManager androidx.lifecycle.ViewModel  OptimizedRenderSettings androidx.lifecycle.ViewModel  PerformanceLevel androidx.lifecycle.ViewModel  PerformanceMetrics androidx.lifecycle.ViewModel  PerformanceMonitor androidx.lifecycle.ViewModel  PerformanceOptimizer androidx.lifecycle.ViewModel  Scene androidx.lifecycle.ViewModel  SceneInitializer androidx.lifecycle.ViewModel  SceneManager androidx.lifecycle.ViewModel  SceneRepository androidx.lifecycle.ViewModel  
SceneState androidx.lifecycle.ViewModel  SettingsRepository androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  WallpaperSettings androidx.lifecycle.ViewModel  WallpaperTimeManager androidx.lifecycle.ViewModel  WallpaperTimeState androidx.lifecycle.ViewModel  WeatherForecast androidx.lifecycle.ViewModel  WeatherManager androidx.lifecycle.ViewModel  WeatherState androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  emptyMap androidx.lifecycle.ViewModel  flow androidx.lifecycle.ViewModel  kotlinx androidx.lifecycle.ViewModel  locationManager androidx.lifecycle.ViewModel  map androidx.lifecycle.ViewModel  musicManager androidx.lifecycle.ViewModel  settingsRepository androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  weatherManager androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  
AboutActivity com.livewallpaper.app  AboutScreen com.livewallpaper.app  AdvancedSettings com.livewallpaper.app  Any com.livewallpaper.app  ApiKeyStatusCard com.livewallpaper.app  Boolean com.livewallpaper.app  ClosedFloatingPointRange com.livewallpaper.app  
Composable com.livewallpaper.app  ContactItem com.livewallpaper.app  CurrentSceneCard com.livewallpaper.app  DirtyRegionManager com.livewallpaper.app  DirtyRegionStats com.livewallpaper.app  DirtyRegionStatsCard com.livewallpaper.app  DisplaySettings com.livewallpaper.app  	ErrorCard com.livewallpaper.app  ExperimentalMaterial3Api com.livewallpaper.app  FeatureItem com.livewallpaper.app  Float com.livewallpaper.app  ForecastItem com.livewallpaper.app  GeneralSettings com.livewallpaper.app  InfoRow com.livewallpaper.app  Int com.livewallpaper.app  LicenseItem com.livewallpaper.app  List com.livewallpaper.app  LoadingCard com.livewallpaper.app  LocationDetailsCard com.livewallpaper.app  MainActivity com.livewallpaper.app  MainApplication com.livewallpaper.app  
MainScreen com.livewallpaper.app  MainScreenPreview com.livewallpaper.app  Map com.livewallpaper.app  
MetricItem com.livewallpaper.app  MusicDetailsCard com.livewallpaper.app  
MusicSettings com.livewallpaper.app  
MusicState com.livewallpaper.app  MusicStateCard com.livewallpaper.app  MusicTestActivity com.livewallpaper.app  MusicTestScreen com.livewallpaper.app  MusicTestViewModel com.livewallpaper.app  MusicVisualizationData com.livewallpaper.app  MutableStateFlow com.livewallpaper.app  NoMusicCard com.livewallpaper.app  NotificationPermissionCard com.livewallpaper.app  ObjectPoolManager com.livewallpaper.app  ObjectPoolStatsCard com.livewallpaper.app  OpenSourceLicense com.livewallpaper.app  OpenSourceLicensesActivity com.livewallpaper.app  OpenSourceLicensesScreen com.livewallpaper.app  OptIn com.livewallpaper.app  OptimizedRenderSettings com.livewallpaper.app  OptimizedSettingsCard com.livewallpaper.app  PerformanceLevel com.livewallpaper.app  PerformanceLevelCard com.livewallpaper.app  PerformanceMetrics com.livewallpaper.app  PerformanceMetricsCard com.livewallpaper.app  PerformanceMonitor com.livewallpaper.app  PerformanceOptimizer com.livewallpaper.app  PerformanceRecommendationsCard com.livewallpaper.app  PerformanceSettings com.livewallpaper.app  PerformanceTestActivity com.livewallpaper.app  PerformanceTestScreen com.livewallpaper.app  PerformanceTestViewModel com.livewallpaper.app  PermissionStatusCard com.livewallpaper.app  
PolicySection com.livewallpaper.app  PrivacyPolicyActivity com.livewallpaper.app  PrivacyPolicyScreen com.livewallpaper.app  Scene com.livewallpaper.app  
SceneCategory com.livewallpaper.app  	SceneItem com.livewallpaper.app  
SceneSettings com.livewallpaper.app  
SceneState com.livewallpaper.app  SceneStatsCard com.livewallpaper.app  SceneTestActivity com.livewallpaper.app  SceneTestScreen com.livewallpaper.app  SceneTestViewModel com.livewallpaper.app  ScenesListCard com.livewallpaper.app  Season com.livewallpaper.app  SelectionSetting com.livewallpaper.app  
SettingOption com.livewallpaper.app  
SettingRow com.livewallpaper.app  SettingsActivity com.livewallpaper.app  SettingsCategory com.livewallpaper.app  SettingsCategoryCard com.livewallpaper.app  SettingsScreen com.livewallpaper.app  SettingsStatsCard com.livewallpaper.app  SettingsViewModel com.livewallpaper.app  SharingStarted com.livewallpaper.app  
SliderSetting com.livewallpaper.app  SolarEventsCard com.livewallpaper.app  StatItem com.livewallpaper.app  	StateFlow com.livewallpaper.app  String com.livewallpaper.app  SupportedAppsCard com.livewallpaper.app  
SwitchSetting com.livewallpaper.app  TermsOfServiceActivity com.livewallpaper.app  TermsOfServiceScreen com.livewallpaper.app  
ThemeSettings com.livewallpaper.app  TimeDetailsCard com.livewallpaper.app  	TimeOfDay com.livewallpaper.app  TimeSettings com.livewallpaper.app  
TimeStateCard com.livewallpaper.app  TimeTestActivity com.livewallpaper.app  TimeTestScreen com.livewallpaper.app  TimeTestViewModel com.livewallpaper.app  Unit com.livewallpaper.app  VisualizationDataCard com.livewallpaper.app  WallpaperPreviewActivity com.livewallpaper.app  WallpaperPreviewScreen com.livewallpaper.app  WallpaperSettings com.livewallpaper.app  WallpaperTimeState com.livewallpaper.app  WeatherDetailsCard com.livewallpaper.app  WeatherForecastCard com.livewallpaper.app  WeatherSettings com.livewallpaper.app  WeatherState com.livewallpaper.app  WeatherStateCard com.livewallpaper.app  WeatherTestActivity com.livewallpaper.app  WeatherTestScreen com.livewallpaper.app  WeatherTestViewModel com.livewallpaper.app  WeatherType com.livewallpaper.app  androidx com.livewallpaper.app  asStateFlow com.livewallpaper.app  	emptyList com.livewallpaper.app  emptyMap com.livewallpaper.app  flow com.livewallpaper.app  
formatDate com.livewallpaper.app  
formatTime com.livewallpaper.app  formatTimestamp com.livewallpaper.app  getCategoryDisplayName com.livewallpaper.app  getOpenSourceLicenses com.livewallpaper.app  getPhaseDisplayName com.livewallpaper.app  getSeasonDisplayName com.livewallpaper.app  getTimeOfDayDisplayName com.livewallpaper.app  getWeatherTypeDisplayName com.livewallpaper.app  kotlinx com.livewallpaper.app  locationManager com.livewallpaper.app  map com.livewallpaper.app  musicManager com.livewallpaper.app  settingsRepository com.livewallpaper.app  stateIn com.livewallpaper.app  viewModelScope com.livewallpaper.app  weatherManager com.livewallpaper.app  Bundle #com.livewallpaper.app.AboutActivity  Bundle "com.livewallpaper.app.MainActivity  Bundle 'com.livewallpaper.app.MusicTestActivity  Boolean (com.livewallpaper.app.MusicTestViewModel  Inject (com.livewallpaper.app.MusicTestViewModel  MusicManager (com.livewallpaper.app.MusicTestViewModel  
MusicState (com.livewallpaper.app.MusicTestViewModel  MusicVisualizationData (com.livewallpaper.app.MusicTestViewModel  SharingStarted (com.livewallpaper.app.MusicTestViewModel  	StateFlow (com.livewallpaper.app.MusicTestViewModel  flow (com.livewallpaper.app.MusicTestViewModel  getFLOW (com.livewallpaper.app.MusicTestViewModel  getFlow (com.livewallpaper.app.MusicTestViewModel  
getKOTLINX (com.livewallpaper.app.MusicTestViewModel  
getKotlinx (com.livewallpaper.app.MusicTestViewModel  
getSTATEIn (com.livewallpaper.app.MusicTestViewModel  
getStateIn (com.livewallpaper.app.MusicTestViewModel  getVIEWModelScope (com.livewallpaper.app.MusicTestViewModel  getViewModelScope (com.livewallpaper.app.MusicTestViewModel  kotlinx (com.livewallpaper.app.MusicTestViewModel  musicManager (com.livewallpaper.app.MusicTestViewModel  stateIn (com.livewallpaper.app.MusicTestViewModel  viewModelScope (com.livewallpaper.app.MusicTestViewModel  String 'com.livewallpaper.app.OpenSourceLicense  Bundle 0com.livewallpaper.app.OpenSourceLicensesActivity  Bundle -com.livewallpaper.app.PerformanceTestActivity  DirtyRegionManager .com.livewallpaper.app.PerformanceTestViewModel  DirtyRegionStats .com.livewallpaper.app.PerformanceTestViewModel  Inject .com.livewallpaper.app.PerformanceTestViewModel  Int .com.livewallpaper.app.PerformanceTestViewModel  List .com.livewallpaper.app.PerformanceTestViewModel  Map .com.livewallpaper.app.PerformanceTestViewModel  MutableStateFlow .com.livewallpaper.app.PerformanceTestViewModel  ObjectPoolManager .com.livewallpaper.app.PerformanceTestViewModel  OptimizedRenderSettings .com.livewallpaper.app.PerformanceTestViewModel  PerformanceLevel .com.livewallpaper.app.PerformanceTestViewModel  PerformanceMetrics .com.livewallpaper.app.PerformanceTestViewModel  PerformanceMonitor .com.livewallpaper.app.PerformanceTestViewModel  PerformanceOptimizer .com.livewallpaper.app.PerformanceTestViewModel  SharingStarted .com.livewallpaper.app.PerformanceTestViewModel  	StateFlow .com.livewallpaper.app.PerformanceTestViewModel  String .com.livewallpaper.app.PerformanceTestViewModel  _dirtyRegionStats .com.livewallpaper.app.PerformanceTestViewModel  _objectPoolStats .com.livewallpaper.app.PerformanceTestViewModel  _optimizedSettings .com.livewallpaper.app.PerformanceTestViewModel  _recommendations .com.livewallpaper.app.PerformanceTestViewModel  asStateFlow .com.livewallpaper.app.PerformanceTestViewModel  	emptyList .com.livewallpaper.app.PerformanceTestViewModel  emptyMap .com.livewallpaper.app.PerformanceTestViewModel  getASStateFlow .com.livewallpaper.app.PerformanceTestViewModel  getAsStateFlow .com.livewallpaper.app.PerformanceTestViewModel  getEMPTYList .com.livewallpaper.app.PerformanceTestViewModel  getEMPTYMap .com.livewallpaper.app.PerformanceTestViewModel  getEmptyList .com.livewallpaper.app.PerformanceTestViewModel  getEmptyMap .com.livewallpaper.app.PerformanceTestViewModel  getMAP .com.livewallpaper.app.PerformanceTestViewModel  getMap .com.livewallpaper.app.PerformanceTestViewModel  
getSTATEIn .com.livewallpaper.app.PerformanceTestViewModel  
getStateIn .com.livewallpaper.app.PerformanceTestViewModel  getVIEWModelScope .com.livewallpaper.app.PerformanceTestViewModel  getViewModelScope .com.livewallpaper.app.PerformanceTestViewModel  map .com.livewallpaper.app.PerformanceTestViewModel  performanceMetrics .com.livewallpaper.app.PerformanceTestViewModel  performanceMonitor .com.livewallpaper.app.PerformanceTestViewModel  stateIn .com.livewallpaper.app.PerformanceTestViewModel  viewModelScope .com.livewallpaper.app.PerformanceTestViewModel  Bundle +com.livewallpaper.app.PrivacyPolicyActivity  Bundle 'com.livewallpaper.app.SceneTestActivity  Inject (com.livewallpaper.app.SceneTestViewModel  List (com.livewallpaper.app.SceneTestViewModel  MutableStateFlow (com.livewallpaper.app.SceneTestViewModel  Scene (com.livewallpaper.app.SceneTestViewModel  SceneInitializer (com.livewallpaper.app.SceneTestViewModel  SceneManager (com.livewallpaper.app.SceneTestViewModel  SceneRepository (com.livewallpaper.app.SceneTestViewModel  
SceneState (com.livewallpaper.app.SceneTestViewModel  	StateFlow (com.livewallpaper.app.SceneTestViewModel  String (com.livewallpaper.app.SceneTestViewModel  
_allScenes (com.livewallpaper.app.SceneTestViewModel  _sceneState (com.livewallpaper.app.SceneTestViewModel  asStateFlow (com.livewallpaper.app.SceneTestViewModel  	emptyList (com.livewallpaper.app.SceneTestViewModel  getASStateFlow (com.livewallpaper.app.SceneTestViewModel  getAsStateFlow (com.livewallpaper.app.SceneTestViewModel  getEMPTYList (com.livewallpaper.app.SceneTestViewModel  getEmptyList (com.livewallpaper.app.SceneTestViewModel  Bundle &com.livewallpaper.app.SettingsActivity  Any 'com.livewallpaper.app.SettingsViewModel  Boolean 'com.livewallpaper.app.SettingsViewModel  Inject 'com.livewallpaper.app.SettingsViewModel  List 'com.livewallpaper.app.SettingsViewModel  Map 'com.livewallpaper.app.SettingsViewModel  MutableStateFlow 'com.livewallpaper.app.SettingsViewModel  SettingsRepository 'com.livewallpaper.app.SettingsViewModel  SharingStarted 'com.livewallpaper.app.SettingsViewModel  	StateFlow 'com.livewallpaper.app.SettingsViewModel  String 'com.livewallpaper.app.SettingsViewModel  WallpaperSettings 'com.livewallpaper.app.SettingsViewModel  
_isLoading 'com.livewallpaper.app.SettingsViewModel  _validationErrors 'com.livewallpaper.app.SettingsViewModel  asStateFlow 'com.livewallpaper.app.SettingsViewModel  	emptyList 'com.livewallpaper.app.SettingsViewModel  emptyMap 'com.livewallpaper.app.SettingsViewModel  flow 'com.livewallpaper.app.SettingsViewModel  getASStateFlow 'com.livewallpaper.app.SettingsViewModel  getAsStateFlow 'com.livewallpaper.app.SettingsViewModel  getEMPTYList 'com.livewallpaper.app.SettingsViewModel  getEMPTYMap 'com.livewallpaper.app.SettingsViewModel  getEmptyList 'com.livewallpaper.app.SettingsViewModel  getEmptyMap 'com.livewallpaper.app.SettingsViewModel  getFLOW 'com.livewallpaper.app.SettingsViewModel  getFlow 'com.livewallpaper.app.SettingsViewModel  
getKOTLINX 'com.livewallpaper.app.SettingsViewModel  
getKotlinx 'com.livewallpaper.app.SettingsViewModel  
getSTATEIn 'com.livewallpaper.app.SettingsViewModel  
getStateIn 'com.livewallpaper.app.SettingsViewModel  getVIEWModelScope 'com.livewallpaper.app.SettingsViewModel  getViewModelScope 'com.livewallpaper.app.SettingsViewModel  kotlinx 'com.livewallpaper.app.SettingsViewModel  settingsRepository 'com.livewallpaper.app.SettingsViewModel  stateIn 'com.livewallpaper.app.SettingsViewModel  viewModelScope 'com.livewallpaper.app.SettingsViewModel  Bundle ,com.livewallpaper.app.TermsOfServiceActivity  Bundle &com.livewallpaper.app.TimeTestActivity  Boolean 'com.livewallpaper.app.TimeTestViewModel  Inject 'com.livewallpaper.app.TimeTestViewModel  LocationManager 'com.livewallpaper.app.TimeTestViewModel  MutableStateFlow 'com.livewallpaper.app.TimeTestViewModel  SharingStarted 'com.livewallpaper.app.TimeTestViewModel  	StateFlow 'com.livewallpaper.app.TimeTestViewModel  WallpaperTimeManager 'com.livewallpaper.app.TimeTestViewModel  WallpaperTimeState 'com.livewallpaper.app.TimeTestViewModel  
_timeState 'com.livewallpaper.app.TimeTestViewModel  asStateFlow 'com.livewallpaper.app.TimeTestViewModel  flow 'com.livewallpaper.app.TimeTestViewModel  getASStateFlow 'com.livewallpaper.app.TimeTestViewModel  getAsStateFlow 'com.livewallpaper.app.TimeTestViewModel  getFLOW 'com.livewallpaper.app.TimeTestViewModel  getFlow 'com.livewallpaper.app.TimeTestViewModel  
getKOTLINX 'com.livewallpaper.app.TimeTestViewModel  
getKotlinx 'com.livewallpaper.app.TimeTestViewModel  
getSTATEIn 'com.livewallpaper.app.TimeTestViewModel  
getStateIn 'com.livewallpaper.app.TimeTestViewModel  getVIEWModelScope 'com.livewallpaper.app.TimeTestViewModel  getViewModelScope 'com.livewallpaper.app.TimeTestViewModel  kotlinx 'com.livewallpaper.app.TimeTestViewModel  locationManager 'com.livewallpaper.app.TimeTestViewModel  stateIn 'com.livewallpaper.app.TimeTestViewModel  viewModelScope 'com.livewallpaper.app.TimeTestViewModel  Bundle .com.livewallpaper.app.WallpaperPreviewActivity  Bundle )com.livewallpaper.app.WeatherTestActivity  Boolean *com.livewallpaper.app.WeatherTestViewModel  Inject *com.livewallpaper.app.WeatherTestViewModel  List *com.livewallpaper.app.WeatherTestViewModel  LocationManager *com.livewallpaper.app.WeatherTestViewModel  MutableStateFlow *com.livewallpaper.app.WeatherTestViewModel  SharingStarted *com.livewallpaper.app.WeatherTestViewModel  	StateFlow *com.livewallpaper.app.WeatherTestViewModel  String *com.livewallpaper.app.WeatherTestViewModel  WeatherForecast *com.livewallpaper.app.WeatherTestViewModel  WeatherManager *com.livewallpaper.app.WeatherTestViewModel  WeatherState *com.livewallpaper.app.WeatherTestViewModel  _weatherForecast *com.livewallpaper.app.WeatherTestViewModel  
_weatherState *com.livewallpaper.app.WeatherTestViewModel  asStateFlow *com.livewallpaper.app.WeatherTestViewModel  	emptyList *com.livewallpaper.app.WeatherTestViewModel  flow *com.livewallpaper.app.WeatherTestViewModel  getASStateFlow *com.livewallpaper.app.WeatherTestViewModel  getAsStateFlow *com.livewallpaper.app.WeatherTestViewModel  getEMPTYList *com.livewallpaper.app.WeatherTestViewModel  getEmptyList *com.livewallpaper.app.WeatherTestViewModel  getFLOW *com.livewallpaper.app.WeatherTestViewModel  getFlow *com.livewallpaper.app.WeatherTestViewModel  
getKOTLINX *com.livewallpaper.app.WeatherTestViewModel  
getKotlinx *com.livewallpaper.app.WeatherTestViewModel  
getSTATEIn *com.livewallpaper.app.WeatherTestViewModel  
getStateIn *com.livewallpaper.app.WeatherTestViewModel  getVIEWModelScope *com.livewallpaper.app.WeatherTestViewModel  getViewModelScope *com.livewallpaper.app.WeatherTestViewModel  kotlinx *com.livewallpaper.app.WeatherTestViewModel  locationManager *com.livewallpaper.app.WeatherTestViewModel  stateIn *com.livewallpaper.app.WeatherTestViewModel  viewModelScope *com.livewallpaper.app.WeatherTestViewModel  weatherManager *com.livewallpaper.app.WeatherTestViewModel  
Composable #com.livewallpaper.app.ui.components  	ErrorCard #com.livewallpaper.app.ui.components  ExperimentalMaterial3Api #com.livewallpaper.app.ui.components  InfoRow #com.livewallpaper.app.ui.components  LoadingCard #com.livewallpaper.app.ui.components  String #com.livewallpaper.app.ui.components  Boolean com.livewallpaper.app.ui.theme  DarkColorScheme com.livewallpaper.app.ui.theme  LightColorScheme com.livewallpaper.app.ui.theme  LiveWallpaperTheme com.livewallpaper.app.ui.theme  Pink40 com.livewallpaper.app.ui.theme  Pink80 com.livewallpaper.app.ui.theme  Purple40 com.livewallpaper.app.ui.theme  Purple80 com.livewallpaper.app.ui.theme  PurpleGrey40 com.livewallpaper.app.ui.theme  PurpleGrey80 com.livewallpaper.app.ui.theme  
Typography com.livewallpaper.app.ui.theme  Unit com.livewallpaper.app.ui.theme  ClosedFloatingPointRange !com.livewallpaper.core.data.model  
Composable !com.livewallpaper.core.data.model  ExperimentalMaterial3Api !com.livewallpaper.core.data.model  	MusicInfo !com.livewallpaper.core.data.model  MusicVisualizationData !com.livewallpaper.core.data.model  MutableStateFlow !com.livewallpaper.core.data.model  Scene !com.livewallpaper.core.data.model  
SceneCategory !com.livewallpaper.core.data.model  Season !com.livewallpaper.core.data.model  
SettingOption !com.livewallpaper.core.data.model  SettingsCategory !com.livewallpaper.core.data.model  SharingStarted !com.livewallpaper.core.data.model  	StateFlow !com.livewallpaper.core.data.model  	TimeOfDay !com.livewallpaper.core.data.model  	TimePhase !com.livewallpaper.core.data.model  WallpaperSettings !com.livewallpaper.core.data.model  Weather !com.livewallpaper.core.data.model  WeatherForecast !com.livewallpaper.core.data.model  WeatherType !com.livewallpaper.core.data.model  asStateFlow !com.livewallpaper.core.data.model  	emptyList !com.livewallpaper.core.data.model  emptyMap !com.livewallpaper.core.data.model  flow !com.livewallpaper.core.data.model  kotlinx !com.livewallpaper.core.data.model  settingsRepository !com.livewallpaper.core.data.model  stateIn !com.livewallpaper.core.data.model  viewModelScope !com.livewallpaper.core.data.model  SceneRepository &com.livewallpaper.core.data.repository  SettingsRepository &com.livewallpaper.core.data.repository  getSettingsStats 9com.livewallpaper.core.data.repository.SettingsRepository  settingsFlow 9com.livewallpaper.core.data.repository.SettingsRepository  LocationManager &com.livewallpaper.core.domain.location  hasLocationPermission 6com.livewallpaper.core.domain.location.LocationManager  MusicManager #com.livewallpaper.core.domain.music  
MusicState #com.livewallpaper.core.domain.music  !hasNotificationListenerPermission 0com.livewallpaper.core.domain.music.MusicManager  
musicDataFlow 0com.livewallpaper.core.domain.music.MusicManager  visualizationDataFlow 0com.livewallpaper.core.domain.music.MusicManager  NoMusic .com.livewallpaper.core.domain.music.MusicState  SceneInitializer #com.livewallpaper.core.domain.scene  SceneManager #com.livewallpaper.core.domain.scene  
SceneState #com.livewallpaper.core.domain.scene  Loading .com.livewallpaper.core.domain.scene.SceneState  WallpaperTimeManager "com.livewallpaper.core.domain.time  WallpaperTimeState "com.livewallpaper.core.domain.time  Loading 5com.livewallpaper.core.domain.time.WallpaperTimeState  Success 5com.livewallpaper.core.domain.time.WallpaperTimeState  WeatherManager %com.livewallpaper.core.domain.weather  WeatherState %com.livewallpaper.core.domain.weather  isApiKeyConfigured 4com.livewallpaper.core.domain.weather.WeatherManager  Loading 2com.livewallpaper.core.domain.weather.WeatherState  DirtyRegionManager "com.livewallpaper.core.performance  DirtyRegionStats "com.livewallpaper.core.performance  MutableStateFlow "com.livewallpaper.core.performance  ObjectPoolManager "com.livewallpaper.core.performance  OptimizedRenderSettings "com.livewallpaper.core.performance  PerformanceLevel "com.livewallpaper.core.performance  PerformanceMetrics "com.livewallpaper.core.performance  PerformanceMonitor "com.livewallpaper.core.performance  PerformanceOptimizer "com.livewallpaper.core.performance  SharingStarted "com.livewallpaper.core.performance  	StateFlow "com.livewallpaper.core.performance  asStateFlow "com.livewallpaper.core.performance  	emptyList "com.livewallpaper.core.performance  emptyMap "com.livewallpaper.core.performance  map "com.livewallpaper.core.performance  stateIn "com.livewallpaper.core.performance  viewModelScope "com.livewallpaper.core.performance  MEDIUM 3com.livewallpaper.core.performance.PerformanceLevel  getPerformanceLevel 5com.livewallpaper.core.performance.PerformanceMonitor  performanceMetrics 5com.livewallpaper.core.performance.PerformanceMonitor  Logger com.livewallpaper.core.utils  Resource com.livewallpaper.core.utils  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ExperimentalMaterial3Api 	java.lang  
MusicState 	java.lang  MusicVisualizationData 	java.lang  MutableStateFlow 	java.lang  PerformanceLevel 	java.lang  PerformanceMetrics 	java.lang  
SceneState 	java.lang  SharingStarted 	java.lang  WallpaperSettings 	java.lang  WallpaperTimeState 	java.lang  WeatherState 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  flow 	java.lang  kotlinx 	java.lang  locationManager 	java.lang  map 	java.lang  musicManager 	java.lang  settingsRepository 	java.lang  stateIn 	java.lang  weatherManager 	java.lang  Inject javax.inject  Any kotlin  Boolean kotlin  ClosedFloatingPointRange kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  Int kotlin  
MusicState kotlin  MusicVisualizationData kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  PerformanceLevel kotlin  PerformanceMetrics kotlin  
SceneState kotlin  SharingStarted kotlin  String kotlin  Unit kotlin  WallpaperSettings kotlin  WallpaperTimeState kotlin  WeatherState kotlin  androidx kotlin  asStateFlow kotlin  	emptyList kotlin  emptyMap kotlin  flow kotlin  kotlinx kotlin  locationManager kotlin  map kotlin  musicManager kotlin  settingsRepository kotlin  stateIn kotlin  weatherManager kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  ClosedFloatingPointRange kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  
MusicState kotlin.annotation  MusicVisualizationData kotlin.annotation  MutableStateFlow kotlin.annotation  PerformanceLevel kotlin.annotation  PerformanceMetrics kotlin.annotation  
SceneState kotlin.annotation  SharingStarted kotlin.annotation  WallpaperSettings kotlin.annotation  WallpaperTimeState kotlin.annotation  WeatherState kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  flow kotlin.annotation  kotlinx kotlin.annotation  locationManager kotlin.annotation  map kotlin.annotation  musicManager kotlin.annotation  settingsRepository kotlin.annotation  stateIn kotlin.annotation  weatherManager kotlin.annotation  ClosedFloatingPointRange kotlin.collections  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  Map kotlin.collections  
MusicState kotlin.collections  MusicVisualizationData kotlin.collections  MutableStateFlow kotlin.collections  PerformanceLevel kotlin.collections  PerformanceMetrics kotlin.collections  
SceneState kotlin.collections  SharingStarted kotlin.collections  WallpaperSettings kotlin.collections  WallpaperTimeState kotlin.collections  WeatherState kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  flow kotlin.collections  kotlinx kotlin.collections  locationManager kotlin.collections  map kotlin.collections  musicManager kotlin.collections  settingsRepository kotlin.collections  stateIn kotlin.collections  weatherManager kotlin.collections  ClosedFloatingPointRange kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  
MusicState kotlin.comparisons  MusicVisualizationData kotlin.comparisons  MutableStateFlow kotlin.comparisons  PerformanceLevel kotlin.comparisons  PerformanceMetrics kotlin.comparisons  
SceneState kotlin.comparisons  SharingStarted kotlin.comparisons  WallpaperSettings kotlin.comparisons  WallpaperTimeState kotlin.comparisons  WeatherState kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  flow kotlin.comparisons  kotlinx kotlin.comparisons  locationManager kotlin.comparisons  map kotlin.comparisons  musicManager kotlin.comparisons  settingsRepository kotlin.comparisons  stateIn kotlin.comparisons  weatherManager kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ClosedFloatingPointRange 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  
MusicState 	kotlin.io  MusicVisualizationData 	kotlin.io  MutableStateFlow 	kotlin.io  PerformanceLevel 	kotlin.io  PerformanceMetrics 	kotlin.io  
SceneState 	kotlin.io  SharingStarted 	kotlin.io  WallpaperSettings 	kotlin.io  WallpaperTimeState 	kotlin.io  WeatherState 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  flow 	kotlin.io  kotlinx 	kotlin.io  locationManager 	kotlin.io  map 	kotlin.io  musicManager 	kotlin.io  settingsRepository 	kotlin.io  stateIn 	kotlin.io  weatherManager 	kotlin.io  ClosedFloatingPointRange 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  
MusicState 
kotlin.jvm  MusicVisualizationData 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  PerformanceLevel 
kotlin.jvm  PerformanceMetrics 
kotlin.jvm  
SceneState 
kotlin.jvm  SharingStarted 
kotlin.jvm  WallpaperSettings 
kotlin.jvm  WallpaperTimeState 
kotlin.jvm  WeatherState 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  flow 
kotlin.jvm  kotlinx 
kotlin.jvm  locationManager 
kotlin.jvm  map 
kotlin.jvm  musicManager 
kotlin.jvm  settingsRepository 
kotlin.jvm  stateIn 
kotlin.jvm  weatherManager 
kotlin.jvm  ClosedFloatingPointRange 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  
MusicState 
kotlin.ranges  MusicVisualizationData 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  PerformanceLevel 
kotlin.ranges  PerformanceMetrics 
kotlin.ranges  
SceneState 
kotlin.ranges  SharingStarted 
kotlin.ranges  WallpaperSettings 
kotlin.ranges  WallpaperTimeState 
kotlin.ranges  WeatherState 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  flow 
kotlin.ranges  kotlinx 
kotlin.ranges  locationManager 
kotlin.ranges  map 
kotlin.ranges  musicManager 
kotlin.ranges  settingsRepository 
kotlin.ranges  stateIn 
kotlin.ranges  weatherManager 
kotlin.ranges  KClass kotlin.reflect  ClosedFloatingPointRange kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  
MusicState kotlin.sequences  MusicVisualizationData kotlin.sequences  MutableStateFlow kotlin.sequences  PerformanceLevel kotlin.sequences  PerformanceMetrics kotlin.sequences  
SceneState kotlin.sequences  SharingStarted kotlin.sequences  WallpaperSettings kotlin.sequences  WallpaperTimeState kotlin.sequences  WeatherState kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  flow kotlin.sequences  kotlinx kotlin.sequences  locationManager kotlin.sequences  map kotlin.sequences  musicManager kotlin.sequences  settingsRepository kotlin.sequences  stateIn kotlin.sequences  weatherManager kotlin.sequences  ClosedFloatingPointRange kotlin.text  ExperimentalMaterial3Api kotlin.text  
MusicState kotlin.text  MusicVisualizationData kotlin.text  MutableStateFlow kotlin.text  PerformanceLevel kotlin.text  PerformanceMetrics kotlin.text  
SceneState kotlin.text  SharingStarted kotlin.text  WallpaperSettings kotlin.text  WallpaperTimeState kotlin.text  WeatherState kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  flow kotlin.text  kotlinx kotlin.text  locationManager kotlin.text  map kotlin.text  musicManager kotlin.text  settingsRepository kotlin.text  stateIn kotlin.text  weatherManager kotlin.text  CoroutineScope kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  DirtyRegionManager kotlinx.coroutines.flow  DirtyRegionStats kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  
MusicState kotlinx.coroutines.flow  MusicVisualizationData kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  ObjectPoolManager kotlinx.coroutines.flow  OptimizedRenderSettings kotlinx.coroutines.flow  PerformanceLevel kotlinx.coroutines.flow  PerformanceMetrics kotlinx.coroutines.flow  PerformanceMonitor kotlinx.coroutines.flow  PerformanceOptimizer kotlinx.coroutines.flow  
SceneState kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  WallpaperSettings kotlinx.coroutines.flow  WallpaperTimeState kotlinx.coroutines.flow  WeatherState kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  emptyMap kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  kotlinx kotlinx.coroutines.flow  locationManager kotlinx.coroutines.flow  map kotlinx.coroutines.flow  musicManager kotlinx.coroutines.flow  settingsRepository kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  weatherManager kotlinx.coroutines.flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  emit %kotlinx.coroutines.flow.FlowCollector  
getKOTLINX %kotlinx.coroutines.flow.FlowCollector  
getKotlinx %kotlinx.coroutines.flow.FlowCollector  getLOCATIONManager %kotlinx.coroutines.flow.FlowCollector  getLocationManager %kotlinx.coroutines.flow.FlowCollector  getMUSICManager %kotlinx.coroutines.flow.FlowCollector  getMusicManager %kotlinx.coroutines.flow.FlowCollector  getSETTINGSRepository %kotlinx.coroutines.flow.FlowCollector  getSettingsRepository %kotlinx.coroutines.flow.FlowCollector  getWEATHERManager %kotlinx.coroutines.flow.FlowCollector  getWeatherManager %kotlinx.coroutines.flow.FlowCollector  kotlinx %kotlinx.coroutines.flow.FlowCollector  locationManager %kotlinx.coroutines.flow.FlowCollector  musicManager %kotlinx.coroutines.flow.FlowCollector  settingsRepository %kotlinx.coroutines.flow.FlowCollector  weatherManager %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  getMAP !kotlinx.coroutines.flow.StateFlow  getMap !kotlinx.coroutines.flow.StateFlow  
getSTATEIn !kotlinx.coroutines.flow.StateFlow  
getStateIn !kotlinx.coroutines.flow.StateFlow  map !kotlinx.coroutines.flow.StateFlow  stateIn !kotlinx.coroutines.flow.StateFlow  Instant kotlinx.datetime  TimeZone kotlinx.datetime  toLocalDateTime kotlinx.datetime  Scene #com.livewallpaper.app.ui.components  
SceneCategory #com.livewallpaper.app.ui.components  Season #com.livewallpaper.app.ui.components  	TimeOfDay #com.livewallpaper.app.ui.components  WeatherType #com.livewallpaper.app.ui.components                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             