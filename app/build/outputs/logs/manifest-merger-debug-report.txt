-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-26:12
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/afd99d696f4a0738b498107c8dd6798f/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/4418cf4add2bc83a75aeff2d2a0e92d6/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/cd7ffbbed677f9cfb90b27e121b657a1/transformed/hilt-android-2.51/AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7fc51f9aa0b2d87d1dbaf52476c22433/transformed/navigation-common-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/53756df730a7ac0aed812ea5a7b64e8a/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/c6e3888cf5cc1490bda08037d58e828a/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/6b6260ce08ef33652978892c1c115343/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/171e41fb89769e7f9bc2dfff012a2096/transformed/navigation-compose-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/345f6482b7a34761472cf08fca92f1c9/transformed/play-services-location-21.2.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/26b6e572f8f22d9baa34d2e34c2ad54b/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/990754870ae118c24ba9a2f79cf8978e/transformed/fragment-1.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/3add51a5300446e99c3aab03828af8d4/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/52417f24efa70d558677385c994a05c0/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/98c8c14f0cb1f099c6946409a5fc997b/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d18b5955fdae4bc9923dac428ac61bf3/transformed/material-icons-extended-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/3fed14768fc303a8881bea7707f5fc55/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c79140210de33ca7fc51dc0baee6b14f/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/73e28e0e9738b0e3f91a1f3ff565f4c2/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/6df3df8f9d30ca5c8c43ac5b4da2c09a/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8c8beb4e035f957c9757bbb608051ccb/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c1e40889257f943616e7c6f4c6419524/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/77bb8a72e89e4ca4d79ebd3e6a8086ca/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d36aaed79d8e65eee4e07badf5169900/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ee8a3d6805407b21ce74ad67109eec5d/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/e24c8cbf5ccb6992fc06204ed3632b92/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/92f0ad3d334edd4f8d274d39f0d99da9/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/15041541fe05a854287396192b2ec54a/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/bf06ac3a3697fde0e6626a26361b0c72/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/902f8e2970dd77ed8d2137ebf15633cb/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/b6b9aea125cfdf2134880b5f31470515/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/85f46f537029faf1451edae089f6dca6/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/a04ea77ed3040e101703ab4392b8284d/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/46c6a361a054e89c36e0f5f8209c30df/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/fefc451889439275613f402eb30e02d5/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/2be285cad9a2e4526138f0a15781302b/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/543f86a8fe0d7304985e75f882795c06/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/19ddc8bece18003bfd973613c489935a/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/edd1b2f816870125fa6e3811e128c33f/transformed/activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/d09b7fecefcbb65b0a46791eaff60bc9/transformed/activity-compose-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/649745a5dede868b3da145ea863db2b8/transformed/activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/18116761ff7f030b3ad485698498f9ef/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f7e18b8bdc682702e27dbe54ae284d0f/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9ab9c927fcd004ec8c004d52c76e919/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/edebdb5651d24310fc996d456ec0159e/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/c943a55d0c984824d06c5cb256532cab/transformed/core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/73084334e45fe2990beec2c6836e4689/transformed/room-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/7fad8cf057d78e0b4786f94623b40f19/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ae936b00f67b5c4c791c107e1a2a58ee/transformed/datastore-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2375eab7b380cbdb5b534b1b292548e4/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/66c45a938ae2010849d956920e5d201b/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/4ba38b5ff81b28a2018f7d623b816211/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/f67d958edada33e0ad4107e1e1de5401/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/630a24401e7160048692f13544c7dc1a/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/654ff8377cb430c5734f0224f236068c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/cc306fb398866a4e25ca920cf28596c9/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ccf193dbcc5ed53bf718e0c344c347d9/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/37bef8b777515620d99f33940578cdc3/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/60ec19cc04a997eaf16ad6d848cc8529/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:16:1-19:12
	package
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
uses-permission#android.permission.BIND_WALLPAPER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
application
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-24:19
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-24:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/345f6482b7a34761472cf08fca92f1c9/transformed/play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/345f6482b7a34761472cf08fca92f1c9/transformed/play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/26b6e572f8f22d9baa34d2e34c2ad54b/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/26b6e572f8f22d9baa34d2e34c2ad54b/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/654ff8377cb430c5734f0224f236068c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/654ff8377cb430c5734f0224f236068c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-35
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:34:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:26:9-35
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-51
	android:dataExtractionRules
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:9-65
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:9-40
activity#com.livewallpaper.app.MainActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:37:9-46:20
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:13-45
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:39:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:38:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-45:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:17-77
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:27-74
activity#com.livewallpaper.app.WallpaperPreviewActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:49:9-57:20
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:13-54
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:51:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:50:13-53
intent-filter#action:name:android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-56:29
activity#com.livewallpaper.app.TimeTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:60:9-64:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:62:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:61:13-45
activity#com.livewallpaper.app.SceneTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:9-71:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:69:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:68:13-46
activity#com.livewallpaper.app.WeatherTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:9-78:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:76:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:75:13-48
activity#com.livewallpaper.app.MusicTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:9-85:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:83:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:82:13-46
activity#com.livewallpaper.app.SettingsActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:9-92:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:13-31
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:90:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:89:13-45
activity#com.livewallpaper.app.PerformanceTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:9-99:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:97:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:96:13-52
activity#com.livewallpaper.app.AboutActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:9-106:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:13-31
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:104:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:103:13-42
activity#com.livewallpaper.app.PrivacyPolicyActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:9-113:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:111:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:110:13-50
activity#com.livewallpaper.app.TermsOfServiceActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:9-120:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:118:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:117:13-51
activity#com.livewallpaper.app.OpenSourceLicensesActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:9-127:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:125:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:124:13-55
service#com.livewallpaper.core.service.MusicNotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:9-137:19
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:132:13-37
	android:permission
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:13-87
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:131:13-91
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-136:29
action#android.service.notification.NotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:17-99
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:25-96
uses-sdk
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/afd99d696f4a0738b498107c8dd6798f/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/afd99d696f4a0738b498107c8dd6798f/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/4418cf4add2bc83a75aeff2d2a0e92d6/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/4418cf4add2bc83a75aeff2d2a0e92d6/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/cd7ffbbed677f9cfb90b27e121b657a1/transformed/hilt-android-2.51/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/cd7ffbbed677f9cfb90b27e121b657a1/transformed/hilt-android-2.51/AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7fc51f9aa0b2d87d1dbaf52476c22433/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7fc51f9aa0b2d87d1dbaf52476c22433/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/53756df730a7ac0aed812ea5a7b64e8a/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/53756df730a7ac0aed812ea5a7b64e8a/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/c6e3888cf5cc1490bda08037d58e828a/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/c6e3888cf5cc1490bda08037d58e828a/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/6b6260ce08ef33652978892c1c115343/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/6b6260ce08ef33652978892c1c115343/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/171e41fb89769e7f9bc2dfff012a2096/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/171e41fb89769e7f9bc2dfff012a2096/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/345f6482b7a34761472cf08fca92f1c9/transformed/play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/345f6482b7a34761472cf08fca92f1c9/transformed/play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/26b6e572f8f22d9baa34d2e34c2ad54b/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/26b6e572f8f22d9baa34d2e34c2ad54b/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/990754870ae118c24ba9a2f79cf8978e/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/990754870ae118c24ba9a2f79cf8978e/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/3add51a5300446e99c3aab03828af8d4/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/3add51a5300446e99c3aab03828af8d4/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/c6a330154e8046512488728f2194268a/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/52417f24efa70d558677385c994a05c0/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/52417f24efa70d558677385c994a05c0/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/98c8c14f0cb1f099c6946409a5fc997b/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/98c8c14f0cb1f099c6946409a5fc997b/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d18b5955fdae4bc9923dac428ac61bf3/transformed/material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d18b5955fdae4bc9923dac428ac61bf3/transformed/material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/3fed14768fc303a8881bea7707f5fc55/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/3fed14768fc303a8881bea7707f5fc55/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/19c061252ad5f8cf8a88339364cdf1a8/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c79140210de33ca7fc51dc0baee6b14f/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c79140210de33ca7fc51dc0baee6b14f/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/73e28e0e9738b0e3f91a1f3ff565f4c2/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/73e28e0e9738b0e3f91a1f3ff565f4c2/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/6df3df8f9d30ca5c8c43ac5b4da2c09a/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/6df3df8f9d30ca5c8c43ac5b4da2c09a/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8c8beb4e035f957c9757bbb608051ccb/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8c8beb4e035f957c9757bbb608051ccb/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c1e40889257f943616e7c6f4c6419524/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c1e40889257f943616e7c6f4c6419524/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/77bb8a72e89e4ca4d79ebd3e6a8086ca/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/77bb8a72e89e4ca4d79ebd3e6a8086ca/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d36aaed79d8e65eee4e07badf5169900/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d36aaed79d8e65eee4e07badf5169900/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ee8a3d6805407b21ce74ad67109eec5d/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ee8a3d6805407b21ce74ad67109eec5d/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/e24c8cbf5ccb6992fc06204ed3632b92/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/e24c8cbf5ccb6992fc06204ed3632b92/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/92f0ad3d334edd4f8d274d39f0d99da9/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/92f0ad3d334edd4f8d274d39f0d99da9/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/15041541fe05a854287396192b2ec54a/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/15041541fe05a854287396192b2ec54a/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/bf06ac3a3697fde0e6626a26361b0c72/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/bf06ac3a3697fde0e6626a26361b0c72/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/902f8e2970dd77ed8d2137ebf15633cb/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/902f8e2970dd77ed8d2137ebf15633cb/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/b6b9aea125cfdf2134880b5f31470515/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/b6b9aea125cfdf2134880b5f31470515/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/85f46f537029faf1451edae089f6dca6/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/85f46f537029faf1451edae089f6dca6/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/a04ea77ed3040e101703ab4392b8284d/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/a04ea77ed3040e101703ab4392b8284d/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/46c6a361a054e89c36e0f5f8209c30df/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/46c6a361a054e89c36e0f5f8209c30df/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/fefc451889439275613f402eb30e02d5/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/fefc451889439275613f402eb30e02d5/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/2be285cad9a2e4526138f0a15781302b/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/2be285cad9a2e4526138f0a15781302b/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/543f86a8fe0d7304985e75f882795c06/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/543f86a8fe0d7304985e75f882795c06/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/19ddc8bece18003bfd973613c489935a/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/19ddc8bece18003bfd973613c489935a/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2d5e2d6c3aea9611fe131a5dcb48197d/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/edd1b2f816870125fa6e3811e128c33f/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/edd1b2f816870125fa6e3811e128c33f/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/d09b7fecefcbb65b0a46791eaff60bc9/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/d09b7fecefcbb65b0a46791eaff60bc9/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/649745a5dede868b3da145ea863db2b8/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/649745a5dede868b3da145ea863db2b8/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/18116761ff7f030b3ad485698498f9ef/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/18116761ff7f030b3ad485698498f9ef/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f7e18b8bdc682702e27dbe54ae284d0f/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f7e18b8bdc682702e27dbe54ae284d0f/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9ab9c927fcd004ec8c004d52c76e919/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9ab9c927fcd004ec8c004d52c76e919/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/edebdb5651d24310fc996d456ec0159e/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/edebdb5651d24310fc996d456ec0159e/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/c943a55d0c984824d06c5cb256532cab/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/c943a55d0c984824d06c5cb256532cab/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/73084334e45fe2990beec2c6836e4689/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/73084334e45fe2990beec2c6836e4689/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/7fad8cf057d78e0b4786f94623b40f19/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/7fad8cf057d78e0b4786f94623b40f19/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ae936b00f67b5c4c791c107e1a2a58ee/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ae936b00f67b5c4c791c107e1a2a58ee/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2375eab7b380cbdb5b534b1b292548e4/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2375eab7b380cbdb5b534b1b292548e4/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/66c45a938ae2010849d956920e5d201b/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/66c45a938ae2010849d956920e5d201b/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/4ba38b5ff81b28a2018f7d623b816211/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/4ba38b5ff81b28a2018f7d623b816211/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/f67d958edada33e0ad4107e1e1de5401/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/f67d958edada33e0ad4107e1e1de5401/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/630a24401e7160048692f13544c7dc1a/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/630a24401e7160048692f13544c7dc1a/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/654ff8377cb430c5734f0224f236068c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/654ff8377cb430c5734f0224f236068c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/cc306fb398866a4e25ca920cf28596c9/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/cc306fb398866a4e25ca920cf28596c9/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ccf193dbcc5ed53bf718e0c344c347d9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ccf193dbcc5ed53bf718e0c344c347d9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/37bef8b777515620d99f33940578cdc3/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/37bef8b777515620d99f33940578cdc3/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/60ec19cc04a997eaf16ad6d848cc8529/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/60ec19cc04a997eaf16ad6d848cc8529/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
service#com.livewallpaper.features.wallpaper.LiveWallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-23:19
	android:enabled
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-35
	android:label
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-60
	android:exported
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-36
	android:permission
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-67
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-85
intent-filter#action:name:android.service.wallpaper.WallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:29
action#android.service.wallpaper.WallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-85
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:25-82
meta-data#android.service.wallpaper
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-22:53
	android:resource
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-50
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:17-57
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/011bd6b750179fe3033823f86d5ee2e8/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c7f2fb61ffb14227957d8eaa2f642695/transformed/play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/ba13e3a2430b60cdf40f2700ac627b5b/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f7095c621466aeb320d5cc4bee553718/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8bd9721370632b74dca165bb767f164d/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/9c3764ab0107e41a53d816240e16e335/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/4e97847d66bcdf4b7a5543809fb3a162/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/d9caae402621b007b3ef141cdc2d3010/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a799a4d2b26f8c85b5507fdf9daa3d23/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a9a1753bcd57c5f46360f725ab7a088a/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
