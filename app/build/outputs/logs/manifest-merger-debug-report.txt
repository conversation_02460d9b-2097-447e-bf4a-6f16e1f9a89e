-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-158:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-158:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-158:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-158:12
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-26:12
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/AndroidManifest.xml:17:1-25:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d0cf81df2c62b13be50fa0d5f0921f4/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/15bc08db638775b78b48ddaa4b812b64/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/7d3c7f94643812bc91be4e49299bd7d3/transformed/hilt-android-2.51/AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/f506b16bfe272be73816844197d4d94f/transformed/navigation-common-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7317a51cf93dc61a56605d6d44934013/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7d2fe0265bf6872a3e154648728fc9b6/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/67d5b13efa4be02bf32b8f5ba05ed2f2/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/f1570bcbc5e17d964416c11c106616e2/transformed/navigation-compose-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [com.android.billingclient:billing-ktx:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8080915b12027deb3c4cfb1d2cc6f7f1/transformed/billing-ktx-7.1.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:2:1-38:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/21a7226ef8cdb574d1f4c066e1f4f536/transformed/play-services-location-21.2.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:17:1-112:12
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/5f7ad7cde5483633763e2fc715ac58b9/transformed/play-services-ads-base-23.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/198c85c57cced1bfbe2c2882586c16b2/transformed/play-services-appset-16.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/84bba9b1f4669c2c14ed03f545c906bd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.ump:user-messaging-platform:3.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/32eabad47471e3d56db99dc1d2ef784f/transformed/user-messaging-platform-3.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/8a64a654e33d33caa6d77ef19a19fdd5/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/e5a633594e5175518bfd9b799ae16cd6/transformed/fragment-1.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/a782beec2c9ac78aeab2531f46545aa2/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/7c851dfd701b401c9070be38d9bf5cf0/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fbe27c58a6e6cabaa3384369a010c750/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/0ad69d857da3b0928aaea32f1e95df00/transformed/material-icons-extended-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8360e4ee997870e3c61ea4ea1676e8e1/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fd3cc04bbaf6501ff47e895ce27890db/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/394d071b4e9173de8c778c2851dde14e/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8d51b76d86882a0766297ab892663a35/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/548dfcb34e8d8d5385298a90dd021c13/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b38566ed7304624e86dfc00fd3531084/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/861d1061c7caeab3047efd996f529668/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f05af7bc910c0a3c97aee47f3b4a71e4/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c8b6d9c864742580c051a4f43716efeb/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2a417c68a93e7f1bca5b956d090d352c/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fd8b1947acf45246939432f2dfe7e54d/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/24fda7397021b529df4ea5888347c3fa/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/68f9249684cd359bd67c3b90806642ec/transformed/lifecycle-service-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e86e0cb9d82c880384a39e6a7fefa47c/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/24d2445871155286a6490228293de251/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/fd120e770309b4ef0057f6f268a60640/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/1f1441b07263ee560a1454f065a2c225/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/f3b7d6d9db28c7cba3f2906dce1672c5/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/70d17bb71056a7af8202413617c814bf/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/58e20e4c087b24b68b469747591bff23/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ba550fd068fe64f47a741a31b888ffc1/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/7baad7096fb85dc473c6df4c32e806ed/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/815dd039427a63d577045d2b9e5bd7ec/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/46effac049952f310f36ca7656aa0dc2/transformed/activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/bc6601cc79571e9ad1addc48bd209813/transformed/activity-compose-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/0be44cfda288f48cbd9f0a30e1e4f52a/transformed/activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] /Users/<USER>/.gradle/caches/8.10/transforms/5adf65828cfbbed950be41d9f44d5edd/transformed/webkit-1.11.0-alpha02/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/664f0471376617ae4ab49f71f47a21ec/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/c0d9bc72b3e11b29fb79f7db5fb648eb/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/6c0f00d5f3f888cfc0890bd77975b0e4/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/f6578a9f600e74918a761bec2a13ef87/transformed/ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f0e7da2fa4ed454db32133de195507b0/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dedc8acd80de33a677c521808dc78974/transformed/core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/e2ab74747d150df334eaa2093ddef474/transformed/room-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/51b499ebf95f4d5bc5519e2bf1ad14ad/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/50c7fe6e444b4479e5bc40caacdd56cb/transformed/datastore-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c4f170946a85ce3490dfde00af98ec59/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/5a991d1e0fb596ae766db2847a97bda0/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/9900a88ea149166694f9ee10e5a52139/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac94d93ea0c85c24a5a2788c67d9755b/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/b49190dedf4e30726ab070df4b95309b/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/cbcc510b090f02a3da98ef8ccf6335e5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/936c338337e10da08752204c0f6f1ef4/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/adcb6027f8f4c54ca41beb00e30b3c4c/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/205b75ffc31acc94c43297bc8a932508/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f91ba767c42795354e70d18a72d72d7b/transformed/transport-api-3.0.0/AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ef5d46501d1ebee9c7e43f0784c6ee1a/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/965b1a531c473b5d1d7b52fd86fea715/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:16:1-19:12
	package
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:22:5-79
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
uses-permission#android.permission.BIND_WALLPAPER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
uses-permission#com.android.vending.BILLING
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:5-67
MERGED from [com.android.billingclient:billing-ktx:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8080915b12027deb3c4cfb1d2cc6f7f1/transformed/billing-ktx-7.1.1/AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing-ktx:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8080915b12027deb3c4cfb1d2cc6f7f1/transformed/billing-ktx-7.1.1/AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:10:5-67
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:22-64
application
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:5-156:19
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:5-156:19
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-24:19
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-24:19
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:21:5-36:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/21a7226ef8cdb574d1f4c066e1f4f536/transformed/play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/21a7226ef8cdb574d1f4c066e1f4f536/transformed/play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/5f7ad7cde5483633763e2fc715ac58b9/transformed/play-services-ads-base-23.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/5f7ad7cde5483633763e2fc715ac58b9/transformed/play-services-ads-base-23.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/198c85c57cced1bfbe2c2882586c16b2/transformed/play-services-appset-16.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/198c85c57cced1bfbe2c2882586c16b2/transformed/play-services-appset-16.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/84bba9b1f4669c2c14ed03f545c906bd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/84bba9b1f4669c2c14ed03f545c906bd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/8a64a654e33d33caa6d77ef19a19fdd5/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/8a64a654e33d33caa6d77ef19a19fdd5/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/cbcc510b090f02a3da98ef8ccf6335e5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/cbcc510b090f02a3da98ef8ccf6335e5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:35:9-35
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:34:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:37:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-35
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:36:9-51
	android:dataExtractionRules
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-65
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-40
activity#com.livewallpaper.app.MainActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:9-49:20
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:13-45
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:45:13-48:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:46:17-69
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:46:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:47:17-77
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:47:27-74
activity#com.livewallpaper.app.WallpaperPreviewActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:9-60:20
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:55:13-54
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:56:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-53
intent-filter#action:name:android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:57:13-59:29
activity#com.livewallpaper.app.TimeTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:9-67:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:66:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:65:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-45
activity#com.livewallpaper.app.SceneTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:9-74:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:73:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:72:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-46
activity#com.livewallpaper.app.WeatherTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:9-81:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:80:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:79:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-48
activity#com.livewallpaper.app.MusicTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:9-88:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:87:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:86:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-46
activity#com.livewallpaper.app.SettingsActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:9-95:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:94:13-31
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:93:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-45
activity#com.livewallpaper.app.PerformanceTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:9-102:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:101:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:100:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-52
activity#com.livewallpaper.app.AboutActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:9-109:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:108:13-31
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:107:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-42
activity#com.livewallpaper.app.PrivacyPolicyActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:9-116:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:115:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:114:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-50
activity#com.livewallpaper.app.TermsOfServiceActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:9-123:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:122:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:121:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-51
activity#com.livewallpaper.app.OpenSourceLicensesActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:9-130:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:129:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:128:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
activity#com.livewallpaper.app.PremiumActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:9-137:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:136:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:137:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-44
activity#com.livewallpaper.app.CustomSceneActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:140:9-144:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:143:13-34
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:142:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:144:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:141:13-48
service#com.livewallpaper.core.service.MusicNotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:147:9-154:19
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:149:13-37
	android:permission
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:150:13-87
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:148:13-91
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:151:13-153:29
action#android.service.notification.NotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:152:17-99
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:152:25-96
uses-sdk
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/AndroidManifest.xml:21:5-23:52
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d0cf81df2c62b13be50fa0d5f0921f4/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d0cf81df2c62b13be50fa0d5f0921f4/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/15bc08db638775b78b48ddaa4b812b64/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/15bc08db638775b78b48ddaa4b812b64/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/7d3c7f94643812bc91be4e49299bd7d3/transformed/hilt-android-2.51/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/7d3c7f94643812bc91be4e49299bd7d3/transformed/hilt-android-2.51/AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/f506b16bfe272be73816844197d4d94f/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/f506b16bfe272be73816844197d4d94f/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7317a51cf93dc61a56605d6d44934013/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7317a51cf93dc61a56605d6d44934013/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7d2fe0265bf6872a3e154648728fc9b6/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/7d2fe0265bf6872a3e154648728fc9b6/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/67d5b13efa4be02bf32b8f5ba05ed2f2/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/67d5b13efa4be02bf32b8f5ba05ed2f2/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/f1570bcbc5e17d964416c11c106616e2/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/f1570bcbc5e17d964416c11c106616e2/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing-ktx:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8080915b12027deb3c4cfb1d2cc6f7f1/transformed/billing-ktx-7.1.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing-ktx:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/8080915b12027deb3c4cfb1d2cc6f7f1/transformed/billing-ktx-7.1.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/21a7226ef8cdb574d1f4c066e1f4f536/transformed/play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/21a7226ef8cdb574d1f4c066e1f4f536/transformed/play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/5f7ad7cde5483633763e2fc715ac58b9/transformed/play-services-ads-base-23.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/5f7ad7cde5483633763e2fc715ac58b9/transformed/play-services-ads-base-23.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/198c85c57cced1bfbe2c2882586c16b2/transformed/play-services-appset-16.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/198c85c57cced1bfbe2c2882586c16b2/transformed/play-services-appset-16.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/84bba9b1f4669c2c14ed03f545c906bd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/84bba9b1f4669c2c14ed03f545c906bd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/32eabad47471e3d56db99dc1d2ef784f/transformed/user-messaging-platform-3.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/32eabad47471e3d56db99dc1d2ef784f/transformed/user-messaging-platform-3.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/8a64a654e33d33caa6d77ef19a19fdd5/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/8a64a654e33d33caa6d77ef19a19fdd5/transformed/play-services-measurement-base-20.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/e5a633594e5175518bfd9b799ae16cd6/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/e5a633594e5175518bfd9b799ae16cd6/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/a782beec2c9ac78aeab2531f46545aa2/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/a782beec2c9ac78aeab2531f46545aa2/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/79fb703933e689f6fc400d46473e56d1/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/7c851dfd701b401c9070be38d9bf5cf0/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/7c851dfd701b401c9070be38d9bf5cf0/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fbe27c58a6e6cabaa3384369a010c750/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fbe27c58a6e6cabaa3384369a010c750/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/0ad69d857da3b0928aaea32f1e95df00/transformed/material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/0ad69d857da3b0928aaea32f1e95df00/transformed/material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8360e4ee997870e3c61ea4ea1676e8e1/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8360e4ee997870e3c61ea4ea1676e8e1/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8f97ca3010301b3dcc8e1227134793bc/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fd3cc04bbaf6501ff47e895ce27890db/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fd3cc04bbaf6501ff47e895ce27890db/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/394d071b4e9173de8c778c2851dde14e/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/394d071b4e9173de8c778c2851dde14e/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8d51b76d86882a0766297ab892663a35/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/8d51b76d86882a0766297ab892663a35/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/548dfcb34e8d8d5385298a90dd021c13/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/548dfcb34e8d8d5385298a90dd021c13/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b38566ed7304624e86dfc00fd3531084/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b38566ed7304624e86dfc00fd3531084/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/861d1061c7caeab3047efd996f529668/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/861d1061c7caeab3047efd996f529668/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f05af7bc910c0a3c97aee47f3b4a71e4/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f05af7bc910c0a3c97aee47f3b4a71e4/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c8b6d9c864742580c051a4f43716efeb/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c8b6d9c864742580c051a4f43716efeb/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2a417c68a93e7f1bca5b956d090d352c/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2a417c68a93e7f1bca5b956d090d352c/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fd8b1947acf45246939432f2dfe7e54d/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/fd8b1947acf45246939432f2dfe7e54d/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/24fda7397021b529df4ea5888347c3fa/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/24fda7397021b529df4ea5888347c3fa/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/68f9249684cd359bd67c3b90806642ec/transformed/lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/68f9249684cd359bd67c3b90806642ec/transformed/lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e86e0cb9d82c880384a39e6a7fefa47c/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e86e0cb9d82c880384a39e6a7fefa47c/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/24d2445871155286a6490228293de251/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/24d2445871155286a6490228293de251/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/fd120e770309b4ef0057f6f268a60640/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/fd120e770309b4ef0057f6f268a60640/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/1f1441b07263ee560a1454f065a2c225/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/1f1441b07263ee560a1454f065a2c225/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/f3b7d6d9db28c7cba3f2906dce1672c5/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/f3b7d6d9db28c7cba3f2906dce1672c5/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/70d17bb71056a7af8202413617c814bf/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/70d17bb71056a7af8202413617c814bf/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/58e20e4c087b24b68b469747591bff23/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/58e20e4c087b24b68b469747591bff23/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ba550fd068fe64f47a741a31b888ffc1/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ba550fd068fe64f47a741a31b888ffc1/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/7baad7096fb85dc473c6df4c32e806ed/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/7baad7096fb85dc473c6df4c32e806ed/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/815dd039427a63d577045d2b9e5bd7ec/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/815dd039427a63d577045d2b9e5bd7ec/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b8644617b8d89d1375a8ddc9dabbd8e5/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/46effac049952f310f36ca7656aa0dc2/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/46effac049952f310f36ca7656aa0dc2/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/bc6601cc79571e9ad1addc48bd209813/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/bc6601cc79571e9ad1addc48bd209813/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/0be44cfda288f48cbd9f0a30e1e4f52a/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/0be44cfda288f48cbd9f0a30e1e4f52a/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10/transforms/1b07cfc2d920f59c1fda6147826aa7c9/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] /Users/<USER>/.gradle/caches/8.10/transforms/5adf65828cfbbed950be41d9f44d5edd/transformed/webkit-1.11.0-alpha02/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] /Users/<USER>/.gradle/caches/8.10/transforms/5adf65828cfbbed950be41d9f44d5edd/transformed/webkit-1.11.0-alpha02/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/664f0471376617ae4ab49f71f47a21ec/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/664f0471376617ae4ab49f71f47a21ec/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/c0d9bc72b3e11b29fb79f7db5fb648eb/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/c0d9bc72b3e11b29fb79f7db5fb648eb/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/6c0f00d5f3f888cfc0890bd77975b0e4/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/6c0f00d5f3f888cfc0890bd77975b0e4/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/f6578a9f600e74918a761bec2a13ef87/transformed/ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/f6578a9f600e74918a761bec2a13ef87/transformed/ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f0e7da2fa4ed454db32133de195507b0/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f0e7da2fa4ed454db32133de195507b0/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dedc8acd80de33a677c521808dc78974/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dedc8acd80de33a677c521808dc78974/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/e2ab74747d150df334eaa2093ddef474/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/e2ab74747d150df334eaa2093ddef474/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/51b499ebf95f4d5bc5519e2bf1ad14ad/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/51b499ebf95f4d5bc5519e2bf1ad14ad/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/50c7fe6e444b4479e5bc40caacdd56cb/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/50c7fe6e444b4479e5bc40caacdd56cb/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c4f170946a85ce3490dfde00af98ec59/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c4f170946a85ce3490dfde00af98ec59/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/5a991d1e0fb596ae766db2847a97bda0/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/5a991d1e0fb596ae766db2847a97bda0/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/9900a88ea149166694f9ee10e5a52139/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/9900a88ea149166694f9ee10e5a52139/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac94d93ea0c85c24a5a2788c67d9755b/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac94d93ea0c85c24a5a2788c67d9755b/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/b49190dedf4e30726ab070df4b95309b/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/b49190dedf4e30726ab070df4b95309b/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/cbcc510b090f02a3da98ef8ccf6335e5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/cbcc510b090f02a3da98ef8ccf6335e5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/936c338337e10da08752204c0f6f1ef4/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/936c338337e10da08752204c0f6f1ef4/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/adcb6027f8f4c54ca41beb00e30b3c4c/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/adcb6027f8f4c54ca41beb00e30b3c4c/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/205b75ffc31acc94c43297bc8a932508/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/205b75ffc31acc94c43297bc8a932508/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f91ba767c42795354e70d18a72d72d7b/transformed/transport-api-3.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f91ba767c42795354e70d18a72d72d7b/transformed/transport-api-3.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ef5d46501d1ebee9c7e43f0784c6ee1a/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ef5d46501d1ebee9c7e43f0784c6ee1a/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/965b1a531c473b5d1d7b52fd86fea715/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/965b1a531c473b5d1d7b52fd86fea715/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:18:3-42
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/f4c8b95f3e9c72e202f56d650855f9ce/transformed/play-services-ads-23.5.0/AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
service#com.livewallpaper.features.wallpaper.LiveWallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-23:19
	android:enabled
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-35
	android:label
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-60
	android:exported
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-36
	android:permission
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-67
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-85
intent-filter#action:name:android.service.wallpaper.WallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:29
action#android.service.wallpaper.WallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-85
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:25-82
meta-data#android.service.wallpaper
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-22:53
	android:resource
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-50
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:17-57
queries
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:12:5-19:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:35:5-68:15
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:28:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:30:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:27:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/c38ad4e94aaa543319c468ca7cd3b77e/transformed/billing-7.1.1/AndroidManifest.xml:32:13-80
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f093d2b27c6976d5471f666239b8ebee/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:99:13-82
property
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:107:9-109:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:109:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f20d63bf80d66e121268eafec57d93b/transformed/play-services-ads-lite-23.5.0/AndroidManifest.xml:108:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.10/transforms/71e0d4103619836010616bdda0dbe6c8/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/43d703246fe95d576307a158a5b02e68/transformed/play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/39c7531c7177a534bb27b0fb573ef13e/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/1f8de84394614fd7b593b0df9942e400/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/35f45b55a87987d97a7ec9cca44966f0/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:25-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c9e73d2b7f738acb6a908d0ff33c8f37/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/18cf9c29d73e046eb58eada7c9edd376/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/6f0abcac127dfb2bd0dd9508a870ccc1/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/ea25008a46333915cf8020b65273a30d/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/739c4b887dedab6b751083852655b236/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10/transforms/3ed24d3ece5b3a8d5ec5887e21750e35/transformed/ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/545f6f5aa403b96c4ee365b2bcb6ecf7/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/06ebb9fb2e55d47c3a4c4fc427fcd0df/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/ea0842cb70d667a4eeffd521722d3dec/transformed/transport-backend-cct-3.1.8/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] /Users/<USER>/.gradle/caches/8.10/transforms/84057300ea78625f92dce4734e514fd0/transformed/transport-runtime-3.1.8/AndroidManifest.xml:33:13-132
