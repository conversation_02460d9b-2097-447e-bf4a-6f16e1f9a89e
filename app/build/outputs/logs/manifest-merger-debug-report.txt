-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:1-141:12
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-26:12
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a7c3f855e2cfe5502b2b0c10d8a01622/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/516b9c86e8861d205e409c39a68f46bb/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/ce1e759d3d6ee9c7473d5bd30e8017eb/transformed/hilt-android-2.51/AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/e411b250a24a66b882acc71938f0c6c1/transformed/navigation-common-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/638b5b7a6414b4089edbf8c6ae6236b1/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/769a62d9374510cb956b2a4859ee884d/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/3738b15b915c4de63a364aef626673e7/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/02e55aea2dee81b054acf7060b579837/transformed/navigation-compose-2.7.7/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/0b6eea660c9ad467bd48b7530b4d52e6/transformed/play-services-location-21.2.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac3233551a1c9124efa9ea67d82dd1ef/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/45538e565e9c2dc73dbf4f27fcaca00d/transformed/fragment-1.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/16e15140eb1ad736f87b41cf83be644b/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c31fb0da55bb1f611041b4ec45c80180/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/692998de6aa58840efc00876fc3bfb7b/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/7f324c768c4ddcbe59155ec265f012d6/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d0f4454c00478abf934b791b932aaf6b/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/9960bf390c82439e7461aa5548b40697/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87483d6a51e705db8b428b88f9a3bccf/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2200b64798e0418ee4b89aa2daecf622/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/1aafa5830a2e865e694a670fa1137f53/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f6f552f390bff6a2b25965b59ef392a4/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/53fbae5c644432a6c191f1f8dd783882/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/bf02055c3e005c39c2e5f0a52c8d6bdf/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/385a6e77dfd8d765e4e3a6185835cefc/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/6112ac7bb4a417aef786e32ff6a13547/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/805c42f25dcc7b9e129fc9d65e2ab348/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/1c005f9b55c422460f8a5731bde12dcb/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/25aa4fa2124839dbfd25be97093cd15b/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/2cc0d7f67765178859e89ecb112defb8/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/c754f8c6231a4dc026cb2adde34b41d4/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/6ab14baacac80d337bd7e3e0a3b82d37/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/951268667f4973fb8f33b14fc76f8aad/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f9713d78c4c4788449d9d3430cf9419/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/153cd2ddac2913b905e4ae4a5100609d/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/321257f65879994afa8a73ca09eaa546/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/57ee4036bcc82dd7cd475505a2f3eb08/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/140f6926afec9ecba7fab2dc60d70221/transformed/activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/392f0812e3a0e65510f3d28e002bdfc9/transformed/activity-compose-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/d7f09c5f98ab8b0ba042adc5567dcf70/transformed/activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e3c522c189bd0262ce5361d593d5fed1/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e9a2379178245b416378c62d450970a5/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e1adc9173cd1143183b3060c0285829f/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/901130a58ac5d33388aae215b1b078bb/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/a33537b5f86018e51d0c2115ede15a93/transformed/core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a760cb68131ccb21621cf6eab2c36923/transformed/room-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/b22830d138c7e43e7879c292c1aee027/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/4bc844f5d12a88d6592162473018795c/transformed/datastore-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/4386066f0f7165e8d0b6b973a50b8c6a/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/45462d0edb7f3e20793aca86843f3d1b/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/cf6890c1cb1a66d23cca5c395a3c21e3/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/0936725e7035ecf0ebef69c752ddbcad/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/c5c8da7923e8ed98ba92e03b41e20f9b/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/36783c592cb9e9a354749c7c8e051b6c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/eefaafa01b6a368013775cb765d3c24f/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/aca5f0327744c77883f5ebfcf384a857/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/94cf5d949e0e541eef95c2540f15616c/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/46b306ec6cbabb1e9a0153a1338a1ee0/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:16:1-19:12
	package
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:5-67
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:5-79
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:5-81
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:10:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:5-79
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:11:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:5-15:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:15:9-35
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:14:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:5-76
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:16:22-73
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:5-93
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:19:22-90
uses-permission#android.permission.BIND_WALLPAPER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:5-73
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:22:22-70
application
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:24:5-139:19
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-24:19
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-24:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/0b6eea660c9ad467bd48b7530b4d52e6/transformed/play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/0b6eea660c9ad467bd48b7530b4d52e6/transformed/play-services-location-21.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac3233551a1c9124efa9ea67d82dd1ef/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac3233551a1c9124efa9ea67d82dd1ef/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/36783c592cb9e9a354749c7c8e051b6c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/36783c592cb9e9a354749c7c8e051b6c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:32:9-35
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:30:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:28:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:31:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:34:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:29:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:26:9-35
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:33:9-51
	android:dataExtractionRules
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:27:9-65
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:25:9-40
activity#com.livewallpaper.app.MainActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:37:9-46:20
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:40:13-45
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:39:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:41:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:38:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:42:13-45:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:17-69
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:17-77
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:44:27-74
activity#com.livewallpaper.app.WallpaperPreviewActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:49:9-57:20
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:52:13-54
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:51:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:53:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:50:13-53
intent-filter#action:name:android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:54:13-56:29
activity#com.livewallpaper.app.TimeTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:60:9-64:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:63:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:62:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:64:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:61:13-45
activity#com.livewallpaper.app.SceneTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:67:9-71:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:70:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:69:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:71:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:68:13-46
activity#com.livewallpaper.app.WeatherTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:74:9-78:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:77:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:76:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:78:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:75:13-48
activity#com.livewallpaper.app.MusicTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:81:9-85:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:84:13-35
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:83:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:85:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:82:13-46
activity#com.livewallpaper.app.SettingsActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:88:9-92:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:91:13-31
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:90:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:92:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:89:13-45
activity#com.livewallpaper.app.PerformanceTestActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:95:9-99:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:98:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:97:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:99:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:96:13-52
activity#com.livewallpaper.app.AboutActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:102:9-106:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:105:13-31
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:104:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:106:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:103:13-42
activity#com.livewallpaper.app.PrivacyPolicyActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:109:9-113:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:112:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:111:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:113:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:110:13-50
activity#com.livewallpaper.app.TermsOfServiceActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:116:9-120:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:119:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:118:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:120:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:117:13-51
activity#com.livewallpaper.app.OpenSourceLicensesActivity
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:123:9-127:58
	android:label
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:126:13-33
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:125:13-37
	android:theme
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:127:13-55
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:124:13-55
service#com.livewallpaper.core.service.MusicNotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:130:9-137:19
	android:exported
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:132:13-37
	android:permission
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:133:13-87
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:131:13-91
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:134:13-136:29
action#android.service.notification.NotificationListenerService
ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:17-99
	android:name
		ADDED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml:135:25-96
uses-sdk
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:weather] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/weather/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:features:music] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/music/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:core] /Users/<USER>/Documents/GitHub/LiveWallpaper/core/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a7c3f855e2cfe5502b2b0c10d8a01622/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a7c3f855e2cfe5502b2b0c10d8a01622/transformed/hilt-navigation-compose-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/516b9c86e8861d205e409c39a68f46bb/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/516b9c86e8861d205e409c39a68f46bb/transformed/hilt-navigation-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/ce1e759d3d6ee9c7473d5bd30e8017eb/transformed/hilt-android-2.51/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/ce1e759d3d6ee9c7473d5bd30e8017eb/transformed/hilt-android-2.51/AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/e411b250a24a66b882acc71938f0c6c1/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/e411b250a24a66b882acc71938f0c6c1/transformed/navigation-common-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/638b5b7a6414b4089edbf8c6ae6236b1/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/638b5b7a6414b4089edbf8c6ae6236b1/transformed/navigation-common-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/769a62d9374510cb956b2a4859ee884d/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/769a62d9374510cb956b2a4859ee884d/transformed/navigation-runtime-2.7.7/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/3738b15b915c4de63a364aef626673e7/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/3738b15b915c4de63a364aef626673e7/transformed/navigation-runtime-ktx-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/02e55aea2dee81b054acf7060b579837/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] /Users/<USER>/.gradle/caches/8.10/transforms/02e55aea2dee81b054acf7060b579837/transformed/navigation-compose-2.7.7/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/0b6eea660c9ad467bd48b7530b4d52e6/transformed/play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/0b6eea660c9ad467bd48b7530b4d52e6/transformed/play-services-location-21.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac3233551a1c9124efa9ea67d82dd1ef/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/ac3233551a1c9124efa9ea67d82dd1ef/transformed/play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/45538e565e9c2dc73dbf4f27fcaca00d/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.10/transforms/45538e565e9c2dc73dbf4f27fcaca00d/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/16e15140eb1ad736f87b41cf83be644b/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/16e15140eb1ad736f87b41cf83be644b/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/0d547187c45418c45f72b67275e94a15/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c31fb0da55bb1f611041b4ec45c80180/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/c31fb0da55bb1f611041b4ec45c80180/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/692998de6aa58840efc00876fc3bfb7b/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/692998de6aa58840efc00876fc3bfb7b/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/7f324c768c4ddcbe59155ec265f012d6/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/7f324c768c4ddcbe59155ec265f012d6/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/60006d538cbfd3b077d33785d15cb176/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d0f4454c00478abf934b791b932aaf6b/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/d0f4454c00478abf934b791b932aaf6b/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/9960bf390c82439e7461aa5548b40697/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/9960bf390c82439e7461aa5548b40697/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87483d6a51e705db8b428b88f9a3bccf/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87483d6a51e705db8b428b88f9a3bccf/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2200b64798e0418ee4b89aa2daecf622/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/2200b64798e0418ee4b89aa2daecf622/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/1aafa5830a2e865e694a670fa1137f53/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/1aafa5830a2e865e694a670fa1137f53/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f6f552f390bff6a2b25965b59ef392a4/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/f6f552f390bff6a2b25965b59ef392a4/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/53fbae5c644432a6c191f1f8dd783882/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/53fbae5c644432a6c191f1f8dd783882/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/bf02055c3e005c39c2e5f0a52c8d6bdf/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/bf02055c3e005c39c2e5f0a52c8d6bdf/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/385a6e77dfd8d765e4e3a6185835cefc/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/385a6e77dfd8d765e4e3a6185835cefc/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/6112ac7bb4a417aef786e32ff6a13547/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/6112ac7bb4a417aef786e32ff6a13547/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/805c42f25dcc7b9e129fc9d65e2ab348/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/805c42f25dcc7b9e129fc9d65e2ab348/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/1c005f9b55c422460f8a5731bde12dcb/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/1c005f9b55c422460f8a5731bde12dcb/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/25aa4fa2124839dbfd25be97093cd15b/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/25aa4fa2124839dbfd25be97093cd15b/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/2cc0d7f67765178859e89ecb112defb8/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/2cc0d7f67765178859e89ecb112defb8/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/c754f8c6231a4dc026cb2adde34b41d4/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/c754f8c6231a4dc026cb2adde34b41d4/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/6ab14baacac80d337bd7e3e0a3b82d37/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/6ab14baacac80d337bd7e3e0a3b82d37/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/951268667f4973fb8f33b14fc76f8aad/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/951268667f4973fb8f33b14fc76f8aad/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f9713d78c4c4788449d9d3430cf9419/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/9f9713d78c4c4788449d9d3430cf9419/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/153cd2ddac2913b905e4ae4a5100609d/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/153cd2ddac2913b905e4ae4a5100609d/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/321257f65879994afa8a73ca09eaa546/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/321257f65879994afa8a73ca09eaa546/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/57ee4036bcc82dd7cd475505a2f3eb08/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/57ee4036bcc82dd7cd475505a2f3eb08/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/61aff17e7f39222c3b9ac9a66c5a5fd6/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/140f6926afec9ecba7fab2dc60d70221/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/140f6926afec9ecba7fab2dc60d70221/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/392f0812e3a0e65510f3d28e002bdfc9/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/392f0812e3a0e65510f3d28e002bdfc9/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/d7f09c5f98ab8b0ba042adc5567dcf70/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/d7f09c5f98ab8b0ba042adc5567dcf70/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e3c522c189bd0262ce5361d593d5fed1/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e3c522c189bd0262ce5361d593d5fed1/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e9a2379178245b416378c62d450970a5/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e9a2379178245b416378c62d450970a5/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e1adc9173cd1143183b3060c0285829f/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e1adc9173cd1143183b3060c0285829f/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/901130a58ac5d33388aae215b1b078bb/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/901130a58ac5d33388aae215b1b078bb/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/a33537b5f86018e51d0c2115ede15a93/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/a33537b5f86018e51d0c2115ede15a93/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a760cb68131ccb21621cf6eab2c36923/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a760cb68131ccb21621cf6eab2c36923/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/b22830d138c7e43e7879c292c1aee027/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/b22830d138c7e43e7879c292c1aee027/transformed/datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/4bc844f5d12a88d6592162473018795c/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/4bc844f5d12a88d6592162473018795c/transformed/datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/4386066f0f7165e8d0b6b973a50b8c6a/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/4386066f0f7165e8d0b6b973a50b8c6a/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/45462d0edb7f3e20793aca86843f3d1b/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/45462d0edb7f3e20793aca86843f3d1b/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/cf6890c1cb1a66d23cca5c395a3c21e3/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/cf6890c1cb1a66d23cca5c395a3c21e3/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/0936725e7035ecf0ebef69c752ddbcad/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/0936725e7035ecf0ebef69c752ddbcad/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/c5c8da7923e8ed98ba92e03b41e20f9b/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/c5c8da7923e8ed98ba92e03b41e20f9b/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/36783c592cb9e9a354749c7c8e051b6c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/36783c592cb9e9a354749c7c8e051b6c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/eefaafa01b6a368013775cb765d3c24f/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/eefaafa01b6a368013775cb765d3c24f/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/aca5f0327744c77883f5ebfcf384a857/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/aca5f0327744c77883f5ebfcf384a857/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/94cf5d949e0e541eef95c2540f15616c/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/94cf5d949e0e541eef95c2540f15616c/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/46b306ec6cbabb1e9a0153a1338a1ee0/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51] /Users/<USER>/.gradle/caches/8.10/transforms/46b306ec6cbabb1e9a0153a1338a1ee0/transformed/dagger-lint-aar-2.51/AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/GitHub/LiveWallpaper/app/src/main/AndroidManifest.xml
service#com.livewallpaper.features.wallpaper.LiveWallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-23:19
	android:enabled
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-35
	android:label
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-60
	android:exported
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-36
	android:permission
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-67
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-85
intent-filter#action:name:android.service.wallpaper.WallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-18:29
action#android.service.wallpaper.WallpaperService
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-85
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:25-82
meta-data#android.service.wallpaper
ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-22:53
	android:resource
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-50
	android:name
		ADDED from [:features:wallpaper] /Users/<USER>/Documents/GitHub/LiveWallpaper/features/wallpaper/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:17-57
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/030c9c326553cb5fef315337579495be/transformed/play-services-base-18.3.0/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/d19df42a5bb9ad6ecae1bfbf8b215cb3/transformed/play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/87f99fb6979505d4d471c05a506572b4/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.3] /Users/<USER>/.gradle/caches/8.10/transforms/b17969977633a3309efbd4b9af5fe7af/transformed/ui-test-manifest-1.6.3/AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/d9fcb8904960952fb81576a4211fe454/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/3d224be7d164e6c6f967cc6d16e35382/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/83cc7a893ea08add4365a6b0304acd8a/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.livewallpaper.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/dd40244802df1fd17efbb2998727617a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/ae9114c953f4be2951dfb41e3c253c19/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/baf5bbb22238cfaa824fda79091c1f56/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
