package com.livewallpaper.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.data.model.MusicVisualizationData
import com.livewallpaper.core.domain.music.MusicManager
import com.livewallpaper.core.domain.music.MusicState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 音乐测试ViewModel
 */
@HiltViewModel
class MusicTestViewModel @Inject constructor(
    private val musicManager: MusicManager
) : ViewModel() {
    
    val musicState: StateFlow<MusicState> = musicManager.musicDataFlow
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = MusicState.NoMusic
        )
    
    val visualizationData: StateFlow<MusicVisualizationData> = musicManager.visualizationDataFlow
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = MusicVisualizationData(false, 0f, 0f)
        )
    
    val hasNotificationPermission: StateFlow<Boolean> = flow {
        while (true) {
            emit(musicManager.hasNotificationListenerPermission())
            kotlinx.coroutines.delay(2000) // 每2秒检查一次权限状态
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )
    
    fun refreshMusicState() {
        viewModelScope.launch {
            // 音乐状态会自动通过Flow更新，这里可以添加手动刷新逻辑
            // 例如重新检查权限状态等
        }
    }
}
