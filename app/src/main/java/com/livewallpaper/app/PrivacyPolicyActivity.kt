package com.livewallpaper.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 隐私政策Activity
 */
@AndroidEntryPoint
class PrivacyPolicyActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                PrivacyPolicyScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacyPolicyScreen(
    onBackClick: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("隐私政策") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "隐私政策",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "最后更新：2024年12月",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            PolicySection(
                title = "1. 信息收集",
                content = """
我们重视您的隐私。本应用可能收集以下信息：

• 位置信息：用于获取当地天气数据，仅在您授权后使用
• 设备信息：用于性能优化和兼容性调整
• 使用统计：用于改进应用功能和用户体验
• 崩溃报告：用于修复应用问题和提升稳定性

所有信息收集均在您明确同意的前提下进行。
                """.trimIndent()
            )
            
            PolicySection(
                title = "2. 信息使用",
                content = """
我们收集的信息仅用于以下目的：

• 提供天气相关的壁纸效果
• 优化应用性能和用户体验
• 修复应用错误和提升稳定性
• 分析使用模式以改进功能

我们不会将您的个人信息出售、租赁或以其他方式转让给第三方。
                """.trimIndent()
            )
            
            PolicySection(
                title = "3. 数据安全",
                content = """
我们采取适当的安全措施保护您的信息：

• 数据传输采用加密技术
• 本地数据存储采用安全机制
• 定期更新安全措施
• 限制员工访问个人信息

尽管我们努力保护您的信息，但无法保证绝对安全。
                """.trimIndent()
            )
            
            PolicySection(
                title = "4. 第三方服务",
                content = """
本应用可能使用以下第三方服务：

• OpenWeatherMap：提供天气数据服务
• Google Play Services：提供系统服务
• Firebase：提供分析和崩溃报告服务

这些服务有各自的隐私政策，请查阅相关文档。
                """.trimIndent()
            )
            
            PolicySection(
                title = "5. 权限说明",
                content = """
本应用请求的权限及用途：

• 位置权限：获取当地天气信息
• 网络权限：下载天气数据和场景资源
• 通知监听权限：获取音乐播放信息
• 存储权限：保存设置和缓存数据

您可以随时在系统设置中撤销这些权限。
                """.trimIndent()
            )
            
            PolicySection(
                title = "6. 儿童隐私",
                content = """
本应用不专门面向13岁以下儿童设计。我们不会故意收集13岁以下儿童的个人信息。

如果您发现我们收集了儿童的个人信息，请联系我们，我们将立即删除相关信息。
                """.trimIndent()
            )
            
            PolicySection(
                title = "7. 政策更新",
                content = """
我们可能会不时更新本隐私政策。更新后的政策将在应用中发布，并在适当情况下通知您。

继续使用本应用即表示您接受更新后的隐私政策。
                """.trimIndent()
            )
            
            PolicySection(
                title = "8. 联系我们",
                content = """
如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：

邮箱：<EMAIL>
地址：中国北京市朝阳区

我们将在收到您的询问后尽快回复。
                """.trimIndent()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
fun PolicySection(
    title: String,
    content: String
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = content,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.2
            )
        }
    }
}
