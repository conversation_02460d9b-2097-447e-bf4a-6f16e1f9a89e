package com.livewallpaper.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.data.model.Scene
import com.livewallpaper.core.data.repository.SceneRepository
import com.livewallpaper.core.domain.scene.SceneInitializer
import com.livewallpaper.core.domain.scene.SceneManager
import com.livewallpaper.core.domain.scene.SceneState
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 场景测试ViewModel
 */
@HiltViewModel
class SceneTestViewModel @Inject constructor(
    private val sceneManager: SceneManager,
    private val sceneRepository: SceneRepository,
    private val sceneInitializer: SceneInitializer
) : ViewModel() {

    private val _sceneState = MutableStateFlow<SceneState>(SceneState.Loading)
    val sceneState: StateFlow<SceneState> = _sceneState.asStateFlow()

    private val _allScenes = MutableStateFlow<List<Scene>>(emptyList())
    val allScenes: StateFlow<List<Scene>> = _allScenes.asStateFlow()

    init {
        initializeScenes()
        startSceneStateMonitoring()
        loadAllScenes()
    }

    private fun initializeScenes() {
        viewModelScope.launch {
            try {
                if (sceneInitializer.shouldInitialize()) {
                    sceneInitializer.initializeDefaultScenes()
                }
            } catch (e: Exception) {
                _sceneState.value = SceneState.Error("初始化失败: ${e.message}")
            }
        }
    }

    private fun startSceneStateMonitoring() {
        viewModelScope.launch {
            sceneManager.getCurrentSceneFlow()
                .catch { e ->
                    _sceneState.value = SceneState.Error("场景状态监控失败: ${e.message}")
                }
                .collect { state ->
                    _sceneState.value = state
                }
        }
    }

    private fun loadAllScenes() {
        viewModelScope.launch {
            sceneRepository.getAllScenes()
                .catch { e ->
                    // 处理错误，但不影响场景状态
                }
                .collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            _allScenes.value = resource.data ?: emptyList()
                        }
                        is Resource.Error -> {
                            // 保持当前列表，只记录错误
                        }
                        is Resource.Loading -> {
                            // 保持当前状态
                        }
                    }
                }
        }
    }

    fun switchToScene(sceneId: String) {
        viewModelScope.launch {
            try {
                sceneManager.switchToScene(sceneId)
            } catch (e: Exception) {
                _sceneState.value = SceneState.Error("切换场景失败: ${e.message}")
            }
        }
    }

    fun refreshScenes() {
        viewModelScope.launch {
            _sceneState.value = SceneState.Loading
            loadAllScenes()
            // 场景状态会通过监控自动更新
        }
    }
}
