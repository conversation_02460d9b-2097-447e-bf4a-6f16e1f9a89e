package com.livewallpaper.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.domain.location.LocationManager
import com.livewallpaper.core.domain.time.WallpaperTimeManager
import com.livewallpaper.core.domain.time.WallpaperTimeState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 时间测试ViewModel
 */
@HiltViewModel
class TimeTestViewModel @Inject constructor(
    private val timeManager: WallpaperTimeManager,
    private val locationManager: LocationManager
) : ViewModel() {
    
    private val _timeState = MutableStateFlow<WallpaperTimeState>(WallpaperTimeState.Loading)
    val timeState: StateFlow<WallpaperTimeState> = _timeState.asStateFlow()
    
    val hasLocationPermission: StateFlow<Boolean> = flow {
        while (true) {
            emit(locationManager.hasLocationPermission())
            kotlinx.coroutines.delay(1000) // 每秒检查一次权限状态
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )
    
    init {
        startTimeStateMonitoring()
    }
    
    private fun startTimeStateMonitoring() {
        viewModelScope.launch {
            timeManager.getTimeStateFlow()
                .catch { e ->
                    _timeState.value = WallpaperTimeState.Error("时间状态监控失败: ${e.message}")
                }
                .collect { state ->
                    _timeState.value = state
                }
        }
    }
    
    fun refreshTimeState() {
        viewModelScope.launch {
            _timeState.value = WallpaperTimeState.Loading
            val newState = timeManager.refreshLocation()
            _timeState.value = newState
        }
    }
}
