package com.livewallpaper.app

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.app.ui.components.*
import com.livewallpaper.core.data.model.Weather
import com.livewallpaper.core.data.model.WeatherForecast
import com.livewallpaper.core.data.model.WeatherType
import com.livewallpaper.core.data.model.Location
import com.livewallpaper.core.domain.weather.WeatherState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * 天气系统测试Activity
 * 用于验证天气系统是否正常工作
 */
@AndroidEntryPoint
class WeatherTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                WeatherTestScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WeatherTestScreen(
    onBackClick: () -> Unit,
    viewModel: WeatherTestViewModel = viewModel()
) {
    val context = LocalContext.current
    val weatherState by viewModel.weatherState.collectAsState()
    val weatherForecast by viewModel.weatherForecast.collectAsState()
    val hasLocationPermission by viewModel.hasLocationPermission.collectAsState()
    val isApiKeyConfigured by viewModel.isApiKeyConfigured.collectAsState()

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val granted = permissions.values.any { it }
        if (granted) {
            viewModel.refreshWeather()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("天气系统测试") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshWeather() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // API密钥状态卡片
            ApiKeyStatusCard(
                isConfigured = isApiKeyConfigured,
                onConfigureApiKey = { apiKey ->
                    viewModel.setApiKey(apiKey)
                }
            )

            // 权限状态卡片
            PermissionStatusCard(
                hasPermission = hasLocationPermission,
                onRequestPermission = {
                    permissionLauncher.launch(
                        arrayOf(
                            Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_COARSE_LOCATION
                        )
                    )
                }
            )

            // 天气状态卡片
            WeatherStateCard(weatherState = weatherState)

            // 详细信息卡片
            when (val currentWeatherState = weatherState) {
                is WeatherState.Success -> {
                    WeatherDetailsCard(currentWeatherState.weather)
                    WeatherLocationCard(currentWeatherState.location)
                }
                is WeatherState.Error -> {
                    ErrorCard(currentWeatherState.message)
                }
                is WeatherState.Loading -> {
                    LoadingCard()
                }
            }

            // 天气预报卡片
            WeatherForecastCard(forecasts = weatherForecast)
        }
    }
}

@Composable
fun ApiKeyStatusCard(
    isConfigured: Boolean,
    onConfigureApiKey: (String) -> Unit
) {
    var apiKeyInput by remember { mutableStateOf("") }
    var showApiKeyDialog by remember { mutableStateOf(false) }

    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "API密钥状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (isConfigured) "已配置" else "未配置",
                    color = if (isConfigured) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                )

                Button(onClick = { showApiKeyDialog = true }) {
                    Text(if (isConfigured) "更新密钥" else "配置密钥")
                }
            }

            if (!isConfigured) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "请在OpenWeatherMap官网申请免费API密钥",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }

    if (showApiKeyDialog) {
        AlertDialog(
            onDismissRequest = { showApiKeyDialog = false },
            title = { Text("配置API密钥") },
            text = {
                Column {
                    Text("请输入OpenWeatherMap API密钥:")
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = apiKeyInput,
                        onValueChange = { apiKeyInput = it },
                        label = { Text("API密钥") },
                        singleLine = true
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (apiKeyInput.isNotBlank()) {
                            onConfigureApiKey(apiKeyInput)
                            showApiKeyDialog = false
                            apiKeyInput = ""
                        }
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showApiKeyDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
fun WeatherStateCard(weatherState: WeatherState) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "天气状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val statusText = when (weatherState) {
                is WeatherState.Loading -> "加载中..."
                is WeatherState.Success -> "正常"
                is WeatherState.Error -> "错误"
            }

            val statusColor = when (weatherState) {
                is WeatherState.Loading -> MaterialTheme.colorScheme.secondary
                is WeatherState.Success -> MaterialTheme.colorScheme.primary
                is WeatherState.Error -> MaterialTheme.colorScheme.error
            }

            Text(
                text = statusText,
                color = statusColor,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun WeatherLocationCard(location: Location) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "位置信息",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            InfoRow("城市", location.city)
            InfoRow("国家", location.country)
            InfoRow("纬度", "%.4f".format(location.latitude))
            InfoRow("经度", "%.4f".format(location.longitude))
            InfoRow("时区", location.timezone)
        }
    }
}

@Composable
fun WeatherDetailsCard(weather: Weather) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "天气详情",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            InfoRow("温度", "${weather.temperature.toInt()}°C")
            InfoRow("体感温度", "${weather.feelsLike.toInt()}°C")
            InfoRow("天气", getWeatherTypeDisplayName(weather.weatherType))
            InfoRow("描述", weather.description)
            InfoRow("湿度", "${weather.humidity}%")
            InfoRow("气压", "${weather.pressure.toInt()} hPa")
            InfoRow("能见度", "${weather.visibility.toInt()} km")
            InfoRow("风速", "${weather.windSpeed} m/s")
            InfoRow("风向", "${weather.windDirection}°")
            InfoRow("更新时间", formatTimestamp(Instant.fromEpochMilliseconds(weather.timestamp)))
        }
    }
}

@Composable
fun WeatherForecastCard(forecasts: List<WeatherForecast>) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "天气预报 (${forecasts.size})",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            if (forecasts.isEmpty()) {
                Text(
                    text = "暂无预报数据",
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                forecasts.take(5).forEach { forecast ->
                    ForecastItem(forecast = forecast)
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }
        }
    }
}

@Composable
fun ForecastItem(forecast: WeatherForecast) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = formatDate(Instant.fromEpochMilliseconds(forecast.date)),
            style = MaterialTheme.typography.bodySmall
        )
        Text(
            text = getWeatherTypeDisplayName(forecast.weatherType),
            style = MaterialTheme.typography.bodySmall
        )
        Text(
            text = "${forecast.minTemperature.toInt()}°/${forecast.maxTemperature.toInt()}°",
            style = MaterialTheme.typography.bodySmall
        )
    }
}

private fun getWeatherTypeDisplayName(weatherType: WeatherType): String {
    return when (weatherType) {
        WeatherType.CLEAR -> "晴朗"
        WeatherType.CLOUDY -> "多云"
        WeatherType.RAINY -> "雨天"
        WeatherType.SNOWY -> "雪天"
        WeatherType.FOGGY -> "雾天"
        WeatherType.STORMY -> "暴风雨"
        WeatherType.WINDY -> "大风"
    }
}

private fun formatTimestamp(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}"
}

private fun formatDate(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.monthNumber}/${localDateTime.dayOfMonth}"
}
