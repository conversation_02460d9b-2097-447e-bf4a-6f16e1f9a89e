package com.livewallpaper.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.core.performance.DirtyRegionStats
import com.livewallpaper.core.performance.OptimizedRenderSettings
import com.livewallpaper.core.performance.PerformanceLevel
import com.livewallpaper.core.performance.PerformanceMetrics
import dagger.hilt.android.AndroidEntryPoint

/**
 * 性能测试Activity
 * 用于监控和分析动态壁纸的性能表现
 */
@AndroidEntryPoint
class PerformanceTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                PerformanceTestScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PerformanceTestScreen(
    onBackClick: () -> Unit,
    viewModel: PerformanceTestViewModel = viewModel()
) {
    val performanceMetrics by viewModel.performanceMetrics.collectAsState()
    val optimizedSettings by viewModel.optimizedSettings.collectAsState()
    val dirtyRegionStats by viewModel.dirtyRegionStats.collectAsState()
    val performanceLevel by viewModel.performanceLevel.collectAsState()
    val recommendations by viewModel.recommendations.collectAsState()
    val objectPoolStats by viewModel.objectPoolStats.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("性能监控") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.triggerGC() }) {
                        Icon(Icons.Default.Delete, contentDescription = "触发GC")
                    }
                    IconButton(onClick = { viewModel.resetStats() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "重置统计")
                    }
                }
            )
        }
    ) { innerPadding ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 性能等级卡片
            item {
                PerformanceLevelCard(performanceLevel = performanceLevel)
            }
            
            // 实时性能指标
            item {
                PerformanceMetricsCard(metrics = performanceMetrics)
            }
            
            // 优化设置
            item {
                OptimizedSettingsCard(settings = optimizedSettings)
            }
            
            // 脏区域统计
            item {
                DirtyRegionStatsCard(stats = dirtyRegionStats)
            }
            
            // 对象池统计
            item {
                ObjectPoolStatsCard(stats = objectPoolStats)
            }
            
            // 性能建议
            item {
                PerformanceRecommendationsCard(recommendations = recommendations)
            }
            
            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
fun PerformanceLevelCard(performanceLevel: PerformanceLevel) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (performanceLevel) {
                PerformanceLevel.LOW -> MaterialTheme.colorScheme.errorContainer
                PerformanceLevel.MEDIUM -> MaterialTheme.colorScheme.secondaryContainer
                PerformanceLevel.HIGH -> MaterialTheme.colorScheme.primaryContainer
                PerformanceLevel.ULTRA -> MaterialTheme.colorScheme.tertiaryContainer
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "性能等级",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = performanceLevel.displayName,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = when (performanceLevel) {
                    PerformanceLevel.LOW -> "建议降低设置"
                    PerformanceLevel.MEDIUM -> "性能一般"
                    PerformanceLevel.HIGH -> "性能良好"
                    PerformanceLevel.ULTRA -> "性能优秀"
                },
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
fun PerformanceMetricsCard(metrics: PerformanceMetrics) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "实时性能指标",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                MetricItem(
                    label = "帧率",
                    value = "${metrics.currentFps}",
                    unit = "fps",
                    color = if (metrics.currentFps >= 24) Color.Green else Color.Red
                )
                MetricItem(
                    label = "内存",
                    value = "${metrics.memoryUsagePercent}",
                    unit = "%",
                    color = when {
                        metrics.memoryUsagePercent < 60 -> Color.Green
                        metrics.memoryUsagePercent < 80 -> Color(0xFFFF9800)
                        else -> Color.Red
                    }
                )
                MetricItem(
                    label = "掉帧",
                    value = "${metrics.droppedFrames}",
                    unit = "",
                    color = if (metrics.droppedFrames < 5) Color.Green else Color.Red
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "内存使用: ${metrics.memoryUsageMB}MB / ${metrics.maxMemoryMB}MB",
                style = MaterialTheme.typography.bodySmall
            )
            Text(
                text = "平均帧时间: ${String.format("%.1f", metrics.averageFrameTime)}ms",
                style = MaterialTheme.typography.bodySmall
            )
            Text(
                text = "最大帧时间: ${metrics.maxFrameTime}ms",
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
fun MetricItem(
    label: String,
    value: String,
    unit: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "$value$unit",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun OptimizedSettingsCard(settings: OptimizedRenderSettings?) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "优化设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (settings != null) {
                SettingRow("帧率", "${settings.frameRate}fps")
                SettingRow("渲染质量", settings.quality.displayName)
                SettingRow("视差效果", if (settings.enableParallax) "开启" else "关闭")
                SettingRow("天气效果", if (settings.enableWeatherEffects) "开启" else "关闭")
                SettingRow("音乐可视化", if (settings.enableMusicVisualization) "开启" else "关闭")
                SettingRow("最大粒子数", "${settings.maxParticles}")
                SettingRow("脏区域重绘", if (settings.enableDirtyRegions) "开启" else "关闭")
            } else {
                Text(
                    text = "正在计算优化设置...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun DirtyRegionStatsCard(stats: DirtyRegionStats?) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "脏区域统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (stats != null) {
                SettingRow("区域数量", "${stats.regionCount}")
                SettingRow("脏区域面积", "${stats.totalDirtyArea}px²")
                SettingRow("覆盖百分比", "${stats.dirtyPercent}%")
                SettingRow("全屏重绘", if (stats.isFullRedraw) "是" else "否")
            } else {
                Text(
                    text = "暂无脏区域数据",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun ObjectPoolStatsCard(stats: Map<String, Int>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "对象池统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (stats.isNotEmpty()) {
                stats.forEach { (type, count) ->
                    SettingRow(type, "$count 个对象")
                }
            } else {
                Text(
                    text = "暂无对象池数据",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun PerformanceRecommendationsCard(recommendations: List<String>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "性能建议",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (recommendations.isNotEmpty()) {
                recommendations.forEach { recommendation ->
                    Row(
                        modifier = Modifier.padding(vertical = 2.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        Text(
                            text = "• ",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            text = recommendation,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
            } else {
                Text(
                    text = "性能表现良好，无需优化建议",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun SettingRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}
