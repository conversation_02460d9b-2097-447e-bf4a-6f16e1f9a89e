package com.livewallpaper.app

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.data.model.Scene
import com.livewallpaper.core.domain.scene.CustomSceneManager
import com.livewallpaper.core.domain.scene.SceneManager
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 自定义场景管理ViewModel
 */
@HiltViewModel
class CustomSceneViewModel @Inject constructor(
    private val customSceneManager: CustomSceneManager,
    private val sceneManager: SceneManager
) : ViewModel() {
    
    private val _customScenes = MutableStateFlow<List<Scene>>(emptyList())
    val customScenes: StateFlow<List<Scene>> = _customScenes.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    private val _selectedImageUri = MutableStateFlow<Uri?>(null)
    val selectedImageUri: StateFlow<Uri?> = _selectedImageUri.asStateFlow()
    
    private val _operationInProgress = MutableStateFlow(false)
    val operationInProgress: StateFlow<Boolean> = _operationInProgress.asStateFlow()
    
    /**
     * 加载自定义场景列表
     */
    fun loadCustomScenes() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = customSceneManager.getAllCustomScenes()
                
                when (result) {
                    is Resource.Success -> {
                        _customScenes.value = result.data ?: emptyList()
                        Logger.d("Loaded ${result.data?.size ?: 0} custom scenes")
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to load custom scenes: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error loading custom scenes", e)
                _errorMessage.value = "加载自定义场景失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 创建自定义场景
     */
    fun createCustomScene(name: String, description: String) {
        val imageUri = _selectedImageUri.value
        if (imageUri == null) {
            _errorMessage.value = "请先选择图片"
            return
        }
        
        viewModelScope.launch {
            try {
                _operationInProgress.value = true
                _errorMessage.value = null
                
                Logger.d("Creating custom scene: $name")
                
                val result = customSceneManager.createCustomSceneFromGallery(
                    imageUri = imageUri,
                    sceneName = name,
                    description = description
                )
                
                when (result) {
                    is Resource.Success -> {
                        Logger.d("Custom scene created successfully: $name")
                        _selectedImageUri.value = null // 清除选择的图片
                        loadCustomScenes() // 重新加载列表
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to create custom scene: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error creating custom scene", e)
                _errorMessage.value = "创建场景失败: ${e.message}"
            } finally {
                _operationInProgress.value = false
            }
        }
    }
    
    /**
     * 编辑自定义场景
     */
    fun editCustomScene(sceneId: String, newName: String, newDescription: String) {
        viewModelScope.launch {
            try {
                _operationInProgress.value = true
                _errorMessage.value = null
                
                Logger.d("Editing custom scene: $sceneId")
                
                val result = customSceneManager.editCustomScene(
                    sceneId = sceneId,
                    newName = newName,
                    newDescription = newDescription,
                    newBackgroundUri = _selectedImageUri.value
                )
                
                when (result) {
                    is Resource.Success -> {
                        Logger.d("Custom scene edited successfully: $sceneId")
                        _selectedImageUri.value = null // 清除选择的图片
                        loadCustomScenes() // 重新加载列表
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to edit custom scene: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error editing custom scene", e)
                _errorMessage.value = "编辑场景失败: ${e.message}"
            } finally {
                _operationInProgress.value = false
            }
        }
    }
    
    /**
     * 删除自定义场景
     */
    fun deleteCustomScene(sceneId: String) {
        viewModelScope.launch {
            try {
                _operationInProgress.value = true
                _errorMessage.value = null
                
                Logger.d("Deleting custom scene: $sceneId")
                
                val result = customSceneManager.deleteCustomScene(sceneId)
                
                when (result) {
                    is Resource.Success -> {
                        Logger.d("Custom scene deleted successfully: $sceneId")
                        loadCustomScenes() // 重新加载列表
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to delete custom scene: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error deleting custom scene", e)
                _errorMessage.value = "删除场景失败: ${e.message}"
            } finally {
                _operationInProgress.value = false
            }
        }
    }
    
    /**
     * 选择场景作为当前壁纸
     */
    fun selectScene(sceneId: String) {
        viewModelScope.launch {
            try {
                _operationInProgress.value = true
                _errorMessage.value = null
                
                Logger.d("Selecting scene: $sceneId")
                
                val result = sceneManager.switchToScene(sceneId)
                
                when (result) {
                    is Resource.Success -> {
                        Logger.d("Scene selected successfully: $sceneId")
                        _errorMessage.value = "场景已应用"
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to select scene: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error selecting scene", e)
                _errorMessage.value = "应用场景失败: ${e.message}"
            } finally {
                _operationInProgress.value = false
            }
        }
    }
    
    /**
     * 设置选择的图片URI
     */
    fun setSelectedImageUri(uri: Uri) {
        _selectedImageUri.value = uri
        Logger.d("Selected image URI: $uri")
    }
    
    /**
     * 清除选择的图片
     */
    fun clearSelectedImage() {
        _selectedImageUri.value = null
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    /**
     * 刷新场景列表
     */
    fun refresh() {
        loadCustomScenes()
    }
    
    /**
     * 获取场景统计信息
     */
    fun getSceneStats(): SceneStats {
        val scenes = _customScenes.value
        return SceneStats(
            totalScenes = scenes.size,
            recentScenes = scenes.filter { 
                System.currentTimeMillis() - it.createdAt < 7 * 24 * 60 * 60 * 1000L // 7天内
            }.size,
            totalSizeEstimate = "${scenes.size * 2}MB" // 估算大小
        )
    }
    
    /**
     * 检查是否可以创建更多场景
     */
    fun canCreateMoreScenes(): Boolean {
        // 免费用户限制5个自定义场景
        val maxFreeScenes = 5
        return _customScenes.value.size < maxFreeScenes
    }
    
    /**
     * 获取创建场景的限制信息
     */
    fun getCreationLimitInfo(): String {
        val currentCount = _customScenes.value.size
        val maxFreeScenes = 5
        return "已创建 $currentCount/$maxFreeScenes 个场景"
    }
    
    /**
     * 验证场景名称
     */
    fun validateSceneName(name: String): String? {
        return when {
            name.isBlank() -> "场景名称不能为空"
            name.length < 2 -> "场景名称至少需要2个字符"
            name.length > 50 -> "场景名称不能超过50个字符"
            _customScenes.value.any { it.name == name } -> "场景名称已存在"
            else -> null
        }
    }
    
    /**
     * 获取推荐的场景名称
     */
    fun getSuggestedSceneNames(): List<String> {
        return listOf(
            "我的风景",
            "美好回忆",
            "自然之美",
            "城市夜景",
            "温馨时光",
            "梦幻世界"
        ).filter { suggestedName ->
            _customScenes.value.none { it.name == suggestedName }
        }
    }
}

/**
 * 场景统计信息
 */
data class SceneStats(
    val totalScenes: Int,
    val recentScenes: Int,
    val totalSizeEstimate: String
)
