package com.livewallpaper.app

import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.domain.billing.BillingManager
import com.livewallpaper.core.domain.billing.BillingState
import com.livewallpaper.core.domain.billing.ProductDetails
import com.livewallpaper.core.domain.billing.PurchaseState
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 高级功能购买页面ViewModel
 */
@HiltViewModel
class PremiumViewModel @Inject constructor(
    private val billingManager: BillingManager
) : ViewModel() {
    
    private val _products = MutableStateFlow<List<ProductDetails>>(emptyList())
    val products: StateFlow<List<ProductDetails>> = _products.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 从BillingManager获取状态
    val billingState: StateFlow<BillingState> = billingManager.billingState
    val purchaseState: StateFlow<PurchaseState> = billingManager.purchaseState
    
    init {
        initializeBilling()
    }
    
    /**
     * 初始化计费服务
     */
    private fun initializeBilling() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = billingManager.initialize()
                
                when (result) {
                    is Resource.Success -> {
                        Logger.d("Billing initialized successfully")
                        loadProducts()
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to initialize billing: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error initializing billing", e)
                _errorMessage.value = "初始化失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载产品列表
     */
    fun loadProducts() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val result = billingManager.queryProducts()
                
                when (result) {
                    is Resource.Success -> {
                        _products.value = result.data ?: emptyList()
                        Logger.d("Loaded ${result.data?.size ?: 0} products")
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to load products: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error loading products", e)
                _errorMessage.value = "加载产品失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 购买产品
     */
    fun purchaseProduct(activity: Activity, productId: String) {
        viewModelScope.launch {
            try {
                Logger.d("Purchasing product: $productId")
                
                val result = billingManager.launchPurchase(activity, productId)
                
                when (result) {
                    is Resource.Success -> {
                        Logger.d("Purchase initiated successfully")
                        // 购买流程已启动，等待回调
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to start purchase: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error purchasing product", e)
                _errorMessage.value = "购买失败: ${e.message}"
            }
        }
    }
    
    /**
     * 恢复购买
     */
    fun restorePurchases() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                Logger.d("Restoring purchases")
                
                val result = billingManager.queryPurchases()
                
                when (result) {
                    is Resource.Success -> {
                        val purchases = result.data ?: emptyList()
                        if (purchases.isNotEmpty()) {
                            Logger.d("Restored ${purchases.size} purchases")
                            _errorMessage.value = "已恢复 ${purchases.size} 个购买项目"
                        } else {
                            Logger.d("No purchases to restore")
                            _errorMessage.value = "没有找到可恢复的购买记录"
                        }
                    }
                    is Resource.Error -> {
                        Logger.e("Failed to restore purchases: ${result.message}")
                        _errorMessage.value = result.message
                    }
                    is Resource.Loading -> {
                        // 继续等待
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error restoring purchases", e)
                _errorMessage.value = "恢复购买失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 检查产品是否已购买
     */
    fun isProductPurchased(productId: String): Boolean {
        return billingManager.isProductPurchased(productId)
    }
    
    /**
     * 获取产品详情
     */
    fun getProductDetails(productId: String): ProductDetails? {
        return _products.value.find { it.productId == productId }
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    /**
     * 刷新产品和购买状态
     */
    fun refresh() {
        loadProducts()
        restorePurchases()
    }
    
    /**
     * 获取推荐产品
     */
    fun getRecommendedProducts(): List<ProductDetails> {
        return _products.value.filter { product ->
            when (product.productId) {
                BillingManager.PREMIUM_SUBSCRIPTION -> true
                BillingManager.PREMIUM_SCENES_PACK -> true
                BillingManager.REMOVE_ADS -> true
                else -> false
            }
        }
    }
    
    /**
     * 获取产品类别
     */
    fun getProductsByCategory(): Map<String, List<ProductDetails>> {
        val products = _products.value
        return mapOf(
            "订阅服务" to products.filter { it.productId == BillingManager.PREMIUM_SUBSCRIPTION },
            "功能包" to products.filter { 
                it.productId in listOf(
                    BillingManager.PREMIUM_SCENES_PACK,
                    BillingManager.WEATHER_EFFECTS_PACK,
                    BillingManager.MUSIC_VISUALIZER_PACK
                )
            },
            "其他" to products.filter { it.productId == BillingManager.REMOVE_ADS }
        )
    }
    
    /**
     * 计算总价值
     */
    fun calculateTotalValue(): String {
        val totalMicros = _products.value.sumOf { it.priceAmountMicros }
        val totalYuan = totalMicros / 1_000_000.0
        return "¥%.2f".format(totalYuan)
    }
    
    /**
     * 获取最优惠的套餐
     */
    fun getBestDealProduct(): ProductDetails? {
        return _products.value.find { it.productId == BillingManager.PREMIUM_SUBSCRIPTION }
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        billingManager.disconnect()
    }
}
