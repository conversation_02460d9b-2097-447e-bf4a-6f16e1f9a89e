package com.livewallpaper.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.data.model.WeatherForecast
import com.livewallpaper.core.domain.location.LocationManager
import com.livewallpaper.core.domain.weather.WeatherManager
import com.livewallpaper.core.domain.weather.WeatherState
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 天气测试ViewModel
 */
@HiltViewModel
class WeatherTestViewModel @Inject constructor(
    private val weatherManager: WeatherManager,
    private val locationManager: LocationManager
) : ViewModel() {

    private val _weatherState = MutableStateFlow<WeatherState>(WeatherState.Loading)
    val weatherState: StateFlow<WeatherState> = _weatherState.asStateFlow()

    private val _weatherForecast = MutableStateFlow<List<WeatherForecast>>(emptyList())
    val weatherForecast: StateFlow<List<WeatherForecast>> = _weatherForecast.asStateFlow()

    val hasLocationPermission: StateFlow<Boolean> = flow {
        while (true) {
            emit(locationManager.hasLocationPermission())
            kotlinx.coroutines.delay(1000) // 每秒检查一次权限状态
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val isApiKeyConfigured: StateFlow<Boolean> = flow {
        while (true) {
            emit(weatherManager.isApiKeyConfigured())
            kotlinx.coroutines.delay(5000) // 每5秒检查一次API密钥状态
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    init {
        startWeatherStateMonitoring()
        loadWeatherForecast()
    }

    private fun startWeatherStateMonitoring() {
        viewModelScope.launch {
            weatherManager.getWeatherStateFlow()
                .catch { e ->
                    _weatherState.value = WeatherState.Error("天气状态监控失败: ${e.message}")
                }
                .collect { state ->
                    _weatherState.value = state
                }
        }
    }

    private fun loadWeatherForecast() {
        viewModelScope.launch {
            try {
                val result = weatherManager.getWeatherForecast()
                when (result) {
                    is Resource.Success -> {
                        _weatherForecast.value = result.data ?: emptyList()
                    }
                    is Resource.Error -> {
                        // 预报加载失败不影响当前天气显示
                    }
                    is Resource.Loading -> {
                        // 保持当前状态
                    }
                }
            } catch (e: Exception) {
                // 预报加载失败不影响当前天气显示
            }
        }
    }

    fun refreshWeather() {
        viewModelScope.launch {
            _weatherState.value = WeatherState.Loading
            val newState = weatherManager.refreshWeather()
            _weatherState.value = newState

            // 同时刷新预报
            loadWeatherForecast()
        }
    }

    fun setApiKey(apiKey: String) {
        viewModelScope.launch {
            try {
                weatherManager.setApiKey(apiKey)
                // API密钥设置后自动刷新天气
                refreshWeather()
            } catch (e: Exception) {
                _weatherState.value = WeatherState.Error("设置API密钥失败: ${e.message}")
            }
        }
    }
}
