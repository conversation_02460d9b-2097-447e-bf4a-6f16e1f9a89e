package com.livewallpaper.app

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.app.ui.components.*
import com.livewallpaper.core.data.model.MusicInfo
import com.livewallpaper.core.data.model.MusicVisualizationData
import com.livewallpaper.core.domain.music.MusicState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * 音乐系统测试Activity
 * 用于验证音乐系统是否正常工作
 */
@AndroidEntryPoint
class MusicTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                MusicTestScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MusicTestScreen(
    onBackClick: () -> Unit,
    viewModel: MusicTestViewModel = viewModel()
) {
    val context = LocalContext.current
    val musicState by viewModel.musicState.collectAsState()
    val visualizationData by viewModel.visualizationData.collectAsState()
    val hasNotificationPermission by viewModel.hasNotificationPermission.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("音乐系统测试") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                            val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
                            context.startActivity(intent)
                        }
                    ) {
                        Icon(Icons.Default.Settings, contentDescription = "通知设置")
                    }
                    IconButton(onClick = { viewModel.refreshMusicState() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 权限状态卡片
            NotificationPermissionCard(
                hasPermission = hasNotificationPermission,
                onOpenSettings = {
                    val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
                    context.startActivity(intent)
                }
            )

            // 音乐状态卡片
            MusicStateCard(musicState = musicState)

            // 详细信息卡片
            when (musicState) {
                is MusicState.Success -> {
                    val musicInfo = musicState.musicInfo
                    MusicDetailsCard(musicInfo)
                }
                is MusicState.Error -> {
                    val message = musicState.message
                    ErrorCard(message)
                }
                is MusicState.Loading -> {
                    LoadingCard()
                }
                is MusicState.NoMusic -> {
                    NoMusicCard()
                }
            }

            // 可视化数据卡片
            VisualizationDataCard(visualizationData = visualizationData)

            // 支持的应用列表
            SupportedAppsCard()
        }
    }
}

@Composable
fun NotificationPermissionCard(
    hasPermission: Boolean,
    onOpenSettings: () -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "通知监听权限",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (hasPermission) "已授权" else "未授权",
                    color = if (hasPermission) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                )

                if (!hasPermission) {
                    Button(onClick = onOpenSettings) {
                        Text("授权")
                    }
                }
            }

            if (!hasPermission) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "需要通知监听权限才能获取音乐信息",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun MusicStateCard(musicState: MusicState) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "音乐状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val statusText = when (musicState) {
                is MusicState.Loading -> "加载中..."
                is MusicState.Success -> if (musicState.musicInfo.isPlaying) "播放中" else "已暂停"
                is MusicState.Error -> "错误"
                is MusicState.NoMusic -> "无音乐"
            }

            val statusColor = when (musicState) {
                is MusicState.Loading -> MaterialTheme.colorScheme.secondary
                is MusicState.Success -> if (musicState.musicInfo.isPlaying) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline
                is MusicState.Error -> MaterialTheme.colorScheme.error
                is MusicState.NoMusic -> MaterialTheme.colorScheme.onSurfaceVariant
            }

            Text(
                text = statusText,
                color = statusColor,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun MusicDetailsCard(musicInfo: MusicInfo) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "音乐详情",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            InfoRow("歌曲", musicInfo.title)
            InfoRow("艺术家", musicInfo.artist)
            InfoRow("专辑", musicInfo.album)
            InfoRow("应用", musicInfo.packageName)
            InfoRow("状态", if (musicInfo.isPlaying) "播放中" else "已暂停")
            InfoRow("时长", musicInfo.getFormattedDuration())
            InfoRow("位置", musicInfo.getFormattedPosition())
            InfoRow("进度", "${(musicInfo.getProgress() * 100).toInt()}%")
            InfoRow("更新时间", formatTimestamp(Instant.fromEpochMilliseconds(musicInfo.timestamp)))
        }
    }
}

@Composable
fun VisualizationDataCard(visualizationData: MusicVisualizationData) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "可视化数据",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            InfoRow("播放状态", if (visualizationData.isPlaying) "播放中" else "已停止")
            InfoRow("音量", "${(visualizationData.volume * 100).toInt()}%")
            InfoRow("进度", "${(visualizationData.progress * 100).toInt()}%")
            InfoRow("低音", "${(visualizationData.bassLevel * 100).toInt()}%")
            InfoRow("中音", "${(visualizationData.midLevel * 100).toInt()}%")
            InfoRow("高音", "${(visualizationData.trebleLevel * 100).toInt()}%")
            InfoRow("整体强度", "${(visualizationData.getOverallIntensity() * 100).toInt()}%")
        }
    }
}

@Composable
fun SupportedAppsCard() {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "支持的音乐应用",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val supportedApps = listOf(
                "Spotify", "YouTube Music", "网易云音乐", "QQ音乐",
                "酷狗音乐", "酷我音乐", "Apple Music", "Amazon Music",
                "Google Play Music", "小米音乐", "华为音乐", "系统音乐"
            )

            supportedApps.chunked(3).forEach { row ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    row.forEach { app ->
                        Text(
                            text = app,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.weight(1f)
                        )
                    }
                    // 填充空白
                    repeat(3 - row.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

@Composable
fun NoMusicCard() {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "无音乐播放",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "请打开支持的音乐应用并播放音乐",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}



private fun formatTimestamp(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}"
}
