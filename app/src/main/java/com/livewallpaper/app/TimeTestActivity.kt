package com.livewallpaper.app

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.app.ui.components.*
import com.livewallpaper.core.data.model.TimePhase
import com.livewallpaper.core.domain.time.WallpaperTimeState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * 时间系统测试Activity
 * 用于验证时间和天文计算系统是否正常工作
 */
@AndroidEntryPoint
class TimeTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                TimeTestScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeTestScreen(
    onBackClick: () -> Unit,
    viewModel: TimeTestViewModel = viewModel()
) {
    val context = LocalContext.current
    val timeState by viewModel.timeState.collectAsState()
    val hasLocationPermission by viewModel.hasLocationPermission.collectAsState()

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val granted = permissions.values.any { it }
        if (granted) {
            viewModel.refreshTimeState()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("时间系统测试") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshTimeState() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 权限状态卡片
            PermissionStatusCard(
                hasPermission = hasLocationPermission,
                onRequestPermission = {
                    permissionLauncher.launch(
                        arrayOf(
                            Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_COARSE_LOCATION
                        )
                    )
                }
            )

            // 时间状态卡片
            TimeStateCard(timeState = timeState)

            // 详细信息卡片
            when (timeState) {
                is WallpaperTimeState.Success -> {
                    TimeDetailsCard(timeState)
                    LocationDetailsCard(timeState)
                    SolarEventsCard(timeState)
                }
                is WallpaperTimeState.Error -> {
                    ErrorCard(timeState.message)
                }
                is WallpaperTimeState.Loading -> {
                    LoadingCard()
                }
            }
        }
    }
}

@Composable
fun PermissionStatusCard(
    hasPermission: Boolean,
    onRequestPermission: () -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "位置权限状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (hasPermission) "已授权" else "未授权",
                    color = if (hasPermission) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                )

                if (!hasPermission) {
                    Button(onClick = onRequestPermission) {
                        Text("请求权限")
                    }
                }
            }
        }
    }
}

@Composable
fun TimeStateCard(timeState: WallpaperTimeState) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val statusText = when (timeState) {
                is WallpaperTimeState.Loading -> "加载中..."
                is WallpaperTimeState.Success -> "正常"
                is WallpaperTimeState.Error -> "错误"
            }

            val statusColor = when (timeState) {
                is WallpaperTimeState.Loading -> MaterialTheme.colorScheme.secondary
                is WallpaperTimeState.Success -> MaterialTheme.colorScheme.primary
                is WallpaperTimeState.Error -> MaterialTheme.colorScheme.error
            }

            Text(
                text = statusText,
                color = statusColor,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun TimeDetailsCard(timeState: WallpaperTimeState.Success) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间详情",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val timeProgress = timeState.timeProgress

            InfoRow("时间进度", "${(timeProgress.progress * 100).toInt()}%")
            InfoRow("时间阶段", getPhaseDisplayName(timeProgress.phase))
            InfoRow("是否白天", if (timeProgress.isDay) "是" else "否")
            InfoRow("日出时间", "${timeProgress.sunriseTime.toInt()}:00")
            InfoRow("日落时间", "${timeProgress.sunsetTime.toInt()}:00")
            InfoRow("更新时间", formatTimestamp(timeState.timestamp))
        }
    }
}

@Composable
fun LocationDetailsCard(timeState: WallpaperTimeState.Success) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "位置信息",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val location = timeState.location

            InfoRow("城市", location.city)
            InfoRow("国家", location.country)
            InfoRow("纬度", "%.4f".format(location.latitude))
            InfoRow("经度", "%.4f".format(location.longitude))
            InfoRow("时区", location.timezone)
        }
    }
}

@Composable
fun SolarEventsCard(timeState: WallpaperTimeState.Success) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "太阳事件",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))

            val timeProgress = timeState.timeProgress
            InfoRow("日出时间", "${timeProgress.sunriseTime.toInt()}:00")
            InfoRow("日落时间", "${timeProgress.sunsetTime.toInt()}:00")
            InfoRow("光照强度", "${(timeProgress.getLightIntensity() * 100).toInt()}%")
            InfoRow("色温", "${timeProgress.getColorTemperature().toInt()}K")
        }
    }
}





private fun getPhaseDisplayName(phase: TimePhase): String {
    return when (phase) {
        TimePhase.NIGHT -> "夜晚"
        TimePhase.DAWN -> "黎明"
        TimePhase.DUSK -> "黄昏"
        TimePhase.DAY -> "白天"
        TimePhase.UNKNOWN -> "未知"
    }
}

private fun formatTimestamp(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}:${localDateTime.second.toString().padStart(2, '0')}"
}

private fun formatTime(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}"
}
