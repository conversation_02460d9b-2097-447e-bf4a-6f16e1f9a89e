package com.livewallpaper.app

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.core.domain.time.TimePhase
import com.livewallpaper.core.domain.time.WallpaperTimeState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * 时间系统测试Activity
 * 用于验证时间和天文计算系统是否正常工作
 */
@AndroidEntryPoint
class TimeTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                TimeTestScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeTestScreen(
    onBackClick: () -> Unit,
    viewModel: TimeTestViewModel = viewModel()
) {
    val context = LocalContext.current
    val timeState by viewModel.timeState.collectAsState()
    val hasLocationPermission by viewModel.hasLocationPermission.collectAsState()
    
    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val granted = permissions.values.any { it }
        if (granted) {
            viewModel.refreshTimeState()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("时间系统测试") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshTimeState() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 权限状态卡片
            PermissionStatusCard(
                hasPermission = hasLocationPermission,
                onRequestPermission = {
                    permissionLauncher.launch(
                        arrayOf(
                            Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_COARSE_LOCATION
                        )
                    )
                }
            )
            
            // 时间状态卡片
            TimeStateCard(timeState = timeState)
            
            // 详细信息卡片
            when (timeState) {
                is WallpaperTimeState.Success -> {
                    TimeDetailsCard(timeState)
                    LocationDetailsCard(timeState)
                    SolarEventsCard(timeState)
                }
                is WallpaperTimeState.Error -> {
                    ErrorCard(timeState.message)
                }
                is WallpaperTimeState.Loading -> {
                    LoadingCard()
                }
            }
        }
    }
}

@Composable
fun PermissionStatusCard(
    hasPermission: Boolean,
    onRequestPermission: () -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "位置权限状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (hasPermission) "已授权" else "未授权",
                    color = if (hasPermission) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                )
                
                if (!hasPermission) {
                    Button(onClick = onRequestPermission) {
                        Text("请求权限")
                    }
                }
            }
        }
    }
}

@Composable
fun TimeStateCard(timeState: WallpaperTimeState) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            val statusText = when (timeState) {
                is WallpaperTimeState.Loading -> "加载中..."
                is WallpaperTimeState.Success -> "正常"
                is WallpaperTimeState.Error -> "错误"
            }
            
            val statusColor = when (timeState) {
                is WallpaperTimeState.Loading -> MaterialTheme.colorScheme.secondary
                is WallpaperTimeState.Success -> MaterialTheme.colorScheme.primary
                is WallpaperTimeState.Error -> MaterialTheme.colorScheme.error
            }
            
            Text(
                text = statusText,
                color = statusColor,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
fun TimeDetailsCard(timeState: WallpaperTimeState.Success) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间详情",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            val timeProgress = timeState.timeProgress
            
            InfoRow("时间进度", "${(timeProgress.progress * 100).toInt()}%")
            InfoRow("时间阶段", getPhaseDisplayName(timeProgress.phase))
            InfoRow("是否白天", if (timeProgress.isDay) "是" else "否")
            InfoRow("太阳高度角", "${timeProgress.sunElevation.toInt()}°")
            InfoRow("更新时间", formatTimestamp(timeState.timestamp))
        }
    }
}

@Composable
fun LocationDetailsCard(timeState: WallpaperTimeState.Success) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "位置信息",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            val location = timeState.location
            
            InfoRow("城市", location.city)
            InfoRow("国家", location.country)
            InfoRow("纬度", "%.4f".format(location.latitude))
            InfoRow("经度", "%.4f".format(location.longitude))
            InfoRow("时区", location.timezone)
        }
    }
}

@Composable
fun SolarEventsCard(timeState: WallpaperTimeState.Success) {
    val solarEvents = timeState.timeProgress.solarEvents
    if (solarEvents == null) return
    
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "太阳事件",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            solarEvents.sunrise?.let { InfoRow("日出", formatTime(it)) }
            solarEvents.noon?.let { InfoRow("正午", formatTime(it)) }
            solarEvents.sunset?.let { InfoRow("日落", formatTime(it)) }
            solarEvents.nadir?.let { InfoRow("子夜", formatTime(it)) }
            solarEvents.civilTwilightDawn?.let { InfoRow("民用晨光", formatTime(it)) }
            solarEvents.civilTwilightDusk?.let { InfoRow("民用暮光", formatTime(it)) }
        }
    }
}

@Composable
fun ErrorCard(message: String) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "错误",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
        }
    }
}

@Composable
fun LoadingCard() {
    Card {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}

@Composable
fun InfoRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

private fun getPhaseDisplayName(phase: TimePhase): String {
    return when (phase) {
        TimePhase.NIGHT -> "夜晚"
        TimePhase.ASTRONOMICAL_TWILIGHT -> "天文暮光"
        TimePhase.NAUTICAL_TWILIGHT -> "航海暮光"
        TimePhase.CIVIL_TWILIGHT -> "民用暮光"
        TimePhase.DAY -> "白天"
        TimePhase.UNKNOWN -> "未知"
    }
}

private fun formatTimestamp(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}:${localDateTime.second.toString().padStart(2, '0')}"
}

private fun formatTime(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.hour.toString().padStart(2, '0')}:${localDateTime.minute.toString().padStart(2, '0')}"
}
