package com.livewallpaper.app

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.core.data.model.Scene
import dagger.hilt.android.AndroidEntryPoint

/**
 * 自定义场景管理页面
 */
@AndroidEntryPoint
class CustomSceneActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                CustomSceneScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomSceneScreen(
    onBackClick: () -> Unit,
    viewModel: CustomSceneViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val customScenes by viewModel.customScenes.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val errorMessage by viewModel.errorMessage.collectAsStateWithLifecycle()

    var showCreateDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var selectedScene by remember { mutableStateOf<Scene?>(null) }

    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { viewModel.setSelectedImageUri(it) }
    }

    LaunchedEffect(Unit) {
        viewModel.loadCustomScenes()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("自定义场景") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { showCreateDialog = true }) {
                        Icon(Icons.Default.Add, contentDescription = "添加场景")
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { showCreateDialog = true }
            ) {
                Icon(Icons.Default.Add, contentDescription = "添加场景")
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (customScenes.isEmpty()) {
                EmptyCustomScenesView(
                    onCreateClick = { showCreateDialog = true }
                )
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(customScenes) { scene ->
                        CustomSceneCard(
                            scene = scene,
                            onEditClick = {
                                selectedScene = scene
                                showEditDialog = true
                            },
                            onDeleteClick = { viewModel.deleteCustomScene(scene.id) },
                            onSelectClick = { viewModel.selectScene(scene.id) }
                        )
                    }
                }
            }

            // 错误消息
            errorMessage?.let { message ->
                Snackbar(
                    modifier = Modifier.align(Alignment.BottomCenter),
                    action = {
                        TextButton(onClick = { viewModel.clearErrorMessage() }) {
                            Text("关闭")
                        }
                    }
                ) {
                    Text(message)
                }
            }
        }
    }

    // 创建场景对话框
    if (showCreateDialog) {
        CreateCustomSceneDialog(
            onDismiss = { showCreateDialog = false },
            onSelectImage = { imagePickerLauncher.launch("image/*") },
            onCreateScene = { name, description ->
                viewModel.createCustomScene(name, description)
                showCreateDialog = false
            },
            viewModel = viewModel
        )
    }

    // 编辑场景对话框
    if (showEditDialog && selectedScene != null) {
        EditCustomSceneDialog(
            scene = selectedScene!!,
            onDismiss = {
                showEditDialog = false
                selectedScene = null
            },
            onSelectImage = { imagePickerLauncher.launch("image/*") },
            onUpdateScene = { name, description ->
                viewModel.editCustomScene(selectedScene!!.id, name, description)
                showEditDialog = false
                selectedScene = null
            },
            viewModel = viewModel
        )
    }
}

@Composable
fun EmptyCustomScenesView(onCreateClick: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Image,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "还没有自定义场景",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "使用您喜欢的图片创建独特的壁纸场景",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(24.dp))
        Button(onClick = onCreateClick) {
            Icon(Icons.Default.Add, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("创建场景")
        }
    }
}

@Composable
fun CustomSceneCard(
    scene: Scene,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onSelectClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = scene.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    if (scene.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = scene.description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Row {
                    IconButton(onClick = onEditClick) {
                        Icon(Icons.Default.Edit, contentDescription = "编辑")
                    }
                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                OutlinedButton(
                    onClick = onSelectClick,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("应用场景")
                }
            }
        }
    }
}

@Composable
fun CreateCustomSceneDialog(
    onDismiss: () -> Unit,
    onSelectImage: () -> Unit,
    onCreateScene: (String, String) -> Unit,
    viewModel: CustomSceneViewModel
) {
    var sceneName by remember { mutableStateOf("") }
    var sceneDescription by remember { mutableStateOf("") }
    val selectedImageUri by viewModel.selectedImageUri.collectAsStateWithLifecycle()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("创建自定义场景") },
        text = {
            Column {
                OutlinedTextField(
                    value = sceneName,
                    onValueChange = { sceneName = it },
                    label = { Text("场景名称") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = sceneDescription,
                    onValueChange = { sceneDescription = it },
                    label = { Text("场景描述（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = onSelectImage,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Image, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(if (selectedImageUri != null) "更换图片" else "选择图片")
                }

                if (selectedImageUri != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "✓ 已选择图片",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onCreateScene(sceneName, sceneDescription) },
                enabled = sceneName.isNotBlank() && selectedImageUri != null
            ) {
                Text("创建")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
fun EditCustomSceneDialog(
    scene: Scene,
    onDismiss: () -> Unit,
    onSelectImage: () -> Unit,
    onUpdateScene: (String, String) -> Unit,
    viewModel: CustomSceneViewModel
) {
    var sceneName by remember { mutableStateOf(scene.name) }
    var sceneDescription by remember { mutableStateOf(scene.description) }
    val selectedImageUri by viewModel.selectedImageUri.collectAsStateWithLifecycle()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("编辑场景") },
        text = {
            Column {
                OutlinedTextField(
                    value = sceneName,
                    onValueChange = { sceneName = it },
                    label = { Text("场景名称") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = sceneDescription,
                    onValueChange = { sceneDescription = it },
                    label = { Text("场景描述") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = onSelectImage,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Image, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("更换背景图片")
                }

                if (selectedImageUri != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "✓ 已选择新图片",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onUpdateScene(sceneName, sceneDescription) },
                enabled = sceneName.isNotBlank()
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
