package com.livewallpaper.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.core.data.model.*
import dagger.hilt.android.AndroidEntryPoint

/**
 * 设置界面Activity
 */
@AndroidEntryPoint
class SettingsActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                SettingsScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit,
    viewModel: SettingsViewModel = viewModel()
) {
    val settings by viewModel.settings.collectAsState()
    val settingsStats by viewModel.settingsStats.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设置") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.resetToDefaults() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "重置")
                    }
                }
            )
        }
    ) { innerPadding ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 设置统计卡片
            item {
                SettingsStatsCard(stats = settingsStats)
            }

            // 设置分类
            items(SettingsCategory.values()) { category ->
                SettingsCategoryCard(
                    category = category,
                    settings = settings,
                    onSettingChanged = { key, value ->
                        viewModel.updateSetting(key, value)
                    }
                )
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
fun SettingsStatsCard(stats: Map<String, Any>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "设置概览",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "已启用功能",
                    value = "${stats["enabled_features"] ?: 0}",
                    icon = "✅"
                )
                StatItem(
                    label = "性能等级",
                    value = stats["performance_level"]?.toString() ?: "未知",
                    icon = "⚡"
                )
                StatItem(
                    label = "画质",
                    value = stats["quality_level"]?.toString() ?: "高",
                    icon = "🎨"
                )
                StatItem(
                    label = "帧率",
                    value = "${stats["frame_rate"] ?: 30}fps",
                    icon = "📱"
                )
            }
        }
    }
}

@Composable
fun StatItem(
    label: String,
    value: String,
    icon: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = icon,
            style = MaterialTheme.typography.headlineSmall
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun SettingsCategoryCard(
    category: SettingsCategory,
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            // 分类标题
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = category.icon,
                        style = MaterialTheme.typography.headlineSmall
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = category.displayName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }

                IconButton(onClick = { expanded = !expanded }) {
                    Icon(
                        imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (expanded) "收起" else "展开"
                    )
                }
            }

            // 设置项列表
            if (expanded) {
                Column(
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                ) {
                    when (category) {
                        SettingsCategory.GENERAL -> {
                            GeneralSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.SCENE -> {
                            SceneSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.TIME -> {
                            TimeSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.WEATHER -> {
                            WeatherSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.MUSIC -> {
                            MusicSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.PERFORMANCE -> {
                            PerformanceSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.DISPLAY -> {
                            DisplaySettings(settings, onSettingChanged)
                        }
                        SettingsCategory.THEME -> {
                            ThemeSettings(settings, onSettingChanged)
                        }
                        SettingsCategory.ADVANCED -> {
                            AdvancedSettings(settings, onSettingChanged)
                        }
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
fun GeneralSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "启用动态壁纸",
        description = "开启或关闭动态壁纸功能",
        checked = settings.isEnabled,
        onCheckedChange = { onSettingChanged("isEnabled", it) }
    )

    SliderSetting(
        title = "帧率",
        description = "调整动画帧率，影响流畅度和耗电",
        value = settings.frameRate.toFloat(),
        valueRange = 15f..60f,
        steps = 8,
        unit = "fps",
        onValueChange = { onSettingChanged("frameRate", it.toInt()) }
    )

    SelectionSetting(
        title = "渲染质量",
        description = "选择渲染质量等级",
        selectedValue = settings.quality,
        options = RenderQuality.values().map {
            SettingOption(it, it.displayName, "缩放比例: ${it.scale}x")
        },
        onSelectionChange = { onSettingChanged("quality", it) }
    )
}

@Composable
fun SceneSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "自动场景切换",
        description = "根据时间和天气自动切换场景",
        checked = settings.enableAutoSceneSwitch,
        onCheckedChange = { onSettingChanged("enableAutoSceneSwitch", it) }
    )

    SwitchSetting(
        title = "视差效果",
        description = "启用场景的视差滚动效果",
        checked = settings.enableParallaxEffect,
        onCheckedChange = { onSettingChanged("enableParallaxEffect", it) }
    )

    if (settings.enableParallaxEffect) {
        SliderSetting(
            title = "视差强度",
            description = "调整视差效果的强度",
            value = settings.parallaxIntensity,
            valueRange = 0f..2f,
            steps = 19,
            onValueChange = { onSettingChanged("parallaxIntensity", it) }
        )
    }
}

@Composable
fun TimeSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "时间感知场景",
        description = "根据当前时间选择合适的场景",
        checked = settings.enableTimeBasedScenes,
        onCheckedChange = { onSettingChanged("enableTimeBasedScenes", it) }
    )

    SwitchSetting(
        title = "季节感知场景",
        description = "根据当前季节调整场景样式",
        checked = settings.enableSeasonalScenes,
        onCheckedChange = { onSettingChanged("enableSeasonalScenes", it) }
    )
}

@Composable
fun WeatherSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "天气效果",
        description = "显示雨雪等天气视觉效果",
        checked = settings.enableWeatherEffects,
        onCheckedChange = { onSettingChanged("enableWeatherEffects", it) }
    )

    SwitchSetting(
        title = "天气感知场景",
        description = "根据当前天气选择合适的场景",
        checked = settings.enableWeatherBasedScenes,
        onCheckedChange = { onSettingChanged("enableWeatherBasedScenes", it) }
    )

    SwitchSetting(
        title = "位置服务",
        description = "允许获取位置信息以获取天气数据",
        checked = settings.enableLocationServices,
        onCheckedChange = { onSettingChanged("enableLocationServices", it) }
    )
}

@Composable
fun MusicSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "音乐可视化",
        description = "显示正在播放的音乐信息",
        checked = settings.enableMusicVisualization,
        onCheckedChange = { onSettingChanged("enableMusicVisualization", it) }
    )

    if (settings.enableMusicVisualization) {
        SelectionSetting(
            title = "音乐卡片位置",
            description = "选择音乐信息卡片的显示位置",
            selectedValue = settings.musicCardPosition,
            options = CardPosition.values().map {
                SettingOption(it, it.name.replace("_", " "))
            },
            onSelectionChange = { onSettingChanged("musicCardPosition", it) }
        )

        SliderSetting(
            title = "卡片透明度",
            description = "调整音乐卡片的透明度",
            value = settings.musicCardOpacity,
            valueRange = 0.1f..1f,
            steps = 17,
            onValueChange = { onSettingChanged("musicCardOpacity", it) }
        )

        SwitchSetting(
            title = "音频可视化",
            description = "显示音频频谱动画效果",
            checked = settings.enableAudioVisualization,
            onCheckedChange = { onSettingChanged("enableAudioVisualization", it) }
        )
    }
}

@Composable
fun PerformanceSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "电池优化",
        description = "在低电量时自动降低性能",
        checked = settings.enableBatteryOptimization,
        onCheckedChange = { onSettingChanged("enableBatteryOptimization", it) }
    )

    SwitchSetting(
        title = "低功耗模式",
        description = "强制使用最低性能设置",
        checked = settings.enableLowPowerMode,
        onCheckedChange = { onSettingChanged("enableLowPowerMode", it) }
    )

    SliderSetting(
        title = "内存限制",
        description = "设置最大内存使用量",
        value = settings.maxMemoryUsage.toFloat(),
        valueRange = 50f..200f,
        steps = 14,
        unit = "MB",
        onValueChange = { onSettingChanged("maxMemoryUsage", it.toInt()) }
    )
}

@Composable
fun DisplaySettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "调试信息",
        description = "显示性能和状态信息",
        checked = settings.enableDebugInfo,
        onCheckedChange = { onSettingChanged("enableDebugInfo", it) }
    )

    if (settings.enableDebugInfo) {
        SwitchSetting(
            title = "帧率计数器",
            description = "显示实时帧率",
            checked = settings.enableFpsCounter,
            onCheckedChange = { onSettingChanged("enableFpsCounter", it) }
        )

        SwitchSetting(
            title = "内存监控",
            description = "显示内存使用情况",
            checked = settings.enableMemoryMonitor,
            onCheckedChange = { onSettingChanged("enableMemoryMonitor", it) }
        )
    }
}

@Composable
fun ThemeSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SelectionSetting(
        title = "主题模式",
        description = "选择应用主题",
        selectedValue = settings.themeMode,
        options = ThemeMode.values().map {
            SettingOption(it, it.displayName)
        },
        onSelectionChange = { onSettingChanged("themeMode", it) }
    )

    SwitchSetting(
        title = "动态颜色",
        description = "使用系统动态颜色主题",
        checked = settings.enableDynamicColors,
        onCheckedChange = { onSettingChanged("enableDynamicColors", it) }
    )
}

@Composable
fun AdvancedSettings(
    settings: WallpaperSettings,
    onSettingChanged: (String, Any) -> Unit
) {
    SwitchSetting(
        title = "实验性功能",
        description = "启用测试中的新功能",
        checked = settings.enableExperimentalFeatures,
        onCheckedChange = { onSettingChanged("enableExperimentalFeatures", it) }
    )

    SwitchSetting(
        title = "数据分析",
        description = "帮助改进应用体验",
        checked = settings.enableAnalytics,
        onCheckedChange = { onSettingChanged("enableAnalytics", it) }
    )

    SwitchSetting(
        title = "崩溃报告",
        description = "自动发送崩溃报告",
        checked = settings.enableCrashReporting,
        onCheckedChange = { onSettingChanged("enableCrashReporting", it) }
    )
}

// 设置组件
@Composable
fun SwitchSetting(
    title: String,
    description: String? = null,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    enabled: Boolean = true
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled
        )
    }
}

@Composable
fun SliderSetting(
    title: String,
    description: String? = null,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    steps: Int = 0,
    unit: String? = null,
    onValueChange: (Float) -> Unit,
    enabled: Boolean = true
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge
                )
                if (description != null) {
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Text(
                text = if (unit != null) "${value.toInt()}$unit" else value.toInt().toString(),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
        }

        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            steps = steps,
            enabled = enabled,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun <T> SelectionSetting(
    title: String,
    description: String? = null,
    selectedValue: T,
    options: List<SettingOption>,
    onSelectionChange: (T) -> Unit,
    enabled: Boolean = true
) {
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge
                )
                if (description != null) {
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            TextButton(
                onClick = { expanded = true },
                enabled = enabled
            ) {
                Text(
                    text = options.find { it.value == selectedValue }?.label ?: "未选择"
                )
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            options.forEach { option ->
                DropdownMenuItem(
                    text = {
                        Column {
                            Text(option.label)
                            if (option.description != null) {
                                Text(
                                    text = option.description,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    },
                    onClick = {
                        @Suppress("UNCHECKED_CAST")
                        onSelectionChange(option.value as T)
                        expanded = false
                    }
                )
            }
        }
    }
}
