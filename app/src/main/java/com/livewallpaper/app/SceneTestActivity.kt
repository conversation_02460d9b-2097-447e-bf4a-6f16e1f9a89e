package com.livewallpaper.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.domain.scene.SceneState
import dagger.hilt.android.AndroidEntryPoint

/**
 * 场景系统测试Activity
 * 用于验证场景管理系统是否正常工作
 */
@AndroidEntryPoint
class SceneTestActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                SceneTestScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SceneTestScreen(
    onBackClick: () -> Unit,
    viewModel: SceneTestViewModel = viewModel()
) {
    val sceneState by viewModel.sceneState.collectAsState()
    val allScenes by viewModel.allScenes.collectAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("场景系统测试") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshScenes() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 当前场景状态卡片
            CurrentSceneCard(sceneState = sceneState)
            
            // 场景列表卡片
            ScenesListCard(
                scenes = allScenes,
                onSceneSelect = { sceneId ->
                    viewModel.switchToScene(sceneId)
                }
            )
            
            // 场景统计卡片
            SceneStatsCard(scenes = allScenes)
        }
    }
}

@Composable
fun CurrentSceneCard(sceneState: SceneState) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "当前场景状态",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            when (sceneState) {
                is SceneState.Loading -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        CircularProgressIndicator(modifier = Modifier.size(16.dp))
                        Text("加载中...")
                    }
                }
                is SceneState.Success -> {
                    val scene = sceneState.scene
                    InfoRow("场景名称", scene.name)
                    InfoRow("场景描述", scene.description)
                    InfoRow("分类", getCategoryDisplayName(scene.category))
                    InfoRow("季节", scene.season?.let { getSeasonDisplayName(it) } ?: "无限制")
                    InfoRow("时间", scene.timeOfDay?.let { getTimeOfDayDisplayName(it) } ?: "无限制")
                    InfoRow("天气", scene.weatherType?.let { getWeatherTypeDisplayName(it) } ?: "无限制")
                    InfoRow("是否高级", if (scene.isPremium) "是" else "否")
                    InfoRow("资源状态", if (sceneState.layers != null) "已加载" else "未加载")
                }
                is SceneState.Error -> {
                    Text(
                        text = "错误: ${sceneState.message}",
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

@Composable
fun ScenesListCard(
    scenes: List<Scene>,
    onSceneSelect: (String) -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "可用场景 (${scenes.size})",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (scenes.isEmpty()) {
                Text(
                    text = "暂无场景数据",
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                LazyColumn(
                    modifier = Modifier.height(300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(scenes) { scene ->
                        SceneItem(
                            scene = scene,
                            onClick = { onSceneSelect(scene.id) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SceneItem(
    scene: Scene,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = scene.name,
                    style = MaterialTheme.typography.titleSmall
                )
                if (scene.isPremium) {
                    AssistChip(
                        onClick = { },
                        label = { Text("高级") }
                    )
                }
            }
            
            Text(
                text = scene.description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                FilterChip(
                    onClick = { },
                    label = { Text(getCategoryDisplayName(scene.category)) },
                    selected = false
                )
                
                scene.timeOfDay?.let { timeOfDay ->
                    FilterChip(
                        onClick = { },
                        label = { Text(getTimeOfDayDisplayName(timeOfDay)) },
                        selected = false
                    )
                }
                
                scene.season?.let { season ->
                    FilterChip(
                        onClick = { },
                        label = { Text(getSeasonDisplayName(season)) },
                        selected = false
                    )
                }
            }
        }
    }
}

@Composable
fun SceneStatsCard(scenes: List<Scene>) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "场景统计",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            InfoRow("总场景数", scenes.size.toString())
            InfoRow("免费场景", scenes.count { !it.isPremium }.toString())
            InfoRow("高级场景", scenes.count { it.isPremium }.toString())
            InfoRow("自然场景", scenes.count { it.category in listOf(SceneCategory.NATURE, SceneCategory.FOREST, SceneCategory.OCEAN, SceneCategory.MOUNTAIN) }.toString())
            InfoRow("城市场景", scenes.count { it.category == SceneCategory.CITY }.toString())
            InfoRow("抽象场景", scenes.count { it.category == SceneCategory.ABSTRACT }.toString())
            InfoRow("太空场景", scenes.count { it.category == SceneCategory.SPACE }.toString())
        }
    }
}

@Composable
fun InfoRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

// 显示名称转换函数
private fun getCategoryDisplayName(category: SceneCategory): String {
    return when (category) {
        SceneCategory.NATURE -> "自然"
        SceneCategory.CITY -> "城市"
        SceneCategory.ABSTRACT -> "抽象"
        SceneCategory.SPACE -> "太空"
        SceneCategory.OCEAN -> "海洋"
        SceneCategory.MOUNTAIN -> "山脉"
        SceneCategory.FOREST -> "森林"
        SceneCategory.DESERT -> "沙漠"
        SceneCategory.CUSTOM -> "自定义"
    }
}

private fun getSeasonDisplayName(season: Season): String {
    return when (season) {
        Season.SPRING -> "春季"
        Season.SUMMER -> "夏季"
        Season.AUTUMN -> "秋季"
        Season.WINTER -> "冬季"
    }
}

private fun getTimeOfDayDisplayName(timeOfDay: TimeOfDay): String {
    return when (timeOfDay) {
        TimeOfDay.DAWN -> "黎明"
        TimeOfDay.MORNING -> "上午"
        TimeOfDay.NOON -> "正午"
        TimeOfDay.AFTERNOON -> "下午"
        TimeOfDay.DUSK -> "黄昏"
        TimeOfDay.NIGHT -> "夜晚"
    }
}

private fun getWeatherTypeDisplayName(weatherType: WeatherType): String {
    return when (weatherType) {
        WeatherType.CLEAR -> "晴朗"
        WeatherType.CLOUDY -> "多云"
        WeatherType.RAINY -> "雨天"
        WeatherType.SNOWY -> "雪天"
        WeatherType.FOGGY -> "雾天"
        WeatherType.STORMY -> "暴风雨"
        WeatherType.WINDY -> "大风"
    }
}
