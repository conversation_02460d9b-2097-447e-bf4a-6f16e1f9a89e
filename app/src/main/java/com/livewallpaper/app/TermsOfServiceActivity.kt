package com.livewallpaper.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.livewallpaper.app.ui.theme.LiveWallpaperTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 服务条款Activity
 */
@AndroidEntryPoint
class TermsOfServiceActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            LiveWallpaperTheme {
                TermsOfServiceScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TermsOfServiceScreen(
    onBackClick: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("服务条款") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "服务条款",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "最后更新：2024年12月",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            PolicySection(
                title = "1. 接受条款",
                content = """
欢迎使用动态壁纸应用。通过下载、安装或使用本应用，您同意遵守本服务条款。

如果您不同意这些条款，请不要使用本应用。
                """.trimIndent()
            )
            
            PolicySection(
                title = "2. 服务描述",
                content = """
动态壁纸是一款为Android设备提供智能动态壁纸的应用程序，主要功能包括：

• 时间感知的场景切换
• 天气同步的视觉效果
• 音乐可视化功能
• 个性化设置选项
• 性能优化功能

我们保留随时修改、暂停或终止服务的权利。
                """.trimIndent()
            )
            
            PolicySection(
                title = "3. 用户责任",
                content = """
使用本应用时，您同意：

• 仅将应用用于合法目的
• 不尝试破解、逆向工程或修改应用
• 不干扰应用的正常运行
• 遵守所有适用的法律法规
• 尊重他人的权利和隐私

违反这些条款可能导致服务终止。
                """.trimIndent()
            )
            
            PolicySection(
                title = "4. 知识产权",
                content = """
本应用及其内容受知识产权法保护：

• 应用代码、设计和功能归我们所有
• 场景图片和音效等素材受版权保护
• 用户不得复制、分发或修改应用内容
• 商标和标识归各自所有者所有

未经授权使用可能面临法律后果。
                """.trimIndent()
            )
            
            PolicySection(
                title = "5. 免责声明",
                content = """
本应用按"现状"提供，我们不提供任何明示或暗示的保证：

• 不保证应用无错误或不间断运行
• 不保证天气数据的准确性
• 不对因使用应用造成的损失负责
• 不保证与所有设备的兼容性

您使用本应用的风险由您自行承担。
                """.trimIndent()
            )
            
            PolicySection(
                title = "6. 责任限制",
                content = """
在法律允许的最大范围内：

• 我们的责任限于您支付的费用金额
• 不对间接、特殊或后果性损害负责
• 不对数据丢失或设备损坏负责
• 不对第三方服务的问题负责

某些司法管辖区不允许责任限制，因此上述限制可能不适用于您。
                """.trimIndent()
            )
            
            PolicySection(
                title = "7. 第三方服务",
                content = """
本应用可能集成第三方服务：

• 天气数据提供商
• 地图和位置服务
• 分析和广告服务
• 支付处理服务

这些服务有各自的条款，您需要遵守相关规定。
                """.trimIndent()
            )
            
            PolicySection(
                title = "8. 隐私保护",
                content = """
我们重视您的隐私：

• 收集的信息仅用于提供服务
• 不会出售您的个人信息
• 采取合理措施保护数据安全
• 详细信息请查看隐私政策

使用本应用即表示您同意我们的隐私政策。
                """.trimIndent()
            )
            
            PolicySection(
                title = "9. 条款修改",
                content = """
我们可能会更新这些服务条款：

• 重大变更将通过应用内通知
• 继续使用表示接受新条款
• 如不同意新条款，请停止使用应用
• 建议定期查看最新条款

最新版本将在应用中发布。
                """.trimIndent()
            )
            
            PolicySection(
                title = "10. 争议解决",
                content = """
如发生争议：

• 首先尝试友好协商解决
• 协商不成可申请仲裁
• 适用中华人民共和国法律
• 争议由北京市朝阳区人民法院管辖

我们致力于公平合理地解决所有争议。
                """.trimIndent()
            )
            
            PolicySection(
                title = "11. 联系信息",
                content = """
如对本条款有疑问，请联系我们：

邮箱：<EMAIL>
地址：中国北京市朝阳区
电话：+86-10-12345678

我们将及时回复您的询问。
                """.trimIndent()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}
