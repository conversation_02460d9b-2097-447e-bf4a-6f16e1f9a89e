package com.livewallpaper.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.performance.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 性能测试ViewModel
 */
@HiltViewModel
class PerformanceTestViewModel @Inject constructor(
    private val performanceMonitor: PerformanceMonitor,
    private val performanceOptimizer: PerformanceOptimizer,
    private val dirtyRegionManager: DirtyRegionManager,
    private val objectPoolManager: ObjectPoolManager
) : ViewModel() {
    
    val performanceMetrics: StateFlow<PerformanceMetrics> = performanceMonitor.performanceMetrics
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PerformanceMetrics()
        )
    
    val performanceLevel: StateFlow<PerformanceLevel> = performanceMetrics
        .map { performanceMonitor.getPerformanceLevel() }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PerformanceLevel.MEDIUM
        )
    
    private val _optimizedSettings = MutableStateFlow<OptimizedRenderSettings?>(null)
    val optimizedSettings: StateFlow<OptimizedRenderSettings?> = _optimizedSettings.asStateFlow()
    
    private val _dirtyRegionStats = MutableStateFlow<DirtyRegionStats?>(null)
    val dirtyRegionStats: StateFlow<DirtyRegionStats?> = _dirtyRegionStats.asStateFlow()
    
    private val _recommendations = MutableStateFlow<List<String>>(emptyList())
    val recommendations: StateFlow<List<String>> = _recommendations.asStateFlow()
    
    private val _objectPoolStats = MutableStateFlow<Map<String, Int>>(emptyMap())
    val objectPoolStats: StateFlow<Map<String, Int>> = _objectPoolStats.asStateFlow()
    
    init {
        startPerformanceMonitoring()
    }
    
    private fun startPerformanceMonitoring() {
        viewModelScope.launch {
            // 定期更新优化设置
            launch {
                while (true) {
                    try {
                        _optimizedSettings.value = performanceOptimizer.getOptimizedSettings()
                        kotlinx.coroutines.delay(5000) // 每5秒更新一次
                    } catch (e: Exception) {
                        // 忽略错误，继续监控
                    }
                }
            }
            
            // 定期更新脏区域统计
            launch {
                while (true) {
                    try {
                        _dirtyRegionStats.value = dirtyRegionManager.getDirtyRegionStats()
                        kotlinx.coroutines.delay(1000) // 每秒更新一次
                    } catch (e: Exception) {
                        // 忽略错误，继续监控
                    }
                }
            }
            
            // 定期更新性能建议
            launch {
                while (true) {
                    try {
                        _recommendations.value = performanceOptimizer.getPerformanceRecommendations()
                        kotlinx.coroutines.delay(10000) // 每10秒更新一次
                    } catch (e: Exception) {
                        // 忽略错误，继续监控
                    }
                }
            }
            
            // 定期更新对象池统计
            launch {
                while (true) {
                    try {
                        _objectPoolStats.value = objectPoolManager.getPoolStats()
                        kotlinx.coroutines.delay(2000) // 每2秒更新一次
                    } catch (e: Exception) {
                        // 忽略错误，继续监控
                    }
                }
            }
        }
    }
    
    /**
     * 触发垃圾回收
     */
    fun triggerGC() {
        viewModelScope.launch {
            performanceMonitor.triggerGC()
        }
    }
    
    /**
     * 重置统计数据
     */
    fun resetStats() {
        viewModelScope.launch {
            performanceMonitor.resetStats()
            dirtyRegionManager.clearDirtyRegions()
        }
    }
    
    /**
     * 清理对象池
     */
    fun clearObjectPools() {
        viewModelScope.launch {
            objectPoolManager.clearAllPools()
        }
    }
}
