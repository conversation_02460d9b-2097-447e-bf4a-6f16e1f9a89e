package com.livewallpaper.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.data.repository.SettingsRepository
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 设置ViewModel
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository
) : ViewModel() {

    val settings: StateFlow<WallpaperSettings> = settingsRepository.settingsFlow
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = WallpaperSettings()
        )

    val settingsStats: StateFlow<Map<String, Any>> = flow {
        while (true) {
            emit(settingsRepository.getSettingsStats())
            kotlinx.coroutines.delay(5000) // 每5秒更新一次统计
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyMap()
    )

    private val _validationErrors = MutableStateFlow<List<String>>(emptyList())
    val validationErrors: StateFlow<List<String>> = _validationErrors.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        validateSettings()
    }

    /**
     * 更新设置项
     */
    fun updateSetting(key: String, value: Any) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                when (key) {
                    // 基础设置
                    "isEnabled" -> settingsRepository.updateBasicSettings(isEnabled = value as Boolean)
                    "frameRate" -> settingsRepository.updateBasicSettings(frameRate = value as Int)
                    "quality" -> settingsRepository.updateBasicSettings(quality = value as RenderQuality)

                    // 场景设置
                    "enableAutoSceneSwitch" -> settingsRepository.updateSceneSettings(enableAutoSceneSwitch = value as Boolean)
                    "sceneChangeInterval" -> settingsRepository.updateSceneSettings(sceneChangeInterval = value as Long)
                    "enableParallaxEffect" -> settingsRepository.updateSceneSettings(enableParallaxEffect = value as Boolean)
                    "parallaxIntensity" -> settingsRepository.updateSceneSettings(parallaxIntensity = value as Float)
                    "preferredSceneCategory" -> settingsRepository.updateSceneSettings(preferredSceneCategory = value as SceneCategory?)

                    // 时间设置
                    "enableTimeBasedScenes" -> settingsRepository.updateTimeSettings(enableTimeBasedScenes = value as Boolean)
                    "enableSeasonalScenes" -> settingsRepository.updateTimeSettings(enableSeasonalScenes = value as Boolean)
                    "userTimeZone" -> settingsRepository.updateTimeSettings(userTimeZone = value as String)
                    "customLatitude" -> settingsRepository.updateTimeSettings(customLatitude = value as Double?)
                    "customLongitude" -> settingsRepository.updateTimeSettings(customLongitude = value as Double?)

                    // 天气设置
                    "enableWeatherEffects" -> settingsRepository.updateWeatherSettings(enableWeatherEffects = value as Boolean)
                    "enableWeatherBasedScenes" -> settingsRepository.updateWeatherSettings(enableWeatherBasedScenes = value as Boolean)
                    "weatherUpdateInterval" -> settingsRepository.updateWeatherSettings(weatherUpdateInterval = value as Long)
                    "weatherApiKey" -> settingsRepository.updateWeatherSettings(weatherApiKey = value as String)
                    "enableLocationServices" -> settingsRepository.updateWeatherSettings(enableLocationServices = value as Boolean)

                    // 音乐设置
                    "enableMusicVisualization" -> settingsRepository.updateMusicSettings(enableMusicVisualization = value as Boolean)
                    "musicCardPosition" -> settingsRepository.updateMusicSettings(musicCardPosition = value as CardPosition)
                    "musicCardOpacity" -> settingsRepository.updateMusicSettings(musicCardOpacity = value as Float)
                    "enableMusicCardAutoHide" -> settingsRepository.updateMusicSettings(enableMusicCardAutoHide = value as Boolean)
                    "musicCardAutoHideDelay" -> settingsRepository.updateMusicSettings(musicCardAutoHideDelay = value as Long)
                    "enableAudioVisualization" -> settingsRepository.updateMusicSettings(enableAudioVisualization = value as Boolean)

                    // 性能设置
                    "enableBatteryOptimization" -> settingsRepository.updatePerformanceSettings(enableBatteryOptimization = value as Boolean)
                    "enableLowPowerMode" -> settingsRepository.updatePerformanceSettings(enableLowPowerMode = value as Boolean)
                    "maxMemoryUsage" -> settingsRepository.updatePerformanceSettings(maxMemoryUsage = value as Int)
                    "enableGpuAcceleration" -> settingsRepository.updatePerformanceSettings(enableGpuAcceleration = value as Boolean)
                    "enableObjectPooling" -> settingsRepository.updatePerformanceSettings(enableObjectPooling = value as Boolean)

                    // 显示设置
                    "enableDebugInfo" -> settingsRepository.updateDisplaySettings(enableDebugInfo = value as Boolean)
                    "debugInfoPosition" -> settingsRepository.updateDisplaySettings(debugInfoPosition = value as DebugInfoPosition)
                    "enableFpsCounter" -> settingsRepository.updateDisplaySettings(enableFpsCounter = value as Boolean)
                    "enableMemoryMonitor" -> settingsRepository.updateDisplaySettings(enableMemoryMonitor = value as Boolean)

                    // 主题设置
                    "themeMode" -> settingsRepository.updateThemeSettings(themeMode = value as ThemeMode)
                    "accentColor" -> settingsRepository.updateThemeSettings(accentColor = value as String)
                    "enableDynamicColors" -> settingsRepository.updateThemeSettings(enableDynamicColors = value as Boolean)

                    // 高级设置
                    "enableExperimentalFeatures" -> settingsRepository.updateAdvancedSettings(enableExperimentalFeatures = value as Boolean)
                    "enableCloudSync" -> settingsRepository.updateAdvancedSettings(enableCloudSync = value as Boolean)
                    "enableAnalytics" -> settingsRepository.updateAdvancedSettings(enableAnalytics = value as Boolean)
                    "enableCrashReporting" -> settingsRepository.updateAdvancedSettings(enableCrashReporting = value as Boolean)

                    else -> {
                        Logger.w("Unknown setting key: $key")
                    }
                }

                // 更新后重新验证设置
                validateSettings()

            } catch (e: Exception) {
                Logger.e("Error updating setting $key", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 重置设置为默认值
     */
    fun resetToDefaults() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.resetToDefaults()
                validateSettings()
                Logger.d("Settings reset to defaults")
            } catch (e: Exception) {
                Logger.e("Error resetting settings", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 验证设置
     */
    private fun validateSettings() {
        viewModelScope.launch {
            try {
                val errors = settingsRepository.validateSettings()
                _validationErrors.value = errors

                if (errors.isNotEmpty()) {
                    Logger.w("Settings validation errors: $errors")
                }
            } catch (e: Exception) {
                Logger.e("Error validating settings", e)
                _validationErrors.value = listOf("设置验证失败: ${e.message}")
            }
        }
    }

    /**
     * 导出设置
     */
    suspend fun exportSettings(): Map<String, Any> {
        return try {
            settingsRepository.exportSettings()
        } catch (e: Exception) {
            Logger.e("Error exporting settings", e)
            emptyMap()
        }
    }

    /**
     * 获取设置摘要
     */
    fun getSettingsSummary(): String {
        return try {
            val currentSettings = settings.value
            buildString {
                appendLine("动态壁纸设置摘要")
                appendLine("==================")
                appendLine("状态: ${if (currentSettings.isEnabled) "启用" else "禁用"}")
                appendLine("帧率: ${currentSettings.frameRate}fps")
                appendLine("质量: ${currentSettings.quality.displayName}")
                appendLine("自动场景切换: ${if (currentSettings.enableAutoSceneSwitch) "开启" else "关闭"}")
                appendLine("天气效果: ${if (currentSettings.enableWeatherEffects) "开启" else "关闭"}")
                appendLine("音乐可视化: ${if (currentSettings.enableMusicVisualization) "开启" else "关闭"}")
                appendLine("电池优化: ${if (currentSettings.enableBatteryOptimization) "开启" else "关闭"}")
                appendLine("主题模式: ${currentSettings.themeMode.displayName}")
            }
        } catch (e: Exception) {
            Logger.e("Error generating settings summary", e)
            "设置摘要生成失败"
        }
    }

    /**
     * 检查是否需要重启应用
     */
    fun requiresRestart(): Boolean {
        // 某些设置更改需要重启应用才能生效
        return false // 简化处理，实际可以根据具体设置判断
    }

    /**
     * 获取性能建议
     */
    fun getPerformanceRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val currentSettings = settings.value

        try {
            if (currentSettings.frameRate > 30 && !currentSettings.enableBatteryOptimization) {
                recommendations.add("建议开启电池优化以延长续航")
            }

            if (currentSettings.quality == RenderQuality.ULTRA && currentSettings.maxMemoryUsage < 150) {
                recommendations.add("超高画质需要更多内存，建议增加内存限制")
            }

            if (currentSettings.enableParallaxEffect && currentSettings.parallaxIntensity > 1.5f) {
                recommendations.add("高强度视差效果可能影响性能")
            }

            if (!currentSettings.enableObjectPooling) {
                recommendations.add("建议开启对象池以减少内存分配")
            }

            if (currentSettings.enableDebugInfo && !currentSettings.enableExperimentalFeatures) {
                recommendations.add("调试信息仅在开发时使用，建议关闭")
            }

        } catch (e: Exception) {
            Logger.e("Error generating performance recommendations", e)
            recommendations.add("性能建议生成失败")
        }

        return recommendations
    }
}
