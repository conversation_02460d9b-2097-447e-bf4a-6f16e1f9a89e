<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- 读取外部存储权限（用于自定义壁纸） -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- 通知监听权限（用于音乐可视化） -->
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />

    <!-- 壁纸权限 -->
    <uses-permission android:name="android.permission.BIND_WALLPAPER" />

    <application
        android:name=".MainApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LiveWallpaper"
        tools:targetApi="31">

        <!-- 主Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.LiveWallpaper">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 壁纸预览Activity -->
        <activity
            android:name=".WallpaperPreviewActivity"
            android:exported="true"
            android:label="@string/wallpaper_preview"
            android:theme="@style/Theme.LiveWallpaper">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>

        <!-- 时间系统测试Activity -->
        <activity
            android:name=".TimeTestActivity"
            android:exported="false"
            android:label="时间系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 场景系统测试Activity -->
        <activity
            android:name=".SceneTestActivity"
            android:exported="false"
            android:label="场景系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 天气系统测试Activity -->
        <activity
            android:name=".WeatherTestActivity"
            android:exported="false"
            android:label="天气系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 音乐系统测试Activity -->
        <activity
            android:name=".MusicTestActivity"
            android:exported="false"
            android:label="音乐系统测试"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 设置Activity -->
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="设置"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 性能测试Activity -->
        <activity
            android:name=".PerformanceTestActivity"
            android:exported="false"
            android:label="性能监控"
            android:theme="@style/Theme.LiveWallpaper" />

        <!-- 音乐通知监听服务 -->
        <service
            android:name="com.livewallpaper.core.service.MusicNotificationListenerService"
            android:exported="false"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

    </application>

</manifest>
