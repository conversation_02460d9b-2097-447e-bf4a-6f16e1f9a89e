# 动态壁纸应用 (LiveWallpaper)

一款智能动态壁纸应用，根据时间、天气和音乐自动变化。

## 项目结构

```
LiveWallpaper/
├── app/                          # 主应用模块
│   ├── src/main/
│   │   ├── java/com/livewallpaper/app/
│   │   │   ├── MainActivity.kt           # 主Activity
│   │   │   ├── WallpaperPreviewActivity.kt # 壁纸预览Activity
│   │   │   ├── MainApplication.kt        # 应用程序类
│   │   │   └── ui/theme/                 # UI主题
│   │   ├── res/                          # 资源文件
│   │   └── AndroidManifest.xml
│   └── build.gradle.kts
├── core/                         # 核心模块
│   ├── src/main/java/com/livewallpaper/core/
│   │   └── utils/                        # 工具类
│   │       ├── Logger.kt                 # 日志工具
│   │       └── Resource.kt               # 资源封装类
│   └── build.gradle.kts
├── features/                     # 功能模块
│   ├── wallpaper/                        # 壁纸功能模块
│   │   ├── src/main/java/com/livewallpaper/features/wallpaper/
│   │   │   └── LiveWallpaperService.kt   # 壁纸服务
│   │   ├── src/main/res/
│   │   │   ├── xml/wallpaper.xml         # 壁纸配置
│   │   │   └── values/strings.xml
│   │   └── build.gradle.kts
│   ├── weather/                          # 天气功能模块
│   │   └── build.gradle.kts
│   └── music/                            # 音乐功能模块
│       └── build.gradle.kts
├── gradle/
│   └── libs.versions.toml                # 版本管理
├── build.gradle.kts                      # 项目级构建文件
├── settings.gradle.kts                   # 项目设置
├── gradle.properties                     # Gradle属性
├── .gitignore                           # Git忽略文件
└── startup.md                           # 开发指南
```

## 技术栈

- **语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构**: MVVM + Repository Pattern
- **依赖注入**: Hilt
- **网络**: Retrofit + OkHttp
- **数据库**: Room
- **数据存储**: DataStore
- **图片加载**: Coil
- **动画**: Lottie
- **位置服务**: Google Play Services Location

## 开发阶段

### ✅ 阶段零：项目准备与环境搭建
- [x] 创建 Android Studio 项目
- [x] 配置 Gradle 和依赖库
- [x] 搭建模块化架构
- [x] 配置 Hilt
- [x] 实现基础工具类
- [x] 提交到 Git 仓库

### ✅ 阶段一：核心壁纸引擎开发 (部分完成)
- [x] 创建 WallpaperService
- [x] 实现 Engine
- [x] 建立渲染线程
- [x] 注册服务
- [x] 创建壁纸设置预览 Activity
- [x] 内存管理初步

### 🔄 下一步计划
- 阶段二：时间与天文系统
- 阶段三：场景管理系统
- 阶段四：天气系统集成
- 阶段五：音乐可视化系统

## 如何运行

1. 克隆项目到本地
2. 使用 Android Studio 打开项目
3. 等待 Gradle 同步完成
4. 连接 Android 设备或启动模拟器
5. 运行应用

## 设置动态壁纸

1. 运行应用
2. 点击"设置为壁纸"按钮
3. 在系统壁纸选择界面中选择"智能动态壁纸"
4. 点击"设置壁纸"

## 许可证

本项目仅供学习和开发参考使用。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题，请通过 GitHub Issues 联系。
