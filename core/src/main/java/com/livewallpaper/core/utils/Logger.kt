package com.livewallpaper.core.utils

import android.util.Log

/**
 * 统一日志管理工具
 * 提供开关控制和统一TAG
 */
object Logger {
    private const val TAG = "LiveWallpaper"
    private var isDebugEnabled = true
    
    fun enableDebug(enabled: Boolean) {
        isDebugEnabled = enabled
    }
    
    fun d(message: String, tag: String = TAG) {
        if (isDebugEnabled) {
            Log.d(tag, message)
        }
    }
    
    fun i(message: String, tag: String = TAG) {
        if (isDebugEnabled) {
            Log.i(tag, message)
        }
    }
    
    fun w(message: String, tag: String = TAG) {
        if (isDebugEnabled) {
            Log.w(tag, message)
        }
    }
    
    fun e(message: String, throwable: Throwable? = null, tag: String = TAG) {
        if (isDebugEnabled) {
            if (throwable != null) {
                Log.e(tag, message, throwable)
            } else {
                Log.e(tag, message)
            }
        }
    }
}
