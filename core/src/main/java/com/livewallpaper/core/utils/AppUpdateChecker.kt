package com.livewallpaper.core.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import com.livewallpaper.core.data.model.AppVersion
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.net.URL
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用更新检查器
 * 负责检查应用更新和版本管理
 */
@Singleton
class AppUpdateChecker @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val json = Json { ignoreUnknownKeys = true }
    
    /**
     * 获取当前应用版本信息
     */
    fun getCurrentVersion(): AppVersion {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            AppVersion(
                versionName = packageInfo.versionName ?: "1.0.0",
                versionCode = packageInfo.longVersionCode,
                buildTime = System.currentTimeMillis()
            )
        } catch (e: PackageManager.NameNotFoundException) {
            Logger.e("Error getting current version", e)
            AppVersion("1.0.0", 1L, System.currentTimeMillis())
        }
    }
    
    /**
     * 检查应用更新
     */
    suspend fun checkForUpdates(): UpdateCheckResult {
        return withContext(Dispatchers.IO) {
            try {
                val currentVersion = getCurrentVersion()
                val latestVersion = fetchLatestVersion()
                
                when {
                    latestVersion == null -> UpdateCheckResult.Error("无法获取最新版本信息")
                    latestVersion.versionCode > currentVersion.versionCode -> {
                        UpdateCheckResult.UpdateAvailable(currentVersion, latestVersion)
                    }
                    else -> UpdateCheckResult.NoUpdate(currentVersion)
                }
            } catch (e: Exception) {
                Logger.e("Error checking for updates", e)
                UpdateCheckResult.Error("检查更新失败: ${e.message}")
            }
        }
    }
    
    /**
     * 获取最新版本信息
     */
    private suspend fun fetchLatestVersion(): AppVersion? {
        return try {
            // 这里应该从服务器获取最新版本信息
            // 为了演示，我们返回一个模拟的版本信息
            val versionUrl = "https://api.livewallpaper.com/version"
            
            // 实际项目中应该使用HTTP客户端获取
            // val response = httpClient.get(versionUrl)
            // val versionInfo = json.decodeFromString<VersionResponse>(response.body)
            
            // 模拟版本信息
            AppVersion(
                versionName = "1.1.0",
                versionCode = 2L,
                buildTime = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.e("Error fetching latest version", e)
            null
        }
    }
    
    /**
     * 打开应用商店页面
     */
    fun openAppStore() {
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("market://details?id=${context.packageName}")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果没有应用商店，打开浏览器
            try {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse("https://play.google.com/store/apps/details?id=${context.packageName}")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                Logger.e("Error opening app store", e2)
            }
        }
    }
    
    /**
     * 获取应用安装来源
     */
    fun getInstallSource(): String {
        return try {
            val packageManager = context.packageManager
            val installerPackageName = packageManager.getInstallerPackageName(context.packageName)
            
            when (installerPackageName) {
                "com.android.vending" -> "Google Play Store"
                "com.amazon.venezia" -> "Amazon Appstore"
                "com.huawei.appmarket" -> "华为应用市场"
                "com.xiaomi.mipicks" -> "小米应用商店"
                "com.oppo.market" -> "OPPO软件商店"
                "com.vivo.appstore" -> "vivo应用商店"
                "com.sec.android.app.samsungapps" -> "三星应用商店"
                "com.tencent.android.qqdownloader" -> "应用宝"
                "com.baidu.appsearch" -> "百度手机助手"
                "com.qihoo.appstore" -> "360手机助手"
                null -> "未知来源"
                else -> installerPackageName ?: "未知来源"
            }
        } catch (e: Exception) {
            Logger.e("Error getting install source", e)
            "未知来源"
        }
    }
    
    /**
     * 检查是否为调试版本
     */
    fun isDebugBuild(): Boolean {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            (packageInfo.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            Logger.e("Error checking debug build", e)
            false
        }
    }
    
    /**
     * 获取应用详细信息
     */
    fun getAppInfo(): AppInfo {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            val applicationInfo = packageInfo.applicationInfo
            
            AppInfo(
                packageName = context.packageName,
                versionName = packageInfo.versionName ?: "1.0.0",
                versionCode = packageInfo.longVersionCode,
                targetSdkVersion = applicationInfo.targetSdkVersion,
                minSdkVersion = applicationInfo.minSdkVersion,
                installTime = packageInfo.firstInstallTime,
                updateTime = packageInfo.lastUpdateTime,
                installSource = getInstallSource(),
                isDebugBuild = isDebugBuild(),
                dataDir = applicationInfo.dataDir,
                apkPath = applicationInfo.sourceDir
            )
        } catch (e: Exception) {
            Logger.e("Error getting app info", e)
            AppInfo(
                packageName = context.packageName,
                versionName = "1.0.0",
                versionCode = 1L,
                targetSdkVersion = 34,
                minSdkVersion = 24,
                installTime = System.currentTimeMillis(),
                updateTime = System.currentTimeMillis(),
                installSource = "未知来源",
                isDebugBuild = false,
                dataDir = "",
                apkPath = ""
            )
        }
    }
    
    /**
     * 比较版本号
     */
    fun compareVersions(version1: String, version2: String): Int {
        val parts1 = version1.split(".").map { it.toIntOrNull() ?: 0 }
        val parts2 = version2.split(".").map { it.toIntOrNull() ?: 0 }
        
        val maxLength = maxOf(parts1.size, parts2.size)
        
        for (i in 0 until maxLength) {
            val part1 = parts1.getOrNull(i) ?: 0
            val part2 = parts2.getOrNull(i) ?: 0
            
            when {
                part1 < part2 -> return -1
                part1 > part2 -> return 1
            }
        }
        
        return 0
    }
}

/**
 * 更新检查结果
 */
sealed class UpdateCheckResult {
    data class UpdateAvailable(
        val currentVersion: AppVersion,
        val latestVersion: AppVersion
    ) : UpdateCheckResult()
    
    data class NoUpdate(val currentVersion: AppVersion) : UpdateCheckResult()
    data class Error(val message: String) : UpdateCheckResult()
}

/**
 * 应用信息数据类
 */
data class AppInfo(
    val packageName: String,
    val versionName: String,
    val versionCode: Long,
    val targetSdkVersion: Int,
    val minSdkVersion: Int,
    val installTime: Long,
    val updateTime: Long,
    val installSource: String,
    val isDebugBuild: Boolean,
    val dataDir: String,
    val apkPath: String
)

/**
 * 版本响应数据类
 */
@Serializable
data class VersionResponse(
    val versionName: String,
    val versionCode: Long,
    val releaseNotes: String,
    val downloadUrl: String,
    val isForceUpdate: Boolean,
    val minSupportedVersion: Long
)
