package com.livewallpaper.core.utils

import android.graphics.*
import com.livewallpaper.core.data.model.SceneCategory
import com.livewallpaper.core.data.model.TimeOfDay
import kotlin.random.Random

/**
 * 占位图片生成器
 * 用于在没有真实图片资源时生成测试用的占位图片
 */
object PlaceholderImageGenerator {
    
    /**
     * 生成场景背景图片
     */
    fun generateSceneBackground(
        width: Int,
        height: Int,
        category: SceneCategory,
        timeOfDay: TimeOfDay?
    ): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 根据分类和时间生成不同的背景
        val colors = getColorsForScene(category, timeOfDay)
        val gradient = LinearGradient(
            0f, 0f,
            0f, height.toFloat(),
            colors,
            null,
            Shader.TileMode.CLAMP
        )
        
        val paint = Paint().apply {
            shader = gradient
            isAntiAlias = true
        }
        
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)
        
        // 添加一些装饰元素
        addSceneElements(canvas, category, timeOfDay, width, height)
        
        return bitmap
    }
    
    /**
     * 生成中景图层
     */
    fun generateMiddleground(
        width: Int,
        height: Int,
        category: SceneCategory,
        timeOfDay: TimeOfDay?
    ): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        val paint = Paint().apply {
            isAntiAlias = true
            alpha = 180
        }
        
        when (category) {
            SceneCategory.FOREST -> {
                // 绘制树木轮廓
                paint.color = Color.argb(120, 34, 139, 34)
                for (i in 0 until 5) {
                    val x = width * (i + 1) / 6f
                    val treeHeight = height * (0.3f + Random.nextFloat() * 0.4f)
                    canvas.drawRect(
                        x - 20f, height - treeHeight,
                        x + 20f, height.toFloat(),
                        paint
                    )
                }
            }
            SceneCategory.CITY -> {
                // 绘制建筑轮廓
                paint.color = Color.argb(150, 70, 70, 70)
                for (i in 0 until 8) {
                    val x = width * i / 8f
                    val buildingHeight = height * (0.2f + Random.nextFloat() * 0.6f)
                    val buildingWidth = width / 10f
                    canvas.drawRect(
                        x, height - buildingHeight,
                        x + buildingWidth, height.toFloat(),
                        paint
                    )
                }
            }
            SceneCategory.MOUNTAIN -> {
                // 绘制山峰轮廓
                paint.color = Color.argb(100, 105, 105, 105)
                val path = Path()
                path.moveTo(0f, height.toFloat())
                for (i in 0..width step 50) {
                    val y = height * (0.3f + Random.nextFloat() * 0.4f)
                    path.lineTo(i.toFloat(), height - y)
                }
                path.lineTo(width.toFloat(), height.toFloat())
                path.close()
                canvas.drawPath(path, paint)
            }
            else -> {
                // 其他类型的简单装饰
                paint.color = Color.argb(80, 255, 255, 255)
                for (i in 0 until 3) {
                    val x = width * (i + 1) / 4f
                    val y = height * (0.3f + Random.nextFloat() * 0.4f)
                    canvas.drawCircle(x, y, 30f + Random.nextFloat() * 20f, paint)
                }
            }
        }
        
        return bitmap
    }
    
    /**
     * 生成前景图层
     */
    fun generateForeground(
        width: Int,
        height: Int,
        category: SceneCategory,
        timeOfDay: TimeOfDay?
    ): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        val paint = Paint().apply {
            isAntiAlias = true
            alpha = 200
        }
        
        when (category) {
            SceneCategory.FOREST -> {
                // 绘制前景树叶
                paint.color = Color.argb(150, 0, 100, 0)
                for (i in 0 until 10) {
                    val x = Random.nextFloat() * width
                    val y = Random.nextFloat() * height * 0.3f
                    canvas.drawCircle(x, y, 5f + Random.nextFloat() * 10f, paint)
                }
            }
            SceneCategory.OCEAN -> {
                // 绘制波浪
                paint.color = Color.argb(100, 0, 191, 255)
                val path = Path()
                path.moveTo(0f, height * 0.8f)
                for (i in 0..width step 20) {
                    val y = height * 0.8f + kotlin.math.sin(i * 0.02) * 10f
                    path.lineTo(i.toFloat(), y.toFloat())
                }
                path.lineTo(width.toFloat(), height.toFloat())
                path.lineTo(0f, height.toFloat())
                path.close()
                canvas.drawPath(path, paint)
            }
            else -> {
                // 简单的前景元素
                paint.color = Color.argb(60, 255, 255, 255)
                canvas.drawRect(
                    0f, height * 0.9f,
                    width.toFloat(), height.toFloat(),
                    paint
                )
            }
        }
        
        return bitmap
    }
    
    /**
     * 根据场景类型和时间获取颜色
     */
    private fun getColorsForScene(category: SceneCategory, timeOfDay: TimeOfDay?): IntArray {
        return when (category) {
            SceneCategory.FOREST -> when (timeOfDay) {
                TimeOfDay.DAWN -> intArrayOf(
                    Color.rgb(255, 200, 150),
                    Color.rgb(100, 150, 100)
                )
                TimeOfDay.NIGHT -> intArrayOf(
                    Color.rgb(20, 20, 40),
                    Color.rgb(10, 50, 10)
                )
                else -> intArrayOf(
                    Color.rgb(135, 206, 250),
                    Color.rgb(34, 139, 34)
                )
            }
            SceneCategory.OCEAN -> when (timeOfDay) {
                TimeOfDay.DAWN -> intArrayOf(
                    Color.rgb(255, 180, 120),
                    Color.rgb(0, 119, 190)
                )
                TimeOfDay.DUSK -> intArrayOf(
                    Color.rgb(255, 120, 80),
                    Color.rgb(0, 100, 150)
                )
                else -> intArrayOf(
                    Color.rgb(135, 206, 250),
                    Color.rgb(0, 191, 255)
                )
            }
            SceneCategory.CITY -> when (timeOfDay) {
                TimeOfDay.NIGHT -> intArrayOf(
                    Color.rgb(25, 25, 112),
                    Color.rgb(70, 70, 70)
                )
                else -> intArrayOf(
                    Color.rgb(176, 196, 222),
                    Color.rgb(119, 136, 153)
                )
            }
            SceneCategory.SPACE -> intArrayOf(
                Color.rgb(25, 25, 112),
                Color.rgb(0, 0, 0)
            )
            SceneCategory.ABSTRACT -> intArrayOf(
                Color.rgb(Random.nextInt(100, 255), Random.nextInt(100, 255), Random.nextInt(100, 255)),
                Color.rgb(Random.nextInt(50, 200), Random.nextInt(50, 200), Random.nextInt(50, 200))
            )
            else -> intArrayOf(
                Color.rgb(135, 206, 250),
                Color.rgb(255, 255, 255)
            )
        }
    }
    
    /**
     * 添加场景装饰元素
     */
    private fun addSceneElements(
        canvas: Canvas,
        category: SceneCategory,
        timeOfDay: TimeOfDay?,
        width: Int,
        height: Int
    ) {
        val paint = Paint().apply {
            isAntiAlias = true
        }
        
        when (category) {
            SceneCategory.SPACE -> {
                // 添加星星
                paint.color = Color.WHITE
                for (i in 0 until 50) {
                    val x = Random.nextFloat() * width
                    val y = Random.nextFloat() * height
                    canvas.drawCircle(x, y, 1f + Random.nextFloat() * 2f, paint)
                }
            }
            SceneCategory.ABSTRACT -> {
                // 添加抽象图形
                paint.color = Color.argb(100, Random.nextInt(255), Random.nextInt(255), Random.nextInt(255))
                for (i in 0 until 5) {
                    val x = Random.nextFloat() * width
                    val y = Random.nextFloat() * height
                    val radius = 20f + Random.nextFloat() * 50f
                    canvas.drawCircle(x, y, radius, paint)
                }
            }
            else -> {
                // 根据时间添加太阳或月亮
                if (timeOfDay == TimeOfDay.NIGHT) {
                    paint.color = Color.argb(200, 255, 255, 224)
                    canvas.drawCircle(width * 0.8f, height * 0.2f, 30f, paint)
                } else if (timeOfDay != null) {
                    paint.color = Color.argb(180, 255, 255, 0)
                    canvas.drawCircle(width * 0.7f, height * 0.3f, 40f, paint)
                }
            }
        }
    }
}
