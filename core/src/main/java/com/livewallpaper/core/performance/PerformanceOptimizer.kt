package com.livewallpaper.core.performance

import android.content.Context
import android.os.BatteryManager
import com.livewallpaper.core.data.model.RenderQuality
import com.livewallpaper.core.data.model.WallpaperSettings
import com.livewallpaper.core.data.repository.SettingsRepository
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 性能优化器
 * 根据设备状态和性能指标自动调整渲染参数
 */
@Singleton
class PerformanceOptimizer @Inject constructor(
    @ApplicationContext private val context: Context,
    private val performanceMonitor: PerformanceMonitor,
    private val settingsRepository: SettingsRepository
) {
    
    private var lastOptimizationTime = 0L
    private val optimizationInterval = 5000L // 5秒检查一次
    
    /**
     * 获取优化后的渲染设置
     */
    suspend fun getOptimizedSettings(): OptimizedRenderSettings {
        val currentSettings = settingsRepository.getCurrentSettings()
        val performanceMetrics = performanceMonitor.performanceMetrics.first()
        val batteryLevel = getBatteryLevel()
        val isCharging = isCharging()
        val thermalState = getThermalState()
        
        return OptimizedRenderSettings(
            frameRate = calculateOptimalFrameRate(currentSettings, performanceMetrics, batteryLevel, isCharging),
            quality = calculateOptimalQuality(currentSettings, performanceMetrics, batteryLevel, thermalState),
            enableParallax = shouldEnableParallax(currentSettings, performanceMetrics, batteryLevel),
            enableWeatherEffects = shouldEnableWeatherEffects(currentSettings, performanceMetrics, batteryLevel),
            enableMusicVisualization = shouldEnableMusicVisualization(currentSettings, performanceMetrics, batteryLevel),
            maxParticles = calculateMaxParticles(currentSettings, performanceMetrics),
            enableObjectPooling = true, // 始终启用对象池
            enableDirtyRegions = shouldEnableDirtyRegions(performanceMetrics),
            gcTriggerThreshold = calculateGCThreshold(performanceMetrics)
        )
    }
    
    /**
     * 计算最优帧率
     */
    private fun calculateOptimalFrameRate(
        settings: WallpaperSettings,
        metrics: PerformanceMetrics,
        batteryLevel: Int,
        isCharging: Boolean
    ): Int {
        var targetFrameRate = settings.frameRate
        
        // 电池优化
        if (settings.enableBatteryOptimization && !isCharging) {
            when {
                batteryLevel < 15 -> targetFrameRate = minOf(targetFrameRate, 15)
                batteryLevel < 30 -> targetFrameRate = minOf(targetFrameRate, 20)
                batteryLevel < 50 -> targetFrameRate = minOf(targetFrameRate, 24)
            }
        }
        
        // 低功耗模式
        if (settings.enableLowPowerMode) {
            targetFrameRate = minOf(targetFrameRate, 15)
        }
        
        // 性能自适应
        when {
            metrics.currentFps < 15 && metrics.memoryUsagePercent > 80 -> {
                targetFrameRate = minOf(targetFrameRate, 15)
            }
            metrics.currentFps < 20 && metrics.memoryUsagePercent > 70 -> {
                targetFrameRate = minOf(targetFrameRate, 20)
            }
            metrics.droppedFrames > 10 -> {
                targetFrameRate = minOf(targetFrameRate, targetFrameRate - 5)
            }
        }
        
        return maxOf(10, targetFrameRate) // 最低10fps
    }
    
    /**
     * 计算最优渲染质量
     */
    private fun calculateOptimalQuality(
        settings: WallpaperSettings,
        metrics: PerformanceMetrics,
        batteryLevel: Int,
        thermalState: Int
    ): RenderQuality {
        var targetQuality = settings.quality
        
        // 内存压力调整
        when {
            metrics.memoryUsagePercent > 85 -> targetQuality = RenderQuality.LOW
            metrics.memoryUsagePercent > 75 -> targetQuality = minOf(targetQuality, RenderQuality.MEDIUM)
            metrics.memoryUsagePercent > 65 -> targetQuality = minOf(targetQuality, RenderQuality.HIGH)
        }
        
        // 电池电量调整
        if (settings.enableBatteryOptimization && batteryLevel < 20) {
            targetQuality = minOf(targetQuality, RenderQuality.MEDIUM)
        }
        
        // 热节流调整
        if (thermalState >= 3) { // THERMAL_STATUS_MODERATE 或更高
            targetQuality = minOf(targetQuality, RenderQuality.MEDIUM)
        }
        
        // 低功耗模式
        if (settings.enableLowPowerMode) {
            targetQuality = RenderQuality.LOW
        }
        
        return targetQuality
    }
    
    /**
     * 判断是否启用视差效果
     */
    private fun shouldEnableParallax(
        settings: WallpaperSettings,
        metrics: PerformanceMetrics,
        batteryLevel: Int
    ): Boolean {
        if (!settings.enableParallaxEffect) return false
        
        return when {
            settings.enableLowPowerMode -> false
            metrics.memoryUsagePercent > 80 -> false
            metrics.currentFps < 20 -> false
            settings.enableBatteryOptimization && batteryLevel < 20 -> false
            else -> true
        }
    }
    
    /**
     * 判断是否启用天气效果
     */
    private fun shouldEnableWeatherEffects(
        settings: WallpaperSettings,
        metrics: PerformanceMetrics,
        batteryLevel: Int
    ): Boolean {
        if (!settings.enableWeatherEffects) return false
        
        return when {
            settings.enableLowPowerMode -> false
            metrics.memoryUsagePercent > 75 -> false
            metrics.currentFps < 15 -> false
            settings.enableBatteryOptimization && batteryLevel < 15 -> false
            else -> true
        }
    }
    
    /**
     * 判断是否启用音乐可视化
     */
    private fun shouldEnableMusicVisualization(
        settings: WallpaperSettings,
        metrics: PerformanceMetrics,
        batteryLevel: Int
    ): Boolean {
        if (!settings.enableMusicVisualization) return false
        
        return when {
            settings.enableLowPowerMode -> false
            metrics.memoryUsagePercent > 70 -> false
            metrics.currentFps < 20 -> false
            settings.enableBatteryOptimization && batteryLevel < 25 -> false
            else -> true
        }
    }
    
    /**
     * 计算最大粒子数量
     */
    private fun calculateMaxParticles(
        settings: WallpaperSettings,
        metrics: PerformanceMetrics
    ): Int {
        val baseParticles = when (settings.quality) {
            RenderQuality.LOW -> 50
            RenderQuality.MEDIUM -> 100
            RenderQuality.HIGH -> 200
            RenderQuality.ULTRA -> 300
        }
        
        return when {
            metrics.memoryUsagePercent > 80 -> baseParticles / 4
            metrics.memoryUsagePercent > 70 -> baseParticles / 2
            metrics.memoryUsagePercent > 60 -> (baseParticles * 0.75).toInt()
            else -> baseParticles
        }
    }
    
    /**
     * 判断是否启用脏区域重绘
     */
    private fun shouldEnableDirtyRegions(metrics: PerformanceMetrics): Boolean {
        // 在高内存使用或低帧率时启用脏区域重绘
        return metrics.memoryUsagePercent > 60 || metrics.currentFps < 25
    }
    
    /**
     * 计算GC触发阈值
     */
    private fun calculateGCThreshold(metrics: PerformanceMetrics): Int {
        return when {
            metrics.memoryUsagePercent > 80 -> 85 // 更积极的GC
            metrics.memoryUsagePercent > 70 -> 90
            else -> 95 // 较少的GC
        }
    }
    
    /**
     * 获取电池电量
     */
    private fun getBatteryLevel(): Int {
        return try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
        } catch (e: Exception) {
            Logger.e("Error getting battery level", e)
            100 // 默认满电
        }
    }
    
    /**
     * 检查是否正在充电
     */
    private fun isCharging(): Boolean {
        return try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            batteryManager.isCharging
        } catch (e: Exception) {
            Logger.e("Error checking charging status", e)
            false
        }
    }
    
    /**
     * 获取热节流状态
     */
    private fun getThermalState(): Int {
        return try {
            // Android Q+ 才有PowerManager.getCurrentThermalStatus()
            // 这里简化处理，实际项目中可以使用反射或其他方法
            0 // THERMAL_STATUS_NONE
        } catch (e: Exception) {
            Logger.e("Error getting thermal state", e)
            0
        }
    }
    
    /**
     * 检查是否需要优化
     */
    fun shouldOptimize(): Boolean {
        val currentTime = System.currentTimeMillis()
        return currentTime - lastOptimizationTime > optimizationInterval
    }
    
    /**
     * 标记已进行优化
     */
    fun markOptimized() {
        lastOptimizationTime = System.currentTimeMillis()
    }
    
    /**
     * 获取性能建议
     */
    suspend fun getPerformanceRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val metrics = performanceMonitor.performanceMetrics.first()
        val batteryLevel = getBatteryLevel()
        val isCharging = isCharging()
        
        if (metrics.memoryUsagePercent > 80) {
            recommendations.add("内存使用率过高，建议降低渲染质量或启用低功耗模式")
        }
        
        if (metrics.currentFps < 20) {
            recommendations.add("帧率较低，建议降低帧率设置或关闭部分视觉效果")
        }
        
        if (batteryLevel < 20 && !isCharging) {
            recommendations.add("电池电量较低，建议启用电池优化模式")
        }
        
        if (metrics.droppedFrames > 15) {
            recommendations.add("掉帧较多，建议关闭视差效果或降低粒子数量")
        }
        
        if (performanceMonitor.isLowMemory()) {
            recommendations.add("系统内存不足，建议重启应用或清理后台应用")
        }
        
        return recommendations
    }
}

/**
 * 优化后的渲染设置
 */
data class OptimizedRenderSettings(
    val frameRate: Int,
    val quality: RenderQuality,
    val enableParallax: Boolean,
    val enableWeatherEffects: Boolean,
    val enableMusicVisualization: Boolean,
    val maxParticles: Int,
    val enableObjectPooling: Boolean,
    val enableDirtyRegions: Boolean,
    val gcTriggerThreshold: Int
)
