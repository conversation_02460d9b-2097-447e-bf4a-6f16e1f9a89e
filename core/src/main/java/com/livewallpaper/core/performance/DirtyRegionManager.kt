package com.livewallpaper.core.performance

import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.RectF
import com.livewallpaper.core.utils.Logger
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max
import kotlin.math.min

/**
 * 脏区域管理器
 * 负责管理需要重绘的区域，实现局部重绘优化
 */
@Singleton
class DirtyRegionManager @Inject constructor(
    private val objectPoolManager: ObjectPoolManager
) {
    
    private val dirtyRegions = mutableListOf<Rect>()
    private val mergedRegions = mutableListOf<Rect>()
    private var canvasWidth = 0
    private var canvasHeight = 0
    private var isFullRedrawRequired = true
    
    /**
     * 设置画布尺寸
     */
    fun setCanvasSize(width: Int, height: Int) {
        if (canvasWidth != width || canvasHeight != height) {
            canvasWidth = width
            canvasHeight = height
            markFullRedraw()
        }
    }
    
    /**
     * 标记需要全屏重绘
     */
    fun markFullRedraw() {
        isFullRedrawRequired = true
        dirtyRegions.clear()
        Logger.d("Full redraw marked")
    }
    
    /**
     * 添加脏区域
     */
    fun addDirtyRegion(left: Int, top: Int, right: Int, bottom: Int) {
        if (isFullRedrawRequired) return
        
        objectPoolManager.useRect { rect ->
            rect.set(
                max(0, left),
                max(0, top),
                min(canvasWidth, right),
                min(canvasHeight, bottom)
            )
            
            if (!rect.isEmpty) {
                dirtyRegions.add(Rect(rect))
            }
        }
    }
    
    /**
     * 添加脏区域（RectF版本）
     */
    fun addDirtyRegion(rectF: RectF) {
        addDirtyRegion(
            rectF.left.toInt(),
            rectF.top.toInt(),
            rectF.right.toInt(),
            rectF.bottom.toInt()
        )
    }
    
    /**
     * 添加脏区域（Rect版本）
     */
    fun addDirtyRegion(rect: Rect) {
        addDirtyRegion(rect.left, rect.top, rect.right, rect.bottom)
    }
    
    /**
     * 添加圆形脏区域
     */
    fun addDirtyCircle(centerX: Float, centerY: Float, radius: Float) {
        val margin = 2 // 添加一点边距确保完全覆盖
        addDirtyRegion(
            (centerX - radius - margin).toInt(),
            (centerY - radius - margin).toInt(),
            (centerX + radius + margin).toInt(),
            (centerY + radius + margin).toInt()
        )
    }
    
    /**
     * 合并重叠的脏区域
     */
    private fun mergeOverlappingRegions() {
        if (dirtyRegions.isEmpty()) return
        
        mergedRegions.clear()
        
        // 简单的合并算法：如果两个区域重叠或相邻，则合并
        val sortedRegions = dirtyRegions.sortedBy { it.left }
        
        for (region in sortedRegions) {
            var merged = false
            
            for (i in mergedRegions.indices) {
                val existingRegion = mergedRegions[i]
                
                // 检查是否重叠或相邻
                if (isOverlappingOrAdjacent(region, existingRegion)) {
                    // 合并区域
                    existingRegion.union(region)
                    merged = true
                    break
                }
            }
            
            if (!merged) {
                mergedRegions.add(Rect(region))
            }
        }
        
        // 限制合并后的区域数量，避免过多的小区域
        if (mergedRegions.size > 10) {
            // 如果区域太多，直接全屏重绘
            markFullRedraw()
        }
    }
    
    /**
     * 检查两个区域是否重叠或相邻
     */
    private fun isOverlappingOrAdjacent(rect1: Rect, rect2: Rect): Boolean {
        // 扩展区域边界来检查相邻性
        val expandedRect1 = Rect(rect1.left - 1, rect1.top - 1, rect1.right + 1, rect1.bottom + 1)
        return Rect.intersects(expandedRect1, rect2)
    }
    
    /**
     * 获取需要重绘的区域列表
     */
    fun getDirtyRegions(): List<Rect> {
        if (isFullRedrawRequired) {
            return listOf(Rect(0, 0, canvasWidth, canvasHeight))
        }
        
        if (dirtyRegions.isEmpty()) {
            return emptyList()
        }
        
        mergeOverlappingRegions()
        return mergedRegions.toList()
    }
    
    /**
     * 检查是否需要全屏重绘
     */
    fun isFullRedrawRequired(): Boolean = isFullRedrawRequired
    
    /**
     * 清理脏区域（在重绘完成后调用）
     */
    fun clearDirtyRegions() {
        dirtyRegions.clear()
        mergedRegions.clear()
        isFullRedrawRequired = false
    }
    
    /**
     * 检查点是否在脏区域内
     */
    fun isPointInDirtyRegion(x: Int, y: Int): Boolean {
        if (isFullRedrawRequired) return true
        
        return dirtyRegions.any { it.contains(x, y) }
    }
    
    /**
     * 检查矩形是否与脏区域相交
     */
    fun isRectIntersectingDirtyRegion(rect: Rect): Boolean {
        if (isFullRedrawRequired) return true
        
        return dirtyRegions.any { Rect.intersects(it, rect) }
    }
    
    /**
     * 获取脏区域统计信息
     */
    fun getDirtyRegionStats(): DirtyRegionStats {
        val totalArea = if (isFullRedrawRequired) {
            canvasWidth * canvasHeight
        } else {
            dirtyRegions.sumOf { it.width() * it.height() }
        }
        
        val canvasArea = canvasWidth * canvasHeight
        val dirtyPercent = if (canvasArea > 0) {
            (totalArea.toDouble() / canvasArea.toDouble() * 100).toInt()
        } else {
            0
        }
        
        return DirtyRegionStats(
            regionCount = if (isFullRedrawRequired) 1 else dirtyRegions.size,
            totalDirtyArea = totalArea,
            dirtyPercent = dirtyPercent,
            isFullRedraw = isFullRedrawRequired
        )
    }
    
    /**
     * 优化脏区域
     * 如果脏区域覆盖面积超过阈值，则改为全屏重绘
     */
    fun optimizeDirtyRegions(threshold: Float = 0.7f) {
        if (isFullRedrawRequired) return
        
        val stats = getDirtyRegionStats()
        if (stats.dirtyPercent > threshold * 100) {
            Logger.d("Dirty region coverage ${stats.dirtyPercent}% exceeds threshold, switching to full redraw")
            markFullRedraw()
        }
    }
    
    /**
     * 应用Canvas裁剪
     */
    fun applyClipping(canvas: Canvas): Boolean {
        if (isFullRedrawRequired) {
            return false // 不需要裁剪
        }
        
        val regions = getDirtyRegions()
        if (regions.isEmpty()) {
            return true // 没有需要绘制的区域
        }
        
        // 应用裁剪区域
        canvas.save()
        
        if (regions.size == 1) {
            canvas.clipRect(regions[0])
        } else {
            // 多个区域的情况，创建复合裁剪
            for (region in regions) {
                canvas.clipRect(region)
            }
        }
        
        return false
    }
    
    /**
     * 恢复Canvas状态
     */
    fun restoreCanvas(canvas: Canvas) {
        if (!isFullRedrawRequired && dirtyRegions.isNotEmpty()) {
            canvas.restore()
        }
    }
}

/**
 * 脏区域统计信息
 */
data class DirtyRegionStats(
    val regionCount: Int,
    val totalDirtyArea: Int,
    val dirtyPercent: Int,
    val isFullRedraw: Boolean
)
