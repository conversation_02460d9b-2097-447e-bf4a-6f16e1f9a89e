package com.livewallpaper.core.performance

import android.graphics.*
import com.livewallpaper.core.utils.Logger
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 对象池管理器
 * 负责管理常用对象的复用，减少GC压力
 */
@Singleton
class ObjectPoolManager @Inject constructor() {

    private val paintPool = ObjectPool({ Paint().apply { isAntiAlias = true } })
    private val rectPool = ObjectPool({ Rect() })
    private val rectFPool = ObjectPool({ RectF() })
    private val pathPool = ObjectPool({ Path() })
    private val matrixPool = ObjectPool({ Matrix() })
    private val pointPool = ObjectPool({ Point() })
    private val pointFPool = ObjectPool({ PointF() })

    /**
     * 获取Paint对象
     */
    fun obtainPaint(): Paint = paintPool.obtain()

    /**
     * 回收Paint对象
     */
    fun recyclePaint(paint: Paint) {
        paint.reset()
        paintPool.recycle(paint)
    }

    /**
     * 获取Rect对象
     */
    fun obtainRect(): Rect = rectPool.obtain()

    /**
     * 回收Rect对象
     */
    fun recycleRect(rect: Rect) {
        rect.setEmpty()
        rectPool.recycle(rect)
    }

    /**
     * 获取RectF对象
     */
    fun obtainRectF(): RectF = rectFPool.obtain()

    /**
     * 回收RectF对象
     */
    fun recycleRectF(rectF: RectF) {
        rectF.setEmpty()
        rectFPool.recycle(rectF)
    }

    /**
     * 获取Path对象
     */
    fun obtainPath(): Path = pathPool.obtain()

    /**
     * 回收Path对象
     */
    fun recyclePath(path: Path) {
        path.reset()
        pathPool.recycle(path)
    }

    /**
     * 获取Matrix对象
     */
    fun obtainMatrix(): Matrix = matrixPool.obtain()

    /**
     * 回收Matrix对象
     */
    fun recycleMatrix(matrix: Matrix) {
        matrix.reset()
        matrixPool.recycle(matrix)
    }

    /**
     * 获取Point对象
     */
    fun obtainPoint(): Point = pointPool.obtain()

    /**
     * 回收Point对象
     */
    fun recyclePoint(point: Point) {
        point.set(0, 0)
        pointPool.recycle(point)
    }

    /**
     * 获取PointF对象
     */
    fun obtainPointF(): PointF = pointFPool.obtain()

    /**
     * 回收PointF对象
     */
    fun recyclePointF(pointF: PointF) {
        pointF.set(0f, 0f)
        pointFPool.recycle(pointF)
    }

    /**
     * 清理所有对象池
     */
    fun clearAllPools() {
        paintPool.clear()
        rectPool.clear()
        rectFPool.clear()
        pathPool.clear()
        matrixPool.clear()
        pointPool.clear()
        pointFPool.clear()
        Logger.d("All object pools cleared")
    }

    /**
     * 获取对象池统计信息
     */
    fun getPoolStats(): Map<String, Int> {
        return mapOf(
            "Paint" to paintPool.size(),
            "Rect" to rectPool.size(),
            "RectF" to rectFPool.size(),
            "Path" to pathPool.size(),
            "Matrix" to matrixPool.size(),
            "Point" to pointPool.size(),
            "PointF" to pointFPool.size()
        )
    }
}

/**
 * 通用对象池实现
 */
class ObjectPool<T>(
    private val factory: () -> T,
    private val maxSize: Int = 50
) {
    private val pool = ConcurrentLinkedQueue<T>()
    private var currentSize = 0

    /**
     * 获取对象
     */
    fun obtain(): T {
        return pool.poll() ?: factory()
    }

    /**
     * 回收对象
     */
    fun recycle(obj: T) {
        if (currentSize < maxSize) {
            pool.offer(obj)
            currentSize++
        }
    }

    /**
     * 清空对象池
     */
    fun clear() {
        pool.clear()
        currentSize = 0
    }

    /**
     * 获取当前对象池大小
     */
    fun size(): Int = currentSize
}

/**
 * 对象池扩展函数，提供便捷的使用方式
 */
inline fun <T, R> ObjectPool<T>.use(block: (T) -> R): R {
    val obj = obtain()
    return try {
        block(obj)
    } finally {
        recycle(obj)
    }
}

/**
 * ObjectPoolManager的扩展函数
 */
inline fun <R> ObjectPoolManager.usePaint(block: (Paint) -> R): R {
    val paint = obtainPaint()
    return try {
        block(paint)
    } finally {
        recyclePaint(paint)
    }
}

inline fun <R> ObjectPoolManager.useRect(block: (Rect) -> R): R {
    val rect = obtainRect()
    return try {
        block(rect)
    } finally {
        recycleRect(rect)
    }
}

inline fun <R> ObjectPoolManager.useRectF(block: (RectF) -> R): R {
    val rectF = obtainRectF()
    return try {
        block(rectF)
    } finally {
        recycleRectF(rectF)
    }
}

inline fun <R> ObjectPoolManager.usePath(block: (Path) -> R): R {
    val path = obtainPath()
    return try {
        block(path)
    } finally {
        recyclePath(path)
    }
}

inline fun <R> ObjectPoolManager.useMatrix(block: (Matrix) -> R): R {
    val matrix = obtainMatrix()
    return try {
        block(matrix)
    } finally {
        recycleMatrix(matrix)
    }
}

inline fun <R> ObjectPoolManager.usePoint(block: (Point) -> R): R {
    val point = obtainPoint()
    return try {
        block(point)
    } finally {
        recyclePoint(point)
    }
}

inline fun <R> ObjectPoolManager.usePointF(block: (PointF) -> R): R {
    val pointF = obtainPointF()
    return try {
        block(pointF)
    } finally {
        recyclePointF(pointF)
    }
}
