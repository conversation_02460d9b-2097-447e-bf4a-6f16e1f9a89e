package com.livewallpaper.core.performance

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Process
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt

/**
 * 性能监控器
 * 负责监控应用的性能指标，包括内存、CPU、帧率等
 */
@Singleton
class PerformanceMonitor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    
    private val _performanceMetrics = MutableStateFlow(PerformanceMetrics())
    val performanceMetrics: StateFlow<PerformanceMetrics> = _performanceMetrics.asStateFlow()
    
    private var frameCount = 0
    private var lastFrameTime = System.currentTimeMillis()
    private var frameTimeHistory = mutableListOf<Long>()
    private val maxFrameHistory = 60 // 保留最近60帧的数据
    
    /**
     * 更新帧率统计
     */
    fun recordFrame() {
        frameCount++
        val currentTime = System.currentTimeMillis()
        val frameTime = currentTime - lastFrameTime
        
        frameTimeHistory.add(frameTime)
        if (frameTimeHistory.size > maxFrameHistory) {
            frameTimeHistory.removeAt(0)
        }
        
        lastFrameTime = currentTime
        
        // 每秒更新一次性能指标
        if (frameCount % 30 == 0) {
            updatePerformanceMetrics()
        }
    }
    
    /**
     * 更新性能指标
     */
    private fun updatePerformanceMetrics() {
        try {
            val memoryInfo = getMemoryInfo()
            val cpuUsage = getCpuUsage()
            val fps = calculateFps()
            val frameStats = calculateFrameStats()
            
            _performanceMetrics.value = PerformanceMetrics(
                memoryUsageMB = memoryInfo.usedMemoryMB,
                maxMemoryMB = memoryInfo.maxMemoryMB,
                memoryUsagePercent = memoryInfo.usagePercent,
                cpuUsagePercent = cpuUsage,
                currentFps = fps,
                averageFrameTime = frameStats.averageFrameTime,
                maxFrameTime = frameStats.maxFrameTime,
                droppedFrames = frameStats.droppedFrames,
                timestamp = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.e("Error updating performance metrics", e)
        }
    }
    
    /**
     * 获取内存信息
     */
    private fun getMemoryInfo(): MemoryInfo {
        return try {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            
            MemoryInfo(
                usedMemoryMB = (usedMemory / 1024 / 1024).toInt(),
                maxMemoryMB = (maxMemory / 1024 / 1024).toInt(),
                usagePercent = ((usedMemory.toDouble() / maxMemory.toDouble()) * 100).roundToInt()
            )
        } catch (e: Exception) {
            Logger.e("Error getting memory info", e)
            MemoryInfo(0, 0, 0)
        }
    }
    
    /**
     * 获取CPU使用率（简化版本）
     */
    private fun getCpuUsage(): Int {
        return try {
            // 简化的CPU使用率计算
            // 实际项目中可以使用更精确的方法
            val pid = Process.myPid()
            val debugInfo = Debug.MemoryInfo()
            Debug.getMemoryInfo(debugInfo)
            
            // 基于内存分配速度估算CPU使用率
            val allocRate = debugInfo.dalvikPrivateDirty + debugInfo.nativePrivateDirty
            minOf(allocRate / 100, 100) // 简化计算，实际应该更复杂
        } catch (e: Exception) {
            Logger.e("Error getting CPU usage", e)
            0
        }
    }
    
    /**
     * 计算帧率
     */
    private fun calculateFps(): Int {
        return try {
            if (frameTimeHistory.isEmpty()) return 0
            
            val totalTime = frameTimeHistory.sum()
            val averageFrameTime = totalTime.toDouble() / frameTimeHistory.size
            
            if (averageFrameTime > 0) {
                (1000.0 / averageFrameTime).roundToInt()
            } else {
                0
            }
        } catch (e: Exception) {
            Logger.e("Error calculating FPS", e)
            0
        }
    }
    
    /**
     * 计算帧统计信息
     */
    private fun calculateFrameStats(): FrameStats {
        return try {
            if (frameTimeHistory.isEmpty()) {
                return FrameStats(0f, 0L, 0)
            }
            
            val averageFrameTime = frameTimeHistory.average().toFloat()
            val maxFrameTime = frameTimeHistory.maxOrNull() ?: 0L
            val droppedFrames = frameTimeHistory.count { it > 16 } // 超过16ms的帧被认为是掉帧
            
            FrameStats(averageFrameTime, maxFrameTime, droppedFrames)
        } catch (e: Exception) {
            Logger.e("Error calculating frame stats", e)
            FrameStats(0f, 0L, 0)
        }
    }
    
    /**
     * 获取系统内存信息
     */
    fun getSystemMemoryInfo(): ActivityManager.MemoryInfo {
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        return memoryInfo
    }
    
    /**
     * 检查是否处于低内存状态
     */
    fun isLowMemory(): Boolean {
        val systemMemoryInfo = getSystemMemoryInfo()
        return systemMemoryInfo.lowMemory
    }
    
    /**
     * 获取可用内存百分比
     */
    fun getAvailableMemoryPercent(): Int {
        val systemMemoryInfo = getSystemMemoryInfo()
        val availablePercent = (systemMemoryInfo.availMem.toDouble() / systemMemoryInfo.totalMem.toDouble() * 100).roundToInt()
        return availablePercent
    }
    
    /**
     * 触发垃圾回收
     */
    fun triggerGC() {
        try {
            System.gc()
            Logger.d("Garbage collection triggered")
        } catch (e: Exception) {
            Logger.e("Error triggering GC", e)
        }
    }
    
    /**
     * 重置统计数据
     */
    fun resetStats() {
        frameCount = 0
        frameTimeHistory.clear()
        lastFrameTime = System.currentTimeMillis()
        Logger.d("Performance stats reset")
    }
    
    /**
     * 获取性能等级
     */
    fun getPerformanceLevel(): PerformanceLevel {
        val metrics = _performanceMetrics.value
        
        return when {
            metrics.memoryUsagePercent > 80 || metrics.currentFps < 15 -> PerformanceLevel.LOW
            metrics.memoryUsagePercent > 60 || metrics.currentFps < 24 -> PerformanceLevel.MEDIUM
            metrics.memoryUsagePercent > 40 || metrics.currentFps < 45 -> PerformanceLevel.HIGH
            else -> PerformanceLevel.ULTRA
        }
    }
    
    /**
     * 获取性能建议
     */
    fun getPerformanceRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val metrics = _performanceMetrics.value
        
        if (metrics.memoryUsagePercent > 80) {
            recommendations.add("内存使用率过高，建议降低渲染质量")
        }
        
        if (metrics.currentFps < 20) {
            recommendations.add("帧率过低，建议降低帧率设置或启用低功耗模式")
        }
        
        if (metrics.droppedFrames > 10) {
            recommendations.add("掉帧较多，建议关闭部分视觉效果")
        }
        
        if (isLowMemory()) {
            recommendations.add("系统内存不足，建议启用电池优化模式")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("性能表现良好，可以尝试提高画质设置")
        }
        
        return recommendations
    }
}

/**
 * 性能指标数据类
 */
data class PerformanceMetrics(
    val memoryUsageMB: Int = 0,
    val maxMemoryMB: Int = 0,
    val memoryUsagePercent: Int = 0,
    val cpuUsagePercent: Int = 0,
    val currentFps: Int = 0,
    val averageFrameTime: Float = 0f,
    val maxFrameTime: Long = 0L,
    val droppedFrames: Int = 0,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 内存信息数据类
 */
private data class MemoryInfo(
    val usedMemoryMB: Int,
    val maxMemoryMB: Int,
    val usagePercent: Int
)

/**
 * 帧统计信息数据类
 */
private data class FrameStats(
    val averageFrameTime: Float,
    val maxFrameTime: Long,
    val droppedFrames: Int
)

/**
 * 性能等级枚举
 */
enum class PerformanceLevel(val displayName: String) {
    LOW("低"),
    MEDIUM("中"),
    HIGH("高"),
    ULTRA("超高")
}
