package com.livewallpaper.core.domain.scene

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import com.livewallpaper.core.data.model.Scene
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.PlaceholderImageGenerator
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 场景资源加载器
 * 负责异步加载和管理场景图片资源
 */
@Singleton
class SceneLoader @Inject constructor(
    @ApplicationContext private val context: Context
) {

    // 内存缓存
    private val bitmapCache = mutableMapOf<String, Bitmap>()
    private val maxCacheSize = 50 * 1024 * 1024 // 50MB缓存限制
    private var currentCacheSize = 0L

    /**
     * 加载场景的所有图层
     */
    suspend fun loadScene(scene: Scene, targetWidth: Int, targetHeight: Int): Resource<SceneLayers> {
        return withContext(Dispatchers.IO) {
            try {
                val layers = SceneLayers()

                // 加载背景图层
                scene.backgroundImagePath?.let { path ->
                    val bitmap = loadBitmap(path, targetWidth, targetHeight)
                    if (bitmap != null) {
                        layers.background = bitmap
                    } else {
                        Logger.w("Failed to load background: $path, generating placeholder")
                        // 生成占位图片
                        layers.background = PlaceholderImageGenerator.generateSceneBackground(
                            targetWidth, targetHeight, scene.category, scene.timeOfDay
                        )
                    }
                } ?: run {
                    // 如果没有指定背景路径，生成占位图片
                    layers.background = PlaceholderImageGenerator.generateSceneBackground(
                        targetWidth, targetHeight, scene.category, scene.timeOfDay
                    )
                }

                // 加载中景图层
                scene.middlegroundImagePath?.let { path ->
                    val bitmap = loadBitmap(path, targetWidth, targetHeight)
                    if (bitmap != null) {
                        layers.middleground = bitmap
                    } else {
                        Logger.w("Failed to load middleground: $path, generating placeholder")
                        layers.middleground = PlaceholderImageGenerator.generateMiddleground(
                            targetWidth, targetHeight, scene.category, scene.timeOfDay
                        )
                    }
                } ?: run {
                    // 生成默认中景
                    layers.middleground = PlaceholderImageGenerator.generateMiddleground(
                        targetWidth, targetHeight, scene.category, scene.timeOfDay
                    )
                }

                // 加载前景图层
                scene.foregroundImagePath?.let { path ->
                    val bitmap = loadBitmap(path, targetWidth, targetHeight)
                    if (bitmap != null) {
                        layers.foreground = bitmap
                    } else {
                        Logger.w("Failed to load foreground: $path, generating placeholder")
                        layers.foreground = PlaceholderImageGenerator.generateForeground(
                            targetWidth, targetHeight, scene.category, scene.timeOfDay
                        )
                    }
                } ?: run {
                    // 生成默认前景
                    layers.foreground = PlaceholderImageGenerator.generateForeground(
                        targetWidth, targetHeight, scene.category, scene.timeOfDay
                    )
                }

                if (layers.background != null) {
                    Resource.Success(layers)
                } else {
                    Resource.Error("Failed to load scene background")
                }
            } catch (e: Exception) {
                Logger.e("Error loading scene: ${scene.id}", e)
                Resource.Error("Scene loading failed: ${e.message}")
            }
        }
    }

    /**
     * 加载单个图片资源
     */
    private suspend fun loadBitmap(path: String, targetWidth: Int, targetHeight: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查缓存
                val cacheKey = "${path}_${targetWidth}x${targetHeight}"
                bitmapCache[cacheKey]?.let { cachedBitmap ->
                    if (!cachedBitmap.isRecycled) {
                        Logger.d("Bitmap loaded from cache: $path")
                        return@withContext cachedBitmap
                    } else {
                        bitmapCache.remove(cacheKey)
                    }
                }

                // 从assets或文件系统加载
                val inputStream = getInputStream(path)
                if (inputStream == null) {
                    Logger.w("Cannot find image: $path")
                    return@withContext null
                }

                inputStream.use { stream ->
                    // 首先获取图片尺寸
                    val options = BitmapFactory.Options().apply {
                        inJustDecodeBounds = true
                    }
                    BitmapFactory.decodeStream(stream, null, options)

                    // 计算采样率
                    val sampleSize = calculateInSampleSize(options, targetWidth, targetHeight)

                    // 重新打开流并解码
                    getInputStream(path)?.use { newStream ->
                        val decodeOptions = BitmapFactory.Options().apply {
                            inSampleSize = sampleSize
                            inPreferredConfig = Bitmap.Config.RGB_565 // 节省内存
                        }

                        val bitmap = BitmapFactory.decodeStream(newStream, null, decodeOptions)
                        if (bitmap != null) {
                            // 缓存bitmap
                            cacheBitmap(cacheKey, bitmap)
                            Logger.d("Bitmap loaded and cached: $path (${bitmap.width}x${bitmap.height})")
                            bitmap
                        } else {
                            Logger.w("Failed to decode bitmap: $path")
                            null
                        }
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error loading bitmap: $path", e)
                null
            }
        }
    }

    /**
     * 获取输入流
     */
    private fun getInputStream(path: String): InputStream? {
        return try {
            when {
                path.startsWith("assets://") -> {
                    val assetPath = path.removePrefix("assets://")
                    context.assets.open(assetPath)
                }
                path.startsWith("file://") -> {
                    val filePath = path.removePrefix("file://")
                    context.openFileInput(filePath)
                }
                path.startsWith("/") -> {
                    // 绝对路径
                    java.io.FileInputStream(path)
                }
                else -> {
                    // 默认从assets加载
                    context.assets.open(path)
                }
            }
        } catch (e: IOException) {
            Logger.w("Cannot open input stream for: $path")
            null
        }
    }

    /**
     * 计算采样率
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }

    /**
     * 缓存bitmap
     */
    private fun cacheBitmap(key: String, bitmap: Bitmap) {
        val bitmapSize = bitmap.byteCount.toLong()

        // 检查缓存大小限制
        while (currentCacheSize + bitmapSize > maxCacheSize && bitmapCache.isNotEmpty()) {
            val oldestKey = bitmapCache.keys.first()
            val oldBitmap = bitmapCache.remove(oldestKey)
            if (oldBitmap != null && !oldBitmap.isRecycled) {
                currentCacheSize -= oldBitmap.byteCount
                oldBitmap.recycle()
            }
        }

        bitmapCache[key] = bitmap
        currentCacheSize += bitmapSize
        Logger.d("Bitmap cached: $key (${bitmapSize / 1024}KB), total cache: ${currentCacheSize / 1024}KB")
    }

    /**
     * 清理缓存
     */
    fun clearCache() {
        bitmapCache.values.forEach { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
        bitmapCache.clear()
        currentCacheSize = 0
        Logger.d("Scene cache cleared")
    }

    /**
     * 预加载场景
     */
    suspend fun preloadScene(scene: Scene, targetWidth: Int, targetHeight: Int) {
        Logger.d("Preloading scene: ${scene.name}")
        loadScene(scene, targetWidth, targetHeight)
    }

    /**
     * 创建合成图片
     */
    suspend fun createCompositeImage(layers: SceneLayers, width: Int, height: Int): Bitmap? {
        return withContext(Dispatchers.Default) {
            try {
                val compositeBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                val canvas = Canvas(compositeBitmap)
                val paint = Paint().apply {
                    isAntiAlias = true
                    isFilterBitmap = true
                }

                // 绘制背景
                layers.background?.let { background ->
                    canvas.drawBitmap(background, 0f, 0f, paint)
                }

                // 绘制中景
                layers.middleground?.let { middleground ->
                    canvas.drawBitmap(middleground, 0f, 0f, paint)
                }

                // 绘制前景
                layers.foreground?.let { foreground ->
                    canvas.drawBitmap(foreground, 0f, 0f, paint)
                }

                compositeBitmap
            } catch (e: Exception) {
                Logger.e("Error creating composite image", e)
                null
            }
        }
    }
}

/**
 * 场景图层数据类
 */
data class SceneLayers(
    var background: Bitmap? = null,
    var middleground: Bitmap? = null,
    var foreground: Bitmap? = null
) {
    fun recycle() {
        background?.takeIf { !it.isRecycled }?.recycle()
        middleground?.takeIf { !it.isRecycled }?.recycle()
        foreground?.takeIf { !it.isRecycled }?.recycle()
    }
}
