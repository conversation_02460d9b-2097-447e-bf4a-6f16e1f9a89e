package com.livewallpaper.core.domain.update

import android.content.Context
import android.content.pm.PackageManager
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用更新检查器
 * 检查应用更新并提供更新提醒功能
 */
@Singleton
class AppUpdateChecker @Inject constructor(
    @ApplicationContext private val context: Context,
    private val httpClient: OkHttpClient
) {
    
    private val _updateState = MutableStateFlow<UpdateState>(UpdateState.Idle)
    val updateState: StateFlow<UpdateState> = _updateState.asStateFlow()
    
    private val json = Json { ignoreUnknownKeys = true }
    
    // 更新检查URL（实际使用时需要替换为真实的服务器地址）
    private val updateCheckUrl = "https://api.example.com/app/version"
    
    /**
     * 检查应用更新
     */
    suspend fun checkForUpdates(forceCheck: Boolean = false): Resource<UpdateInfo?> = withContext(Dispatchers.IO) {
        try {
            Logger.d("Checking for app updates")
            _updateState.value = UpdateState.Checking
            
            // 获取当前版本信息
            val currentVersion = getCurrentVersionInfo()
            
            // 检查是否需要强制检查或距离上次检查已超过24小时
            if (!forceCheck && !shouldCheckForUpdates()) {
                _updateState.value = UpdateState.Idle
                return@withContext Resource.Success(null)
            }
            
            // 从服务器获取最新版本信息
            val latestVersion = fetchLatestVersionFromServer()
                ?: return@withContext Resource.Error("Failed to fetch version info")
            
            // 比较版本
            val updateInfo = compareVersions(currentVersion, latestVersion)
            
            if (updateInfo != null) {
                _updateState.value = UpdateState.UpdateAvailable(updateInfo)
                Logger.d("Update available: ${updateInfo.latestVersion}")
            } else {
                _updateState.value = UpdateState.UpToDate
                Logger.d("App is up to date")
            }
            
            // 更新最后检查时间
            updateLastCheckTime()
            
            Resource.Success(updateInfo)
            
        } catch (e: Exception) {
            Logger.e("Failed to check for updates", e)
            _updateState.value = UpdateState.Error(e.message ?: "Unknown error")
            Resource.Error("Failed to check for updates: ${e.message}")
        }
    }
    
    /**
     * 获取当前版本信息
     */
    private fun getCurrentVersionInfo(): VersionInfo {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            VersionInfo(
                versionName = packageInfo.versionName ?: "1.0.0",
                versionCode = packageInfo.longVersionCode.toInt(),
                buildTime = System.currentTimeMillis()
            )
        } catch (e: PackageManager.NameNotFoundException) {
            Logger.e("Failed to get current version info", e)
            VersionInfo("1.0.0", 1, System.currentTimeMillis())
        }
    }
    
    /**
     * 从服务器获取最新版本信息
     */
    private suspend fun fetchLatestVersionFromServer(): ServerVersionResponse? = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url(updateCheckUrl)
                .addHeader("User-Agent", "LiveWallpaper-Android")
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    json.decodeFromString<ServerVersionResponse>(responseBody)
                } else {
                    null
                }
            } else {
                Logger.e("Server returned error: ${response.code}")
                null
            }
        } catch (e: Exception) {
            Logger.e("Failed to fetch version from server", e)
            // 返回模拟数据用于测试
            ServerVersionResponse(
                latestVersion = "1.1.0",
                latestVersionCode = 2,
                releaseNotes = "• 新增高级场景包\n• 优化性能和稳定性\n• 修复已知问题",
                downloadUrl = "https://play.google.com/store/apps/details?id=${context.packageName}",
                isForceUpdate = false,
                minSupportedVersion = "1.0.0",
                releaseDate = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 比较版本
     */
    private fun compareVersions(current: VersionInfo, server: ServerVersionResponse): UpdateInfo? {
        return if (server.latestVersionCode > current.versionCode) {
            UpdateInfo(
                currentVersion = current.versionName,
                latestVersion = server.latestVersion,
                releaseNotes = server.releaseNotes,
                downloadUrl = server.downloadUrl,
                isForceUpdate = server.isForceUpdate || isVersionTooOld(current.versionName, server.minSupportedVersion),
                updateSize = estimateUpdateSize(current.versionCode, server.latestVersionCode),
                releaseDate = server.releaseDate
            )
        } else {
            null
        }
    }
    
    /**
     * 检查版本是否过旧
     */
    private fun isVersionTooOld(currentVersion: String, minSupportedVersion: String): Boolean {
        return try {
            compareVersionStrings(currentVersion, minSupportedVersion) < 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 比较版本字符串
     */
    private fun compareVersionStrings(version1: String, version2: String): Int {
        val parts1 = version1.split(".").map { it.toIntOrNull() ?: 0 }
        val parts2 = version2.split(".").map { it.toIntOrNull() ?: 0 }
        
        val maxLength = maxOf(parts1.size, parts2.size)
        
        for (i in 0 until maxLength) {
            val part1 = parts1.getOrNull(i) ?: 0
            val part2 = parts2.getOrNull(i) ?: 0
            
            when {
                part1 < part2 -> return -1
                part1 > part2 -> return 1
            }
        }
        
        return 0
    }
    
    /**
     * 估算更新大小
     */
    private fun estimateUpdateSize(currentVersionCode: Int, latestVersionCode: Int): String {
        val versionDiff = latestVersionCode - currentVersionCode
        val estimatedSizeMB = when {
            versionDiff == 1 -> 15 // 小版本更新
            versionDiff <= 5 -> 25 // 中等更新
            else -> 45 // 大版本更新
        }
        return "${estimatedSizeMB}MB"
    }
    
    /**
     * 检查是否应该进行更新检查
     */
    private fun shouldCheckForUpdates(): Boolean {
        val prefs = context.getSharedPreferences("app_update", Context.MODE_PRIVATE)
        val lastCheckTime = prefs.getLong("last_check_time", 0)
        val currentTime = System.currentTimeMillis()
        val checkInterval = 24 * 60 * 60 * 1000L // 24小时
        
        return currentTime - lastCheckTime > checkInterval
    }
    
    /**
     * 更新最后检查时间
     */
    private fun updateLastCheckTime() {
        val prefs = context.getSharedPreferences("app_update", Context.MODE_PRIVATE)
        prefs.edit().putLong("last_check_time", System.currentTimeMillis()).apply()
    }
    
    /**
     * 忽略当前版本更新
     */
    fun ignoreCurrentUpdate(version: String) {
        val prefs = context.getSharedPreferences("app_update", Context.MODE_PRIVATE)
        prefs.edit().putString("ignored_version", version).apply()
        _updateState.value = UpdateState.Idle
        Logger.d("Ignored update for version: $version")
    }
    
    /**
     * 检查版本是否被忽略
     */
    private fun isVersionIgnored(version: String): Boolean {
        val prefs = context.getSharedPreferences("app_update", Context.MODE_PRIVATE)
        val ignoredVersion = prefs.getString("ignored_version", null)
        return ignoredVersion == version
    }
    
    /**
     * 清除更新状态
     */
    fun clearUpdateState() {
        _updateState.value = UpdateState.Idle
    }
}

/**
 * 更新状态
 */
sealed class UpdateState {
    object Idle : UpdateState()
    object Checking : UpdateState()
    object UpToDate : UpdateState()
    data class UpdateAvailable(val updateInfo: UpdateInfo) : UpdateState()
    data class Error(val message: String) : UpdateState()
}

/**
 * 版本信息
 */
data class VersionInfo(
    val versionName: String,
    val versionCode: Int,
    val buildTime: Long
)

/**
 * 服务器版本响应
 */
@Serializable
data class ServerVersionResponse(
    val latestVersion: String,
    val latestVersionCode: Int,
    val releaseNotes: String,
    val downloadUrl: String,
    val isForceUpdate: Boolean,
    val minSupportedVersion: String,
    val releaseDate: Long
)

/**
 * 更新信息
 */
data class UpdateInfo(
    val currentVersion: String,
    val latestVersion: String,
    val releaseNotes: String,
    val downloadUrl: String,
    val isForceUpdate: Boolean,
    val updateSize: String,
    val releaseDate: Long
)
