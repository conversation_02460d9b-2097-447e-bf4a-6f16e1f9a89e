package com.livewallpaper.core.domain.location

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Geocoder
import android.os.Looper
import androidx.core.content.ContextCompat
import com.google.android.gms.location.*
import com.google.android.gms.tasks.Tasks.CancellationTokenSource
import com.livewallpaper.core.data.model.Location
import com.livewallpaper.core.data.preferences.WallpaperPreferences
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.tasks.await
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 位置服务管理器
 * 负责获取用户位置信息，处理权限请求和位置缓存
 */
@Singleton
class LocationManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val preferences: WallpaperPreferences
) {

    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(context)
    }

    private val geocoder: Geocoder by lazy {
        Geocoder(context, Locale.getDefault())
    }

    /**
     * 检查位置权限是否已授予
     */
    fun hasLocationPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 获取当前位置
     */
    suspend fun getCurrentLocation(): Resource<Location> {
        return try {
            if (!hasLocationPermission()) {
                return Resource.Error("位置权限未授予")
            }

            val useCurrentLocation = preferences.useCurrentLocation.first()
            if (!useCurrentLocation) {
                return getManualLocation()
            }

            val androidLocation = getCurrentAndroidLocation()
            if (androidLocation != null) {
                val location = convertToLocation(androidLocation)
                cacheLocation(location)
                Resource.Success(location)
            } else {
                Resource.Error("无法获取当前位置")
            }
        } catch (e: Exception) {
            Logger.e("获取当前位置失败", e)
            Resource.Error("获取位置失败: ${e.message}")
        }
    }

    /**
     * 获取Android系统位置
     */
    private suspend fun getCurrentAndroidLocation(): android.location.Location? {
        return try {
            if (!hasLocationPermission()) {
                Logger.w("没有位置权限")
                return null
            }

            // 首先尝试获取最后已知位置
            val lastLocation = fusedLocationClient.lastLocation.await()
            if (lastLocation != null && isLocationRecent(lastLocation)) {
                Logger.d("使用最后已知位置")
                return lastLocation
            }

            // 如果最后已知位置不可用或过旧，请求新的位置
            Logger.d("请求新的位置")
            requestNewLocation()
        } catch (e: Exception) {
            Logger.e("获取Android位置失败", e)
            null
        }
    }

    /**
     * 请求新的位置
     */
    private suspend fun requestNewLocation(): android.location.Location? {
        return suspendCancellableCoroutine { continuation ->
            try {
                val locationRequest = LocationRequest.Builder(
                    Priority.PRIORITY_HIGH_ACCURACY,
                    10000L // 10秒
                ).apply {
                    setMaxUpdates(1)
                    setMaxUpdateDelayMillis(15000L) // 15秒超时
                }.build()

                val locationCallback = object : LocationCallback() {
                    override fun onLocationResult(result: LocationResult) {
                        super.onLocationResult(result)
                        val location = result.lastLocation
                        if (location != null && continuation.isActive) {
                            continuation.resume(location)
                        }
                    }
                }

                if (hasLocationPermission()) {
                    fusedLocationClient.requestLocationUpdates(
                        locationRequest,
                        locationCallback,
                        Looper.getMainLooper()
                    )

                    continuation.invokeOnCancellation {
                        fusedLocationClient.removeLocationUpdates(locationCallback)
                    }
                } else {
                    continuation.resume(null)
                }
            } catch (e: Exception) {
                Logger.e("请求新位置失败", e)
                continuation.resume(null)
            }
        }
    }

    /**
     * 检查位置是否是最近的
     */
    private fun isLocationRecent(location: android.location.Location): Boolean {
        val maxAge = 10 * 60 * 1000L // 10分钟
        return System.currentTimeMillis() - location.time < maxAge
    }

    /**
     * 转换为应用的Location对象
     */
    private suspend fun convertToLocation(androidLocation: android.location.Location): Location {
        val cityInfo = getCityFromCoordinates(
            androidLocation.latitude,
            androidLocation.longitude
        )

        return Location(
            latitude = androidLocation.latitude,
            longitude = androidLocation.longitude,
            city = cityInfo.first,
            country = cityInfo.second,
            timezone = getTimezoneFromCoordinates(androidLocation.latitude, androidLocation.longitude)
        )
    }

    /**
     * 根据坐标获取城市信息
     */
    private suspend fun getCityFromCoordinates(latitude: Double, longitude: Double): Pair<String, String> {
        return try {
            if (Geocoder.isPresent()) {
                val addresses = geocoder.getFromLocation(latitude, longitude, 1)
                if (!addresses.isNullOrEmpty()) {
                    val address = addresses[0]
                    val city = address.locality ?: address.adminArea ?: "未知城市"
                    val country = address.countryName ?: "未知国家"
                    Pair(city, country)
                } else {
                    Pair("未知城市", "未知国家")
                }
            } else {
                Pair("未知城市", "未知国家")
            }
        } catch (e: Exception) {
            Logger.e("获取城市信息失败", e)
            Pair("未知城市", "未知国家")
        }
    }

    /**
     * 根据坐标获取时区
     */
    private fun getTimezoneFromCoordinates(latitude: Double, longitude: Double): String {
        // 简化的时区计算，实际项目中可能需要更精确的时区API
        return TimeZone.getDefault().id
    }

    /**
     * 获取手动设置的位置
     */
    private suspend fun getManualLocation(): Resource<Location> {
        return try {
            // 这里应该从preferences中读取手动设置的位置
            // 暂时返回默认位置（北京）
            val defaultLocation = Location(
                latitude = 39.9042,
                longitude = 116.4074,
                city = "北京",
                country = "中国",
                timezone = "Asia/Shanghai"
            )
            Resource.Success(defaultLocation)
        } catch (e: Exception) {
            Resource.Error("获取手动位置失败: ${e.message}")
        }
    }

    /**
     * 缓存位置信息
     */
    private suspend fun cacheLocation(location: Location) {
        try {
            // 这里可以将位置信息保存到数据库或preferences中
            Logger.d("位置已缓存: ${location.city}, ${location.country}")
        } catch (e: Exception) {
            Logger.e("缓存位置失败", e)
        }
    }

    /**
     * 获取位置信息流
     */
    fun getLocationFlow(): Flow<Resource<Location>> = flow {
        emit(Resource.Loading())

        while (true) {
            val location = getCurrentLocation()
            emit(location)

            // 每30分钟更新一次位置
            kotlinx.coroutines.delay(30 * 60 * 1000L)
        }
    }

    /**
     * 设置手动位置
     */
    suspend fun setManualLocation(latitude: Double, longitude: Double, cityName: String): Resource<Location> {
        return try {
            val location = Location(
                latitude = latitude,
                longitude = longitude,
                city = cityName,
                country = "手动设置",
                timezone = getTimezoneFromCoordinates(latitude, longitude)
            )

            // 保存到preferences
            // preferences.setManualLocation(latitude, longitude, cityName)

            cacheLocation(location)
            Resource.Success(location)
        } catch (e: Exception) {
            Logger.e("设置手动位置失败", e)
            Resource.Error("设置位置失败: ${e.message}")
        }
    }
}
