package com.livewallpaper.core.domain.scene

import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.data.preferences.WallpaperPreferences
import com.livewallpaper.core.data.repository.SceneRepository
import com.livewallpaper.core.domain.time.TimePhase
import com.livewallpaper.core.domain.time.WallpaperTimeManager
import com.livewallpaper.core.domain.weather.WeatherManager
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import kotlinx.coroutines.flow.*
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 场景管理器
 * 负责场景的选择、切换和管理逻辑
 */
@Singleton
class SceneManager @Inject constructor(
    private val sceneRepository: SceneRepository,
    private val sceneLoader: SceneLoader,
    private val timeManager: WallpaperTimeManager,
    private val weatherManager: WeatherManager,
    private val preferences: WallpaperPreferences
) {

    private var currentScene: Scene? = null
    private var currentSceneLayers: SceneLayers? = null
    private var lastSceneUpdate = Instant.DISTANT_PAST

    /**
     * 获取当前场景流
     */
    fun getCurrentSceneFlow(): Flow<SceneState> = flow {
        emit(SceneState.Loading)

        // 监听设置变化
        combine(
            preferences.currentSceneId,
            preferences.autoChangeEnabled,
            preferences.timeBasedChangeEnabled,
            preferences.weatherBasedChangeEnabled
        ) { sceneId, autoChange, timeBasedChange, weatherBasedChange ->
            SceneConfig(sceneId, autoChange, timeBasedChange, weatherBasedChange)
        }.collect { config ->
            try {
                val scene = selectAppropriateScene(config)
                if (scene != null) {
                    emit(SceneState.Success(scene, currentSceneLayers))
                } else {
                    emit(SceneState.Error("No suitable scene found"))
                }
            } catch (e: Exception) {
                Logger.e("Error in scene flow", e)
                emit(SceneState.Error("Scene selection failed: ${e.message}"))
            }
        }
    }

    /**
     * 选择合适的场景
     */
    private suspend fun selectAppropriateScene(config: SceneConfig): Scene? {
        return try {
            when {
                // 如果禁用自动切换，使用指定场景
                !config.autoChangeEnabled -> {
                    getSceneById(config.currentSceneId)
                }
                // 基于时间的场景选择
                config.timeBasedChangeEnabled -> {
                    selectSceneByTime()
                }
                // 基于天气的场景选择
                config.weatherBasedChangeEnabled -> {
                    selectSceneByWeather()
                }
                // 默认场景
                else -> {
                    getSceneById(config.currentSceneId) ?: getDefaultScene()
                }
            }
        } catch (e: Exception) {
            Logger.e("Error selecting scene", e)
            getDefaultScene()
        }
    }

    /**
     * 根据时间选择场景
     */
    private suspend fun selectSceneByTime(): Scene? {
        val timeState = timeManager.getCurrentTimeState()
        val currentPhase = timeManager.getCurrentTimePhase()
        val currentSeason = getCurrentSeason()

        Logger.d("Selecting scene by time: phase=$currentPhase, season=$currentSeason")

        // 根据时间阶段选择场景
        val timeOfDay = when (currentPhase) {
            TimePhase.NIGHT -> TimeOfDay.NIGHT
            TimePhase.ASTRONOMICAL_TWILIGHT,
            TimePhase.NAUTICAL_TWILIGHT,
            TimePhase.CIVIL_TWILIGHT -> {
                val isDaytime = timeManager.isDaytime()
                if (isDaytime) TimeOfDay.DUSK else TimeOfDay.DAWN
            }
            TimePhase.DAY -> {
                val progress = timeManager.getSimpleTimeProgress()
                when {
                    progress < 0.3 -> TimeOfDay.MORNING
                    progress < 0.7 -> TimeOfDay.NOON
                    else -> TimeOfDay.AFTERNOON
                }
            }
            TimePhase.UNKNOWN -> TimeOfDay.NOON
        }

        return findBestMatchingScene(
            timeOfDay = timeOfDay,
            season = currentSeason,
            weatherType = null
        )
    }

    /**
     * 根据天气选择场景
     */
    private suspend fun selectSceneByWeather(): Scene? {
        return try {
            val currentWeatherType = weatherManager.getCurrentWeatherType()
            val currentSeason = getCurrentSeason()
            val currentPhase = timeManager.getCurrentTimePhase()

            Logger.d("Selecting scene by weather: type=$currentWeatherType, season=$currentSeason, phase=$currentPhase")

            // 根据天气类型选择时间段
            val timeOfDay = when (currentPhase) {
                TimePhase.NIGHT -> TimeOfDay.NIGHT
                TimePhase.ASTRONOMICAL_TWILIGHT,
                TimePhase.NAUTICAL_TWILIGHT,
                TimePhase.CIVIL_TWILIGHT -> {
                    val isDaytime = timeManager.isDaytime()
                    if (isDaytime) TimeOfDay.DUSK else TimeOfDay.DAWN
                }
                TimePhase.DAY -> {
                    val progress = timeManager.getSimpleTimeProgress()
                    when {
                        progress < 0.3 -> TimeOfDay.MORNING
                        progress < 0.7 -> TimeOfDay.NOON
                        else -> TimeOfDay.AFTERNOON
                    }
                }
                TimePhase.UNKNOWN -> TimeOfDay.NOON
            }

            findBestMatchingScene(
                timeOfDay = timeOfDay,
                season = currentSeason,
                weatherType = currentWeatherType
            )
        } catch (e: Exception) {
            Logger.e("Weather-based scene selection failed", e)
            selectSceneByTime()
        }
    }

    /**
     * 查找最匹配的场景
     */
    private suspend fun findBestMatchingScene(
        timeOfDay: TimeOfDay? = null,
        season: Season? = null,
        weatherType: WeatherType? = null
    ): Scene? {
        return try {
            val allScenesResult = sceneRepository.getAllScenes().first()

            when (allScenesResult) {
                is Resource.Success -> {
                    val scenes = allScenesResult.data

                    // 优先级匹配
                    var bestMatch = scenes?.find { scene ->
                        scene.timeOfDay == timeOfDay &&
                        scene.season == season &&
                        scene.weatherType == weatherType
                    }

                    // 如果没有完全匹配，降级匹配
                    if (bestMatch == null) {
                        bestMatch = scenes?.find { scene ->
                            scene.timeOfDay == timeOfDay && scene.season == season
                        }
                    }

                    if (bestMatch == null) {
                        bestMatch = scenes?.find { scene ->
                            scene.timeOfDay == timeOfDay
                        }
                    }

                    if (bestMatch == null) {
                        bestMatch = scenes?.find { scene ->
                            scene.season == season
                        }
                    }

                    // 最后降级到任意免费场景
                    if (bestMatch == null) {
                        bestMatch = scenes?.find { !it.isPremium }
                    }

                    bestMatch?.also {
                        Logger.d("Selected scene: ${it.name} (${it.category})")
                    }
                }
                else -> {
                    Logger.w("Failed to get scenes for matching")
                    null
                }
            }
        } catch (e: Exception) {
            Logger.e("Error finding matching scene", e)
            null
        }
    }

    /**
     * 根据ID获取场景
     */
    private suspend fun getSceneById(sceneId: String): Scene? {
        return try {
            val result = sceneRepository.getSceneById(sceneId)
            when (result) {
                is Resource.Success -> result.data
                else -> {
                    Logger.w("Scene not found: $sceneId")
                    null
                }
            }
        } catch (e: Exception) {
            Logger.e("Error getting scene by ID: $sceneId", e)
            null
        }
    }

    /**
     * 获取默认场景
     */
    private suspend fun getDefaultScene(): Scene? {
        return try {
            val result = sceneRepository.getFreeScenes().first()
            when (result) {
                is Resource.Success -> result.data?.firstOrNull()
                else -> null
            }
        } catch (e: Exception) {
            Logger.e("Error getting default scene", e)
            null
        }
    }

    /**
     * 获取当前季节
     */
    private fun getCurrentSeason(): Season {
        val now = Clock.System.now()
        val localDate = now.toLocalDateTime(TimeZone.currentSystemDefault()).date
        val month = localDate.monthNumber

        // 北半球季节（可以后续根据用户设置调整）
        return when (month) {
            12, 1, 2 -> Season.WINTER
            3, 4, 5 -> Season.SPRING
            6, 7, 8 -> Season.SUMMER
            9, 10, 11 -> Season.AUTUMN
            else -> Season.SPRING
        }
    }

    /**
     * 加载场景资源
     */
    suspend fun loadSceneResources(scene: Scene, width: Int, height: Int): Resource<SceneLayers> {
        return try {
            if (currentScene?.id == scene.id && currentSceneLayers != null) {
                Logger.d("Scene already loaded: ${scene.name}")
                return Resource.Success(currentSceneLayers!!)
            }

            Logger.d("Loading scene resources: ${scene.name}")
            val result = sceneLoader.loadScene(scene, width, height)

            when (result) {
                is Resource.Success -> {
                    // 清理旧资源
                    currentSceneLayers?.recycle()

                    // 更新当前场景
                    currentScene = scene
                    currentSceneLayers = result.data
                    lastSceneUpdate = Clock.System.now()

                    Logger.d("Scene loaded successfully: ${scene.name}")
                    result
                }
                else -> {
                    Logger.e("Failed to load scene: ${scene.name}")
                    result
                }
            }
        } catch (e: Exception) {
            Logger.e("Error loading scene resources", e)
            Resource.Error("Failed to load scene: ${e.message}")
        }
    }

    /**
     * 切换到指定场景
     */
    suspend fun switchToScene(sceneId: String): Resource<Scene> {
        return try {
            val scene = getSceneById(sceneId)
            if (scene != null) {
                preferences.setCurrentSceneId(sceneId)
                Logger.d("Switched to scene: ${scene.name}")
                Resource.Success(scene)
            } else {
                Resource.Error("Scene not found: $sceneId")
            }
        } catch (e: Exception) {
            Logger.e("Error switching scene", e)
            Resource.Error("Failed to switch scene: ${e.message}")
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        currentSceneLayers?.recycle()
        currentSceneLayers = null
        currentScene = null
        sceneLoader.clearCache()
        Logger.d("Scene manager cleaned up")
    }
}

/**
 * 场景状态密封类
 */
sealed class SceneState {
    object Loading : SceneState()
    data class Success(val scene: Scene, val layers: SceneLayers?) : SceneState()
    data class Error(val message: String) : SceneState()
}

/**
 * 场景配置数据类
 */
private data class SceneConfig(
    val currentSceneId: String,
    val autoChangeEnabled: Boolean,
    val timeBasedChangeEnabled: Boolean,
    val weatherBasedChangeEnabled: Boolean
)
