package com.livewallpaper.core.domain.scene

import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.data.repository.SceneRepository
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 场景初始化器
 * 负责创建和初始化默认场景数据
 */
@Singleton
class SceneInitializer @Inject constructor(
    private val sceneRepository: SceneRepository
) {
    
    /**
     * 初始化默认场景
     */
    suspend fun initializeDefaultScenes(): Resource<Unit> {
        return try {
            Logger.d("Initializing default scenes...")
            
            val defaultScenes = createDefaultScenes()
            val result = sceneRepository.initializeDefaultScenes()
            
            when (result) {
                is Resource.Success -> {
                    Logger.d("Default scenes initialized successfully")
                    Resource.Success(Unit)
                }
                is Resource.Error -> {
                    Logger.e("Failed to initialize default scenes: ${result.message}")
                    result
                }
                is Resource.Loading -> Resource.Loading()
            }
        } catch (e: Exception) {
            Logger.e("Error initializing default scenes", e)
            Resource.Error("Initialization failed: ${e.message}")
        }
    }
    
    /**
     * 创建默认场景列表
     */
    private fun createDefaultScenes(): List<Scene> {
        return listOf(
            // 自然风景 - 森林系列
            Scene(
                id = "forest_dawn",
                name = "森林晨曦",
                description = "清晨的森林，阳光透过树叶洒下金色光芒",
                backgroundImagePath = "scenes/forest/forest_dawn_bg.jpg",
                middlegroundImagePath = "scenes/forest/forest_dawn_mg.png",
                foregroundImagePath = "scenes/forest/forest_dawn_fg.png",
                category = SceneCategory.FOREST,
                season = Season.SPRING,
                timeOfDay = TimeOfDay.DAWN,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "forest_day",
                name = "森林白天",
                description = "阳光明媚的森林，绿意盎然",
                backgroundImagePath = "scenes/forest/forest_day_bg.jpg",
                middlegroundImagePath = "scenes/forest/forest_day_mg.png",
                foregroundImagePath = "scenes/forest/forest_day_fg.png",
                category = SceneCategory.FOREST,
                season = Season.SUMMER,
                timeOfDay = TimeOfDay.NOON,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "forest_dusk",
                name = "森林黄昏",
                description = "夕阳西下的森林，温暖的橙色光芒",
                backgroundImagePath = "scenes/forest/forest_dusk_bg.jpg",
                middlegroundImagePath = "scenes/forest/forest_dusk_mg.png",
                foregroundImagePath = "scenes/forest/forest_dusk_fg.png",
                category = SceneCategory.FOREST,
                season = Season.AUTUMN,
                timeOfDay = TimeOfDay.DUSK,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "forest_night",
                name = "森林夜晚",
                description = "月光下的神秘森林",
                backgroundImagePath = "scenes/forest/forest_night_bg.jpg",
                middlegroundImagePath = "scenes/forest/forest_night_mg.png",
                foregroundImagePath = "scenes/forest/forest_night_fg.png",
                category = SceneCategory.FOREST,
                season = null,
                timeOfDay = TimeOfDay.NIGHT,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            
            // 海洋系列
            Scene(
                id = "ocean_sunrise",
                name = "海上日出",
                description = "海平面上升起的太阳，波光粼粼",
                backgroundImagePath = "scenes/ocean/ocean_sunrise_bg.jpg",
                middlegroundImagePath = "scenes/ocean/ocean_sunrise_mg.png",
                foregroundImagePath = "scenes/ocean/ocean_sunrise_fg.png",
                category = SceneCategory.OCEAN,
                season = null,
                timeOfDay = TimeOfDay.DAWN,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "ocean_day",
                name = "蔚蓝海洋",
                description = "晴朗天空下的蔚蓝海洋",
                backgroundImagePath = "scenes/ocean/ocean_day_bg.jpg",
                middlegroundImagePath = "scenes/ocean/ocean_day_mg.png",
                foregroundImagePath = "scenes/ocean/ocean_day_fg.png",
                category = SceneCategory.OCEAN,
                season = null,
                timeOfDay = TimeOfDay.NOON,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "ocean_sunset",
                name = "海边日落",
                description = "夕阳西下，海浪轻拍沙滩",
                backgroundImagePath = "scenes/ocean/ocean_sunset_bg.jpg",
                middlegroundImagePath = "scenes/ocean/ocean_sunset_mg.png",
                foregroundImagePath = "scenes/ocean/ocean_sunset_fg.png",
                category = SceneCategory.OCEAN,
                season = null,
                timeOfDay = TimeOfDay.DUSK,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            
            // 山脉系列
            Scene(
                id = "mountain_dawn",
                name = "山峦晨光",
                description = "晨光照耀的雄伟山峦",
                backgroundImagePath = "scenes/mountain/mountain_dawn_bg.jpg",
                middlegroundImagePath = "scenes/mountain/mountain_dawn_mg.png",
                foregroundImagePath = "scenes/mountain/mountain_dawn_fg.png",
                category = SceneCategory.MOUNTAIN,
                season = null,
                timeOfDay = TimeOfDay.DAWN,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "mountain_snow",
                name = "雪山风光",
                description = "白雪皑皑的高山美景",
                backgroundImagePath = "scenes/mountain/mountain_snow_bg.jpg",
                middlegroundImagePath = "scenes/mountain/mountain_snow_mg.png",
                foregroundImagePath = "scenes/mountain/mountain_snow_fg.png",
                category = SceneCategory.MOUNTAIN,
                season = Season.WINTER,
                timeOfDay = TimeOfDay.NOON,
                weatherType = WeatherType.SNOWY,
                isPremium = false
            ),
            
            // 城市系列
            Scene(
                id = "city_dawn",
                name = "都市晨曦",
                description = "清晨的城市天际线",
                backgroundImagePath = "scenes/city/city_dawn_bg.jpg",
                middlegroundImagePath = "scenes/city/city_dawn_mg.png",
                foregroundImagePath = "scenes/city/city_dawn_fg.png",
                category = SceneCategory.CITY,
                season = null,
                timeOfDay = TimeOfDay.DAWN,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            Scene(
                id = "city_night",
                name = "都市夜景",
                description = "繁华都市的夜晚，灯火辉煌",
                backgroundImagePath = "scenes/city/city_night_bg.jpg",
                middlegroundImagePath = "scenes/city/city_night_mg.png",
                foregroundImagePath = "scenes/city/city_night_fg.png",
                category = SceneCategory.CITY,
                season = null,
                timeOfDay = TimeOfDay.NIGHT,
                weatherType = WeatherType.CLEAR,
                isPremium = false
            ),
            
            // 抽象艺术系列
            Scene(
                id = "abstract_flow",
                name = "流动艺术",
                description = "抽象的色彩流动效果",
                backgroundImagePath = "scenes/abstract/abstract_flow_bg.jpg",
                middlegroundImagePath = "scenes/abstract/abstract_flow_mg.png",
                foregroundImagePath = "scenes/abstract/abstract_flow_fg.png",
                category = SceneCategory.ABSTRACT,
                season = null,
                timeOfDay = null,
                weatherType = null,
                isPremium = false
            ),
            Scene(
                id = "abstract_geometric",
                name = "几何空间",
                description = "现代几何图形艺术",
                backgroundImagePath = "scenes/abstract/abstract_geometric_bg.jpg",
                middlegroundImagePath = "scenes/abstract/abstract_geometric_mg.png",
                foregroundImagePath = "scenes/abstract/abstract_geometric_fg.png",
                category = SceneCategory.ABSTRACT,
                season = null,
                timeOfDay = null,
                weatherType = null,
                isPremium = false
            ),
            
            // 太空系列
            Scene(
                id = "space_nebula",
                name = "星云奇观",
                description = "绚烂的宇宙星云",
                backgroundImagePath = "scenes/space/space_nebula_bg.jpg",
                middlegroundImagePath = "scenes/space/space_nebula_mg.png",
                foregroundImagePath = "scenes/space/space_nebula_fg.png",
                category = SceneCategory.SPACE,
                season = null,
                timeOfDay = TimeOfDay.NIGHT,
                weatherType = null,
                isPremium = false
            ),
            Scene(
                id = "space_earth",
                name = "地球视角",
                description = "从太空俯瞰美丽的地球",
                backgroundImagePath = "scenes/space/space_earth_bg.jpg",
                middlegroundImagePath = "scenes/space/space_earth_mg.png",
                foregroundImagePath = "scenes/space/space_earth_fg.png",
                category = SceneCategory.SPACE,
                season = null,
                timeOfDay = null,
                weatherType = null,
                isPremium = false
            )
        )
    }
    
    /**
     * 检查是否需要初始化
     */
    suspend fun shouldInitialize(): Boolean {
        return try {
            val result = sceneRepository.getAllScenes().first()
            when (result) {
                is Resource.Success -> result.data.isEmpty()
                else -> true
            }
        } catch (e: Exception) {
            Logger.e("Error checking initialization status", e)
            true
        }
    }
}

/**
 * 扩展函数，获取第一个元素
 */
private suspend fun <T> kotlinx.coroutines.flow.Flow<T>.first(): T {
    return kotlinx.coroutines.flow.first()
}
