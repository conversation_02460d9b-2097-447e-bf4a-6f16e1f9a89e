package com.livewallpaper.core.domain.scene

import android.graphics.*
import com.livewallpaper.core.domain.time.TimePhase
import com.livewallpaper.core.domain.time.TimeProgress
import com.livewallpaper.core.utils.Logger
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 场景渲染器
 * 负责将场景图层渲染到Canvas上，包括视差效果和动态元素
 */
@Singleton
class SceneRenderer @Inject constructor() {
    
    private val backgroundPaint = Paint().apply {
        isAntiAlias = true
        isFilterBitmap = true
    }
    
    private val overlayPaint = Paint().apply {
        isAntiAlias = true
    }
    
    private val parallaxOffsetX = 0f
    private val parallaxOffsetY = 0f
    
    /**
     * 渲染完整场景
     */
    fun renderScene(
        canvas: Canvas,
        sceneLayers: SceneLayers,
        timeProgress: TimeProgress,
        animationTime: Float,
        enableParallax: Boolean = true
    ) {
        val width = canvas.width.toFloat()
        val height = canvas.height.toFloat()
        
        try {
            // 1. 渲染背景图层
            renderBackgroundLayer(canvas, sceneLayers.background, width, height, enableParallax)
            
            // 2. 渲染时间相关的天空效果
            renderSkyEffects(canvas, timeProgress, width, height)
            
            // 3. 渲染中景图层（带视差效果）
            renderMiddlegroundLayer(canvas, sceneLayers.middleground, width, height, enableParallax, animationTime)
            
            // 4. 渲染动态元素（粒子效果等）
            renderDynamicElements(canvas, timeProgress, animationTime, width, height)
            
            // 5. 渲染前景图层
            renderForegroundLayer(canvas, sceneLayers.foreground, width, height)
            
            // 6. 渲染时间覆盖层（夜晚变暗等）
            renderTimeOverlay(canvas, timeProgress, width, height)
            
        } catch (e: Exception) {
            Logger.e("Error rendering scene", e)
        }
    }
    
    /**
     * 渲染背景图层
     */
    private fun renderBackgroundLayer(
        canvas: Canvas,
        background: Bitmap?,
        width: Float,
        height: Float,
        enableParallax: Boolean
    ) {
        background?.let { bitmap ->
            if (bitmap.isRecycled) return
            
            val srcRect = Rect(0, 0, bitmap.width, bitmap.height)
            val destRect = if (enableParallax) {
                // 背景图层移动最慢
                val offsetX = parallaxOffsetX * 0.1f
                val offsetY = parallaxOffsetY * 0.1f
                RectF(offsetX, offsetY, width + offsetX, height + offsetY)
            } else {
                RectF(0f, 0f, width, height)
            }
            
            canvas.drawBitmap(bitmap, srcRect, destRect, backgroundPaint)
        }
    }
    
    /**
     * 渲染天空效果
     */
    private fun renderSkyEffects(
        canvas: Canvas,
        timeProgress: TimeProgress,
        width: Float,
        height: Float
    ) {
        // 根据时间阶段添加天空渐变效果
        val skyGradient = createSkyGradient(timeProgress, width, height)
        if (skyGradient != null) {
            overlayPaint.shader = skyGradient
            overlayPaint.alpha = (128 * getSkyIntensity(timeProgress.phase)).toInt()
            canvas.drawRect(0f, 0f, width, height * 0.6f, overlayPaint)
            overlayPaint.shader = null
        }
    }
    
    /**
     * 创建天空渐变
     */
    private fun createSkyGradient(timeProgress: TimeProgress, width: Float, height: Float): LinearGradient? {
        val colors = when (timeProgress.phase) {
            TimePhase.NIGHT -> intArrayOf(
                Color.argb(100, 10, 10, 40),
                Color.argb(50, 5, 5, 20),
                Color.TRANSPARENT
            )
            TimePhase.CIVIL_TWILIGHT -> {
                if (timeProgress.progress < 0.5) {
                    // 黎明
                    intArrayOf(
                        Color.argb(120, 255, 200, 100),
                        Color.argb(80, 255, 150, 80),
                        Color.TRANSPARENT
                    )
                } else {
                    // 黄昏
                    intArrayOf(
                        Color.argb(120, 255, 120, 60),
                        Color.argb(80, 200, 80, 120),
                        Color.TRANSPARENT
                    )
                }
            }
            TimePhase.DAY -> intArrayOf(
                Color.argb(60, 135, 206, 250),
                Color.argb(30, 176, 224, 230),
                Color.TRANSPARENT
            )
            else -> return null
        }
        
        return LinearGradient(
            0f, 0f,
            0f, height * 0.6f,
            colors,
            floatArrayOf(0f, 0.7f, 1f),
            Shader.TileMode.CLAMP
        )
    }
    
    /**
     * 获取天空效果强度
     */
    private fun getSkyIntensity(phase: TimePhase): Float {
        return when (phase) {
            TimePhase.NIGHT -> 0.8f
            TimePhase.CIVIL_TWILIGHT -> 1.0f
            TimePhase.DAY -> 0.3f
            else -> 0.5f
        }
    }
    
    /**
     * 渲染中景图层
     */
    private fun renderMiddlegroundLayer(
        canvas: Canvas,
        middleground: Bitmap?,
        width: Float,
        height: Float,
        enableParallax: Boolean,
        animationTime: Float
    ) {
        middleground?.let { bitmap ->
            if (bitmap.isRecycled) return
            
            val srcRect = Rect(0, 0, bitmap.width, bitmap.height)
            val destRect = if (enableParallax) {
                // 中景图层移动速度中等
                val offsetX = parallaxOffsetX * 0.3f
                val offsetY = parallaxOffsetY * 0.3f + sin(animationTime * 0.5).toFloat() * 2f
                RectF(offsetX, offsetY, width + offsetX, height + offsetY)
            } else {
                RectF(0f, 0f, width, height)
            }
            
            canvas.drawBitmap(bitmap, srcRect, destRect, backgroundPaint)
        }
    }
    
    /**
     * 渲染动态元素
     */
    private fun renderDynamicElements(
        canvas: Canvas,
        timeProgress: TimeProgress,
        animationTime: Float,
        width: Float,
        height: Float
    ) {
        // 根据时间阶段渲染不同的动态元素
        when (timeProgress.phase) {
            TimePhase.NIGHT -> renderStars(canvas, animationTime, width, height)
            TimePhase.DAY -> renderClouds(canvas, animationTime, width, height)
            TimePhase.CIVIL_TWILIGHT -> renderTwilightEffects(canvas, animationTime, width, height)
            else -> {}
        }
    }
    
    /**
     * 渲染星星
     */
    private fun renderStars(canvas: Canvas, animationTime: Float, width: Float, height: Float) {
        val starPaint = Paint().apply {
            color = Color.WHITE
            isAntiAlias = true
        }
        
        // 简单的星星效果
        for (i in 0 until 20) {
            val x = (width * 0.1f + (width * 0.8f * (i * 37) % 100) / 100f)
            val y = (height * 0.1f + (height * 0.4f * (i * 73) % 100) / 100f)
            val alpha = (128 + 127 * sin(animationTime + i)).toInt().coerceIn(50, 255)
            val radius = 1f + sin(animationTime * 2 + i) * 0.5f
            
            starPaint.alpha = alpha
            canvas.drawCircle(x, y, radius, starPaint)
        }
    }
    
    /**
     * 渲染云朵
     */
    private fun renderClouds(canvas: Canvas, animationTime: Float, width: Float, height: Float) {
        val cloudPaint = Paint().apply {
            color = Color.WHITE
            alpha = 100
            isAntiAlias = true
        }
        
        // 简单的云朵移动效果
        for (i in 0 until 3) {
            val baseX = width * (i + 1) / 4f
            val x = baseX + (animationTime * 10f + i * 100f) % (width + 200f) - 100f
            val y = height * 0.2f + sin(animationTime * 0.3f + i) * 20f
            
            // 绘制简单的椭圆云朵
            canvas.drawOval(x - 40f, y - 15f, x + 40f, y + 15f, cloudPaint)
            canvas.drawOval(x - 25f, y - 25f, x + 25f, y + 5f, cloudPaint)
            canvas.drawOval(x + 10f, y - 20f, x + 50f, y + 10f, cloudPaint)
        }
    }
    
    /**
     * 渲染暮光效果
     */
    private fun renderTwilightEffects(canvas: Canvas, animationTime: Float, width: Float, height: Float) {
        // 暮光时的光线效果
        val rayPaint = Paint().apply {
            color = Color.argb(30, 255, 200, 100)
            isAntiAlias = true
        }
        
        for (i in 0 until 5) {
            val angle = (animationTime * 0.1f + i * 36f) % 360f
            val startX = width / 2f
            val startY = height * 0.3f
            val endX = startX + cos(Math.toRadians(angle.toDouble())).toFloat() * width * 0.6f
            val endY = startY + sin(Math.toRadians(angle.toDouble())).toFloat() * height * 0.3f
            
            canvas.drawLine(startX, startY, endX, endY, rayPaint)
        }
    }
    
    /**
     * 渲染前景图层
     */
    private fun renderForegroundLayer(
        canvas: Canvas,
        foreground: Bitmap?,
        width: Float,
        height: Float
    ) {
        foreground?.let { bitmap ->
            if (bitmap.isRecycled) return
            
            val srcRect = Rect(0, 0, bitmap.width, bitmap.height)
            // 前景图层移动最快，视差效果最明显
            val offsetX = parallaxOffsetX * 0.8f
            val offsetY = parallaxOffsetY * 0.8f
            val destRect = RectF(offsetX, offsetY, width + offsetX, height + offsetY)
            
            canvas.drawBitmap(bitmap, srcRect, destRect, backgroundPaint)
        }
    }
    
    /**
     * 渲染时间覆盖层
     */
    private fun renderTimeOverlay(
        canvas: Canvas,
        timeProgress: TimeProgress,
        width: Float,
        height: Float
    ) {
        val overlayAlpha = when (timeProgress.phase) {
            TimePhase.NIGHT -> 120
            TimePhase.ASTRONOMICAL_TWILIGHT -> 80
            TimePhase.NAUTICAL_TWILIGHT -> 50
            TimePhase.CIVIL_TWILIGHT -> 20
            else -> 0
        }
        
        if (overlayAlpha > 0) {
            overlayPaint.color = Color.argb(overlayAlpha, 0, 0, 50)
            canvas.drawRect(0f, 0f, width, height, overlayPaint)
        }
    }
    
    /**
     * 更新视差偏移
     */
    fun updateParallaxOffset(offsetX: Float, offsetY: Float) {
        // 这里可以根据用户交互或传感器数据更新视差偏移
        // 暂时保持静态
    }
}
