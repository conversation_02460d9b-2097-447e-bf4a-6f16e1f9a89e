package com.livewallpaper.core.domain.music

import android.content.ComponentName
import android.content.Context
import android.provider.Settings
import com.livewallpaper.core.data.model.MusicInfo
import com.livewallpaper.core.data.model.MusicNotificationData
import com.livewallpaper.core.data.model.MusicVisualizationData
import com.livewallpaper.core.data.model.PlaybackState
import com.livewallpaper.core.service.MusicDataBroadcaster
import com.livewallpaper.core.service.MusicNotificationListenerService
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音乐管理器
 * 负责音乐数据的管理和状态监控
 */
@Singleton
class MusicManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val musicDataBroadcaster: MusicDataBroadcaster
) {
    
    private var currentMusicInfo: MusicInfo? = null
    private var lastUpdateTime = Instant.DISTANT_PAST
    
    /**
     * 音乐数据流
     */
    val musicDataFlow: Flow<MusicState> = musicDataBroadcaster.musicDataFlow
        .map { notificationData ->
            try {
                val musicInfo = notificationData.toMusicInfo()
                currentMusicInfo = musicInfo
                lastUpdateTime = Clock.System.now()
                
                MusicState.Success(
                    musicInfo = musicInfo,
                    timestamp = Clock.System.now()
                )
            } catch (e: Exception) {
                Logger.e("Error processing music data", e)
                MusicState.Error("音乐数据处理失败: ${e.message}")
            }
        }
        .catch { e ->
            Logger.e("Music data flow error", e)
            emit(MusicState.Error("音乐数据流错误: ${e.message}"))
        }
        .stateIn(
            scope = kotlinx.coroutines.GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = MusicState.NoMusic
        )
    
    /**
     * 音乐可视化数据流
     */
    val visualizationDataFlow: Flow<MusicVisualizationData> = musicDataFlow
        .map { musicState ->
            when (musicState) {
                is MusicState.Success -> {
                    val musicInfo = musicState.musicInfo
                    MusicVisualizationData(
                        isPlaying = musicInfo.isPlaying,
                        volume = if (musicInfo.isPlaying) 0.7f else 0f,
                        progress = musicInfo.getProgress(),
                        bassLevel = if (musicInfo.isPlaying) generateRandomLevel() else 0f,
                        midLevel = if (musicInfo.isPlaying) generateRandomLevel() else 0f,
                        trebleLevel = if (musicInfo.isPlaying) generateRandomLevel() else 0f
                    )
                }
                else -> {
                    MusicVisualizationData(
                        isPlaying = false,
                        volume = 0f,
                        progress = 0f
                    )
                }
            }
        }
        .distinctUntilChanged()
    
    /**
     * 获取当前音乐信息
     */
    fun getCurrentMusicInfo(): MusicInfo? {
        return currentMusicInfo
    }
    
    /**
     * 获取当前播放状态
     */
    fun getCurrentPlaybackState(): PlaybackState {
        return when {
            currentMusicInfo == null -> PlaybackState.UNKNOWN
            currentMusicInfo?.isPlaying == true -> PlaybackState.PLAYING
            else -> PlaybackState.PAUSED
        }
    }
    
    /**
     * 检查是否有音乐正在播放
     */
    fun isMusicPlaying(): Boolean {
        return currentMusicInfo?.isPlaying == true
    }
    
    /**
     * 检查通知监听权限
     */
    fun hasNotificationListenerPermission(): Boolean {
        return try {
            val enabledListeners = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )
            val componentName = ComponentName(context, MusicNotificationListenerService::class.java)
            enabledListeners?.contains(componentName.flattenToString()) == true
        } catch (e: Exception) {
            Logger.e("Error checking notification listener permission", e)
            false
        }
    }
    
    /**
     * 获取音乐应用名称
     */
    fun getMusicAppName(packageName: String): String {
        return try {
            val packageManager = context.packageManager
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(appInfo).toString()
        } catch (e: Exception) {
            Logger.e("Error getting app name for $packageName", e)
            packageName
        }
    }
    
    /**
     * 检查数据是否过期
     */
    fun isDataStale(): Boolean {
        val now = Clock.System.now()
        val timeSinceLastUpdate = now - lastUpdateTime
        return timeSinceLastUpdate.inWholeSeconds > 30 // 30秒后认为数据过期
    }
    
    /**
     * 生成随机音频级别（模拟音频可视化）
     */
    private fun generateRandomLevel(): Float {
        return (0.3f + Math.random().toFloat() * 0.7f).coerceIn(0f, 1f)
    }
    
    /**
     * 获取音乐信息摘要
     */
    fun getMusicSummary(): String {
        return currentMusicInfo?.let { music ->
            "${music.title} - ${music.artist}"
        } ?: "无音乐播放"
    }
    
    /**
     * 检查是否应该显示音乐卡片
     */
    fun shouldShowMusicCard(): Boolean {
        return currentMusicInfo != null && !isDataStale()
    }
}

/**
 * 音乐状态密封类
 */
sealed class MusicState {
    object NoMusic : MusicState()
    object Loading : MusicState()
    
    data class Success(
        val musicInfo: MusicInfo,
        val timestamp: Instant
    ) : MusicState()
    
    data class Error(val message: String) : MusicState()
}

/**
 * 音乐状态扩展函数
 */
fun MusicState.getMusicInfoOrNull(): MusicInfo? {
    return when (this) {
        is MusicState.Success -> musicInfo
        else -> null
    }
}

fun MusicState.isPlaying(): Boolean {
    return when (this) {
        is MusicState.Success -> musicInfo.isPlaying
        else -> false
    }
}

fun MusicState.getDisplayText(): String {
    return when (this) {
        is MusicState.Success -> "${musicInfo.title} - ${musicInfo.artist}"
        is MusicState.Error -> "音乐获取失败"
        is MusicState.Loading -> "加载中..."
        is MusicState.NoMusic -> "无音乐播放"
    }
}
