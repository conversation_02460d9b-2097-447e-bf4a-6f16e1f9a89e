package com.livewallpaper.core.domain.scene

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import com.livewallpaper.core.data.model.Scene
import com.livewallpaper.core.data.model.SceneCategory
import com.livewallpaper.core.data.model.Season
import com.livewallpaper.core.data.model.TimeOfDay
import com.livewallpaper.core.data.repository.SceneRepository
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.collect
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 自定义场景管理器
 * 处理用户自定义场景的创建、编辑和管理
 */
@Singleton
class CustomSceneManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val sceneRepository: SceneRepository
) {

    private val customScenesDir = File(context.filesDir, "custom_scenes")

    init {
        // 确保自定义场景目录存在
        if (!customScenesDir.exists()) {
            customScenesDir.mkdirs()
        }
    }

    /**
     * 从用户相册创建自定义场景
     */
    suspend fun createCustomSceneFromGallery(
        imageUri: Uri,
        sceneName: String,
        description: String = "",
        category: SceneCategory = SceneCategory.CUSTOM,
        season: Season? = null,
        timeOfDay: TimeOfDay? = null
    ): Resource<Scene> = withContext(Dispatchers.IO) {
        try {
            Logger.d("Creating custom scene from gallery: $sceneName")

            // 验证输入
            if (sceneName.isBlank()) {
                return@withContext Resource.Error("Scene name cannot be empty")
            }

            // 检查权限
            if (!hasStoragePermission()) {
                return@withContext Resource.Error("Storage permission required")
            }

            // 读取和处理图片
            val processedImage = processImageFromUri(imageUri)
                ?: return@withContext Resource.Error("Failed to process image")

            // 生成唯一ID和文件路径
            val sceneId = "custom_${UUID.randomUUID()}"
            val backgroundPath = saveImageToCustomDir(processedImage, "${sceneId}_background.jpg")
                ?: return@withContext Resource.Error("Failed to save image")

            // 创建场景对象
            val customScene = Scene(
                id = sceneId,
                name = sceneName,
                description = description.ifBlank { "用户自定义场景" },
                backgroundImagePath = backgroundPath,
                foregroundImagePath = null,
                middlegroundImagePath = null,
                category = category,
                season = season,
                timeOfDay = timeOfDay,
                isPremium = false,
                isCustom = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            // 保存到数据库
            val insertResult = sceneRepository.insertScene(customScene)

            Logger.d("Custom scene created successfully: $sceneName")
            Resource.Success(customScene)

        } catch (e: Exception) {
            Logger.e("Failed to create custom scene", e)
            Resource.Error("Failed to create custom scene: ${e.message}")
        }
    }

    /**
     * 创建分层自定义场景
     */
    suspend fun createLayeredCustomScene(
        backgroundUri: Uri,
        middlegroundUri: Uri? = null,
        foregroundUri: Uri? = null,
        sceneName: String,
        description: String = ""
    ): Resource<Scene> = withContext(Dispatchers.IO) {
        try {
            Logger.d("Creating layered custom scene: $sceneName")

            if (sceneName.isBlank()) {
                return@withContext Resource.Error("Scene name cannot be empty")
            }

            if (!hasStoragePermission()) {
                return@withContext Resource.Error("Storage permission required")
            }

            val sceneId = "custom_layered_${UUID.randomUUID()}"

            // 处理背景图片
            val backgroundImage = processImageFromUri(backgroundUri)
                ?: return@withContext Resource.Error("Failed to process background image")
            val backgroundPath = saveImageToCustomDir(backgroundImage, "${sceneId}_background.jpg")
                ?: return@withContext Resource.Error("Failed to save background image")

            // 处理中景图片（可选）
            var middlegroundPath: String? = null
            if (middlegroundUri != null) {
                val middlegroundImage = processImageFromUri(middlegroundUri)
                if (middlegroundImage != null) {
                    middlegroundPath = saveImageToCustomDir(middlegroundImage, "${sceneId}_middleground.png")
                }
            }

            // 处理前景图片（可选）
            var foregroundPath: String? = null
            if (foregroundUri != null) {
                val foregroundImage = processImageFromUri(foregroundUri)
                if (foregroundImage != null) {
                    foregroundPath = saveImageToCustomDir(foregroundImage, "${sceneId}_foreground.png")
                }
            }

            // 创建分层场景
            val layeredScene = Scene(
                id = sceneId,
                name = sceneName,
                description = description.ifBlank { "用户自定义分层场景" },
                backgroundImagePath = backgroundPath,
                middlegroundImagePath = middlegroundPath,
                foregroundImagePath = foregroundPath,
                category = SceneCategory.CUSTOM,
                season = null,
                timeOfDay = null,
                isPremium = false,
                isCustom = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            sceneRepository.insertScene(layeredScene)

            Logger.d("Layered custom scene created successfully: $sceneName")
            Resource.Success(layeredScene)

        } catch (e: Exception) {
            Logger.e("Failed to create layered custom scene", e)
            Resource.Error("Failed to create layered custom scene: ${e.message}")
        }
    }

    /**
     * 编辑自定义场景
     */
    suspend fun editCustomScene(
        sceneId: String,
        newName: String? = null,
        newDescription: String? = null,
        newBackgroundUri: Uri? = null
    ): Resource<Scene> = withContext(Dispatchers.IO) {
        try {
            Logger.d("Editing custom scene: $sceneId")

            val sceneResult = sceneRepository.getSceneById(sceneId)
            val existingScene = when (sceneResult) {
                is Resource.Success -> sceneResult.data
                is Resource.Error -> return@withContext Resource.Error(sceneResult.message ?: "Scene not found")
                is Resource.Loading -> return@withContext Resource.Error("Scene loading")
            } ?: return@withContext Resource.Error("Scene not found")

            if (!existingScene.isCustom) {
                return@withContext Resource.Error("Cannot edit non-custom scene")
            }

            var updatedScene = existingScene

            // 更新名称
            if (newName != null && newName.isNotBlank()) {
                updatedScene = updatedScene.copy(name = newName)
            }

            // 更新描述
            if (newDescription != null) {
                updatedScene = updatedScene.copy(description = newDescription)
            }

            // 更新背景图片
            if (newBackgroundUri != null) {
                val newBackgroundImage = processImageFromUri(newBackgroundUri)
                    ?: return@withContext Resource.Error("Failed to process new background image")

                val newBackgroundPath = saveImageToCustomDir(
                    newBackgroundImage,
                    "${sceneId}_background_${System.currentTimeMillis()}.jpg"
                ) ?: return@withContext Resource.Error("Failed to save new background image")

                // 删除旧的背景图片
                deleteImageFile(existingScene.backgroundImagePath)

                updatedScene = updatedScene.copy(backgroundImagePath = newBackgroundPath)
            }

            // 更新时间戳
            updatedScene = updatedScene.copy(updatedAt = System.currentTimeMillis())

            // 保存更新
            val updateResult = sceneRepository.updateScene(updatedScene)

            when (updateResult) {
                is Resource.Success -> {
                    Logger.d("Custom scene edited successfully: $sceneId")
                    Resource.Success(updatedScene)
                }
                is Resource.Error -> {
                    Logger.e("Failed to update scene: ${updateResult.message}")
                    Resource.Error(updateResult.message ?: "Update failed")
                }
                is Resource.Loading -> {
                    Resource.Error("Unexpected loading state")
                }
            }

        } catch (e: Exception) {
            Logger.e("Failed to edit custom scene", e)
            Resource.Error("Failed to edit custom scene: ${e.message}")
        }
    }

    /**
     * 删除自定义场景
     */
    suspend fun deleteCustomScene(sceneId: String): Resource<Unit> = withContext(Dispatchers.IO) {
        try {
            Logger.d("Deleting custom scene: $sceneId")

            val sceneResult = sceneRepository.getSceneById(sceneId)
            val scene = when (sceneResult) {
                is Resource.Success -> sceneResult.data
                is Resource.Error -> return@withContext Resource.Error(sceneResult.message ?: "Scene not found")
                is Resource.Loading -> return@withContext Resource.Error("Scene loading")
            } ?: return@withContext Resource.Error("Scene not found")

            if (!scene.isCustom) {
                return@withContext Resource.Error("Cannot delete non-custom scene")
            }

            // 删除图片文件
            deleteImageFile(scene.backgroundImagePath)
            scene.middlegroundImagePath?.let { deleteImageFile(it) }
            scene.foregroundImagePath?.let { deleteImageFile(it) }

            // 从数据库删除
            val deleteResult = sceneRepository.deleteScene(scene)

            when (deleteResult) {
                is Resource.Success -> {
                    Logger.d("Custom scene deleted successfully: $sceneId")
                    Resource.Success(Unit)
                }
                is Resource.Error -> {
                    Logger.e("Failed to delete scene: ${deleteResult.message}")
                    Resource.Error(deleteResult.message ?: "Delete failed")
                }
                is Resource.Loading -> {
                    Resource.Error("Unexpected loading state")
                }
            }

        } catch (e: Exception) {
            Logger.e("Failed to delete custom scene", e)
            Resource.Error("Failed to delete custom scene: ${e.message}")
        }
    }

    /**
     * 获取所有自定义场景
     */
    suspend fun getAllCustomScenes(): Resource<List<Scene>> {
        return try {
            // 使用Flow的first()方法获取第一个值
            val result = sceneRepository.getScenesByCategory(SceneCategory.CUSTOM)
            // 这里需要收集Flow的值
            var finalResult: Resource<List<Scene>> = Resource.Loading()
            result.collect { resource ->
                finalResult = resource
            }
            finalResult
        } catch (e: Exception) {
            Logger.e("Failed to get custom scenes", e)
            Resource.Error("Failed to get custom scenes: ${e.message}")
        }
    }

    /**
     * 从URI处理图片
     */
    private suspend fun processImageFromUri(uri: Uri): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            inputStream?.use { stream ->
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeStream(stream, null, options)

                // 计算采样率以优化内存使用
                val sampleSize = calculateInSampleSize(options, 1920, 1080)

                // 重新打开流进行实际解码
                context.contentResolver.openInputStream(uri)?.use { newStream ->
                    val decodeOptions = BitmapFactory.Options().apply {
                        inSampleSize = sampleSize
                        inPreferredConfig = Bitmap.Config.RGB_565
                    }
                    BitmapFactory.decodeStream(newStream, null, decodeOptions)
                }
            }
        } catch (e: Exception) {
            Logger.e("Failed to process image from URI", e)
            null
        }
    }

    /**
     * 保存图片到自定义目录
     */
    private suspend fun saveImageToCustomDir(bitmap: Bitmap, fileName: String): String? = withContext(Dispatchers.IO) {
        try {
            val file = File(customScenesDir, fileName)
            FileOutputStream(file).use { out ->
                val format = if (fileName.endsWith(".png")) Bitmap.CompressFormat.PNG else Bitmap.CompressFormat.JPEG
                bitmap.compress(format, 85, out)
            }
            file.absolutePath
        } catch (e: Exception) {
            Logger.e("Failed to save image", e)
            null
        }
    }

    /**
     * 删除图片文件
     */
    private fun deleteImageFile(imagePath: String) {
        try {
            val file = File(imagePath)
            if (file.exists()) {
                file.delete()
                Logger.d("Deleted image file: $imagePath")
            }
        } catch (e: Exception) {
            Logger.e("Failed to delete image file: $imagePath", e)
        }
    }

    /**
     * 计算采样率
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }

    /**
     * 检查存储权限
     */
    private fun hasStoragePermission(): Boolean {
        // TODO: 实际检查存储权限
        return true // 简化处理
    }
}
