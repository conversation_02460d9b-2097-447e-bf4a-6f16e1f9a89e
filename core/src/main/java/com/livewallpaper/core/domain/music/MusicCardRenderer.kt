package com.livewallpaper.core.domain.music

import android.graphics.*
import androidx.core.graphics.ColorUtils
import android.graphics.Color as AndroidColor
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.utils.Logger
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 音乐卡片渲染器
 * 负责在Canvas上绘制音乐信息卡片
 */
@Singleton
class MusicCardRenderer @Inject constructor() {

    private val cardPaint = Paint().apply {
        isAntiAlias = true
    }

    private val textPaint = Paint().apply {
        isAntiAlias = true
        textAlign = Paint.Align.LEFT
    }

    private val progressPaint = Paint().apply {
        isAntiAlias = true
        strokeCap = Paint.Cap.ROUND
    }

    /**
     * 渲染音乐卡片
     */
    fun renderMusicCard(
        canvas: Canvas,
        musicInfo: MusicInfo?,
        visualizationData: MusicVisualizationData,
        config: MusicCardConfig,
        canvasWidth: Float,
        canvasHeight: Float,
        animationTime: Float
    ) {
        if (musicInfo == null || !visualizationData.isPlaying) return

        try {
            val cardWidth = 280f
            val cardHeight = 120f
            val margin = 20f

            // 计算卡片位置
            val cardPosition = calculateCardPosition(
                config.cardPosition,
                canvasWidth,
                canvasHeight,
                cardWidth,
                cardHeight,
                margin
            )

            // 绘制卡片背景
            drawCardBackground(canvas, cardPosition, cardWidth, cardHeight, config.cardOpacity)

            // 绘制专辑封面
            if (config.showAlbumArt) {
                drawAlbumArt(canvas, cardPosition, cardHeight - 20f)
            }

            // 绘制音乐信息
            drawMusicInfo(canvas, musicInfo, cardPosition, cardWidth, cardHeight)

            // 绘制进度条
            if (config.showProgress) {
                drawProgressBar(canvas, musicInfo, cardPosition, cardWidth, cardHeight)
            }

            // 绘制可视化效果
            if (config.enableVisualization) {
                drawVisualization(canvas, visualizationData, cardPosition, cardWidth, cardHeight, animationTime)
            }

        } catch (e: Exception) {
            Logger.e("Error rendering music card", e)
        }
    }

    /**
     * 计算卡片位置
     */
    private fun calculateCardPosition(
        position: CardPosition,
        canvasWidth: Float,
        canvasHeight: Float,
        cardWidth: Float,
        cardHeight: Float,
        margin: Float
    ): PointF {
        return when (position) {
            CardPosition.TOP_LEFT -> PointF(margin, margin)
            CardPosition.TOP_RIGHT -> PointF(canvasWidth - cardWidth - margin, margin)
            CardPosition.BOTTOM_LEFT -> PointF(margin, canvasHeight - cardHeight - margin)
            CardPosition.BOTTOM_RIGHT -> PointF(canvasWidth - cardWidth - margin, canvasHeight - cardHeight - margin)
            CardPosition.CENTER -> PointF(
                (canvasWidth - cardWidth) / 2f,
                (canvasHeight - cardHeight) / 2f
            )
        }
    }

    /**
     * 绘制卡片背景
     */
    private fun drawCardBackground(
        canvas: Canvas,
        position: PointF,
        width: Float,
        height: Float,
        opacity: Float
    ) {
        // 绘制阴影
        cardPaint.color = android.graphics.AndroidColor.argb((opacity * 100).toInt(), 0, 0, 0)
        canvas.drawRoundRect(
            position.x + 4f, position.y + 4f,
            position.x + width + 4f, position.y + height + 4f,
            16f, 16f, cardPaint
        )

        // 绘制卡片背景
        cardPaint.color = android.graphics.AndroidColor.argb((opacity * 255).toInt(), 40, 40, 40)
        canvas.drawRoundRect(
            position.x, position.y,
            position.x + width, position.y + height,
            16f, 16f, cardPaint
        )

        // 绘制边框
        cardPaint.style = Paint.Style.STROKE
        cardPaint.strokeWidth = 1f
        cardPaint.color = AndroidColor.argb((opacity * 150).toInt(), 255, 255, 255)
        canvas.drawRoundRect(
            position.x, position.y,
            position.x + width, position.y + height,
            16f, 16f, cardPaint
        )
        cardPaint.style = Paint.Style.FILL
    }

    /**
     * 绘制专辑封面占位符
     */
    private fun drawAlbumArt(canvas: Canvas, position: PointF, size: Float) {
        val albumArtX = position.x + 10f
        val albumArtY = position.y + 10f

        // 绘制专辑封面背景
        cardPaint.color = AndroidColor.argb(150, 80, 80, 80)
        canvas.drawRoundRect(
            albumArtX, albumArtY,
            albumArtX + size, albumArtY + size,
            8f, 8f, cardPaint
        )

        // 绘制音乐图标
        cardPaint.color = AndroidColor.argb(200, 255, 255, 255)
        val iconSize = size * 0.4f
        val iconX = albumArtX + (size - iconSize) / 2f
        val iconY = albumArtY + (size - iconSize) / 2f

        // 简单的音符图标
        canvas.drawCircle(iconX + iconSize * 0.3f, iconY + iconSize * 0.7f, iconSize * 0.15f, cardPaint)
        canvas.drawRect(
            iconX + iconSize * 0.3f - 2f, iconY + iconSize * 0.2f,
            iconX + iconSize * 0.3f + 2f, iconY + iconSize * 0.7f,
            cardPaint
        )
    }

    /**
     * 绘制音乐信息
     */
    private fun drawMusicInfo(
        canvas: Canvas,
        musicInfo: MusicInfo,
        position: PointF,
        cardWidth: Float,
        cardHeight: Float
    ) {
        val textStartX = position.x + 120f // 专辑封面后面
        val textStartY = position.y + 25f

        // 绘制歌曲标题
        textPaint.color = AndroidColor.argb(255, 255, 255, 255)
        textPaint.textSize = 16f
        textPaint.typeface = Typeface.DEFAULT_BOLD

        val titleText = truncateText(musicInfo.title, 18)
        canvas.drawText(titleText, textStartX, textStartY, textPaint)

        // 绘制艺术家
        textPaint.color = AndroidColor.argb(200, 200, 200, 200)
        textPaint.textSize = 14f
        textPaint.typeface = Typeface.DEFAULT

        val artistText = truncateText(musicInfo.artist, 20)
        canvas.drawText(artistText, textStartX, textStartY + 25f, textPaint)

        // 绘制专辑
        if (musicInfo.album.isNotBlank()) {
            textPaint.color = AndroidColor.argb(150, 180, 180, 180)
            textPaint.textSize = 12f

            val albumText = truncateText(musicInfo.album, 22)
            canvas.drawText(albumText, textStartX, textStartY + 45f, textPaint)
        }
    }

    /**
     * 绘制进度条
     */
    private fun drawProgressBar(
        canvas: Canvas,
        musicInfo: MusicInfo,
        position: PointF,
        cardWidth: Float,
        cardHeight: Float
    ) {
        val progressY = position.y + cardHeight - 25f
        val progressStartX = position.x + 120f
        val progressWidth = cardWidth - 130f
        val progressHeight = 4f

        // 绘制进度条背景
        progressPaint.color = AndroidColor.argb(100, 255, 255, 255)
        progressPaint.strokeWidth = progressHeight
        canvas.drawLine(
            progressStartX, progressY,
            progressStartX + progressWidth, progressY,
            progressPaint
        )

        // 绘制进度
        val progress = musicInfo.getProgress()
        if (progress > 0f) {
            progressPaint.color = AndroidColor.argb(200, 100, 150, 255)
            canvas.drawLine(
                progressStartX, progressY,
                progressStartX + progressWidth * progress, progressY,
                progressPaint
            )
        }

        // 绘制时间信息
        textPaint.color = AndroidColor.argb(150, 200, 200, 200)
        textPaint.textSize = 10f

        val timeText = "${musicInfo.getFormattedPosition()} / ${musicInfo.getFormattedDuration()}"
        canvas.drawText(timeText, progressStartX, progressY + 15f, textPaint)
    }

    /**
     * 绘制可视化效果
     */
    private fun drawVisualization(
        canvas: Canvas,
        visualizationData: MusicVisualizationData,
        position: PointF,
        cardWidth: Float,
        cardHeight: Float,
        animationTime: Float
    ) {
        if (!visualizationData.isPlaying) return

        val barCount = 20
        val barWidth = 2f
        val barSpacing = 1f
        val maxBarHeight = 30f
        val startX = position.x + cardWidth - 80f
        val startY = position.y + cardHeight - 40f

        progressPaint.strokeWidth = barWidth
        progressPaint.strokeCap = Paint.Cap.ROUND

        for (i in 0 until barCount) {
            val x = startX + i * (barWidth + barSpacing)

            // 生成动态高度
            val baseHeight = when {
                i < barCount * 0.3 -> visualizationData.bassLevel
                i < barCount * 0.7 -> visualizationData.midLevel
                else -> visualizationData.trebleLevel
            }

            val animatedHeight = baseHeight * (0.5f + 0.5f * sin(animationTime * 0.01f + i * 0.3f))
            val barHeight = maxBarHeight * animatedHeight * visualizationData.volume

            // 颜色渐变
            val alpha = (200 * visualizationData.volume).toInt()
            val hue = (i.toFloat() / barCount * 60f) // 从红到黄的渐变
            progressPaint.color = Color.HSVToColor(alpha, floatArrayOf(hue, 0.8f, 1f))

            canvas.drawLine(x, startY, x, startY - barHeight, progressPaint)
        }
    }

    /**
     * 截断文本
     */
    private fun truncateText(text: String, maxLength: Int): String {
        return if (text.length > maxLength) {
            text.take(maxLength - 3) + "..."
        } else {
            text
        }
    }
}
