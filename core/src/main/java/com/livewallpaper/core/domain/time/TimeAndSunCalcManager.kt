package com.livewallpaper.core.domain.time

import com.livewallpaper.core.data.model.Location
import com.livewallpaper.core.utils.Logger
import dev.jamesyox.kastro.SolarEvent
import dev.jamesyox.kastro.SolarEventSequence
import dev.jamesyox.kastro.calculateSolarState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 时间和天文计算管理器
 * 负责计算日出日落时间，并将其转换为时间进度值
 */
@Singleton
class TimeAndSunCalcManager @Inject constructor() {
    
    /**
     * 计算当前时间进度
     * @param location 位置信息
     * @param currentTime 当前时间，默认为系统当前时间
     * @return 时间进度值 (0.0 = 午夜, 0.5 = 正午, 1.0 = 下一个午夜)
     */
    suspend fun calculateTimeProgress(
        location: Location,
        currentTime: Instant = Clock.System.now()
    ): TimeProgress {
        return try {
            val solarEvents = getSolarEventsForDay(location, currentTime)
            calculateProgressFromSolarEvents(solarEvents, currentTime)
        } catch (e: Exception) {
            Logger.e("计算时间进度失败", e)
            // 降级到基于时钟的计算
            calculateTimeProgressFromClock(currentTime, location.timezone)
        }
    }
    
    /**
     * 获取指定日期的太阳事件
     */
    private suspend fun getSolarEventsForDay(
        location: Location,
        time: Instant
    ): SolarEvents {
        val timeZone = TimeZone.of(location.timezone)
        val localDate = time.toLocalDateTime(timeZone).date
        val startOfDay = localDate.atStartOfDayIn(timeZone)
        val endOfDay = localDate.plus(1, DateTimeUnit.DAY).atStartOfDayIn(timeZone)
        
        val events = SolarEventSequence(
            start = startOfDay,
            latitude = location.latitude,
            longitude = location.longitude,
            requestedSolarEvents = listOf(
                SolarEvent.Sunrise,
                SolarEvent.Sunset,
                SolarEvent.Noon,
                SolarEvent.Nadir,
                SolarEvent.CivilTwilightDawn,
                SolarEvent.CivilTwilightDusk,
                SolarEvent.NauticalTwilightDawn,
                SolarEvent.NauticalTwilightDusk,
                SolarEvent.AstronomicalTwilightDawn,
                SolarEvent.AstronomicalTwilightDusk
            ),
            limit = kotlin.time.Duration.parse("24h")
        ).toList()
        
        return SolarEvents(
            sunrise = events.find { it.event == SolarEvent.Sunrise }?.time,
            sunset = events.find { it.event == SolarEvent.Sunset }?.time,
            noon = events.find { it.event == SolarEvent.Noon }?.time,
            nadir = events.find { it.event == SolarEvent.Nadir }?.time,
            civilTwilightDawn = events.find { it.event == SolarEvent.CivilTwilightDawn }?.time,
            civilTwilightDusk = events.find { it.event == SolarEvent.CivilTwilightDusk }?.time,
            nauticalTwilightDawn = events.find { it.event == SolarEvent.NauticalTwilightDawn }?.time,
            nauticalTwilightDusk = events.find { it.event == SolarEvent.NauticalTwilightDusk }?.time,
            astronomicalTwilightDawn = events.find { it.event == SolarEvent.AstronomicalTwilightDawn }?.time,
            astronomicalTwilightDusk = events.find { it.event == SolarEvent.AstronomicalTwilightDusk }?.time
        )
    }
    
    /**
     * 根据太阳事件计算时间进度
     */
    private fun calculateProgressFromSolarEvents(
        solarEvents: SolarEvents,
        currentTime: Instant
    ): TimeProgress {
        val sunrise = solarEvents.sunrise
        val sunset = solarEvents.sunset
        val noon = solarEvents.noon
        val nadir = solarEvents.nadir
        
        // 如果没有日出日落数据，降级到时钟计算
        if (sunrise == null || sunset == null || noon == null || nadir == null) {
            Logger.w("太阳事件数据不完整，使用时钟计算")
            return calculateTimeProgressFromClock(currentTime, "UTC")
        }
        
        val progress = when {
            // 从午夜到日出
            currentTime < sunrise -> {
                val totalDuration = sunrise - nadir
                val elapsed = currentTime - nadir
                if (totalDuration.inWholeMilliseconds > 0) {
                    (elapsed.inWholeMilliseconds.toDouble() / totalDuration.inWholeMilliseconds.toDouble()) * 0.25
                } else 0.0
            }
            // 从日出到正午
            currentTime < noon -> {
                val totalDuration = noon - sunrise
                val elapsed = currentTime - sunrise
                if (totalDuration.inWholeMilliseconds > 0) {
                    0.25 + (elapsed.inWholeMilliseconds.toDouble() / totalDuration.inWholeMilliseconds.toDouble()) * 0.25
                } else 0.25
            }
            // 从正午到日落
            currentTime < sunset -> {
                val totalDuration = sunset - noon
                val elapsed = currentTime - noon
                if (totalDuration.inWholeMilliseconds > 0) {
                    0.5 + (elapsed.inWholeMilliseconds.toDouble() / totalDuration.inWholeMilliseconds.toDouble()) * 0.25
                } else 0.5
            }
            // 从日落到午夜
            else -> {
                val nextNadir = nadir.plus(24, DateTimeUnit.HOUR)
                val totalDuration = nextNadir - sunset
                val elapsed = currentTime - sunset
                if (totalDuration.inWholeMilliseconds > 0) {
                    0.75 + (elapsed.inWholeMilliseconds.toDouble() / totalDuration.inWholeMilliseconds.toDouble()) * 0.25
                } else 0.75
            }
        }
        
        // 确保进度值在0.0-1.0范围内
        val clampedProgress = max(0.0, min(1.0, progress))
        
        return TimeProgress(
            progress = clampedProgress,
            phase = determineTimePhase(solarEvents, currentTime),
            solarEvents = solarEvents,
            isDay = currentTime in sunrise..sunset,
            sunElevation = calculateSunElevation(currentTime, solarEvents)
        )
    }
    
    /**
     * 基于时钟的时间进度计算（降级方案）
     */
    private fun calculateTimeProgressFromClock(
        currentTime: Instant,
        timezone: String
    ): TimeProgress {
        val timeZone = TimeZone.of(timezone)
        val localTime = currentTime.toLocalDateTime(timeZone).time
        
        val totalMinutes = localTime.hour * 60 + localTime.minute
        val progress = totalMinutes / (24.0 * 60.0)
        
        return TimeProgress(
            progress = progress,
            phase = TimePhase.UNKNOWN,
            solarEvents = null,
            isDay = localTime.hour in 6..18, // 简单的日夜判断
            sunElevation = 0.0
        )
    }
    
    /**
     * 确定当前时间阶段
     */
    private fun determineTimePhase(solarEvents: SolarEvents, currentTime: Instant): TimePhase {
        return when {
            solarEvents.astronomicalTwilightDawn != null && currentTime < solarEvents.astronomicalTwilightDawn -> TimePhase.NIGHT
            solarEvents.nauticalTwilightDawn != null && currentTime < solarEvents.nauticalTwilightDawn -> TimePhase.ASTRONOMICAL_TWILIGHT
            solarEvents.civilTwilightDawn != null && currentTime < solarEvents.civilTwilightDawn -> TimePhase.NAUTICAL_TWILIGHT
            solarEvents.sunrise != null && currentTime < solarEvents.sunrise -> TimePhase.CIVIL_TWILIGHT
            solarEvents.sunset != null && currentTime < solarEvents.sunset -> TimePhase.DAY
            solarEvents.civilTwilightDusk != null && currentTime < solarEvents.civilTwilightDusk -> TimePhase.CIVIL_TWILIGHT
            solarEvents.nauticalTwilightDusk != null && currentTime < solarEvents.nauticalTwilightDusk -> TimePhase.NAUTICAL_TWILIGHT
            solarEvents.astronomicalTwilightDusk != null && currentTime < solarEvents.astronomicalTwilightDusk -> TimePhase.ASTRONOMICAL_TWILIGHT
            else -> TimePhase.NIGHT
        }
    }
    
    /**
     * 计算太阳高度角（简化版本）
     */
    private fun calculateSunElevation(currentTime: Instant, solarEvents: SolarEvents): Double {
        val sunrise = solarEvents.sunrise
        val sunset = solarEvents.sunset
        val noon = solarEvents.noon
        
        if (sunrise == null || sunset == null || noon == null) return 0.0
        
        return when {
            currentTime < sunrise || currentTime > sunset -> -18.0 // 夜晚，太阳在地平线下
            currentTime == noon -> 90.0 // 正午，太阳在天顶
            currentTime < noon -> {
                // 上午，从0度到最高点
                val progress = (currentTime - sunrise).inWholeMilliseconds.toDouble() / 
                              (noon - sunrise).inWholeMilliseconds.toDouble()
                progress * 90.0
            }
            else -> {
                // 下午，从最高点到0度
                val progress = (currentTime - noon).inWholeMilliseconds.toDouble() / 
                              (sunset - noon).inWholeMilliseconds.toDouble()
                90.0 * (1.0 - progress)
            }
        }
    }
    
    /**
     * 获取时间进度的实时流
     */
    fun getTimeProgressFlow(location: Location): Flow<TimeProgress> = flow {
        while (true) {
            val progress = calculateTimeProgress(location)
            emit(progress)
            kotlinx.coroutines.delay(60_000) // 每分钟更新一次
        }
    }
}

/**
 * 时间进度数据类
 */
data class TimeProgress(
    val progress: Double, // 0.0 到 1.0
    val phase: TimePhase,
    val solarEvents: SolarEvents?,
    val isDay: Boolean,
    val sunElevation: Double // 太阳高度角，度数
)

/**
 * 太阳事件数据类
 */
data class SolarEvents(
    val sunrise: Instant?,
    val sunset: Instant?,
    val noon: Instant?,
    val nadir: Instant?,
    val civilTwilightDawn: Instant?,
    val civilTwilightDusk: Instant?,
    val nauticalTwilightDawn: Instant?,
    val nauticalTwilightDusk: Instant?,
    val astronomicalTwilightDawn: Instant?,
    val astronomicalTwilightDusk: Instant?
)

/**
 * 时间阶段枚举
 */
enum class TimePhase {
    NIGHT,                    // 夜晚
    ASTRONOMICAL_TWILIGHT,    // 天文暮光
    NAUTICAL_TWILIGHT,        // 航海暮光
    CIVIL_TWILIGHT,          // 民用暮光
    DAY,                     // 白天
    UNKNOWN                  // 未知（降级模式）
}
