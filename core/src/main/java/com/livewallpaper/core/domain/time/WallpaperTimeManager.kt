package com.livewallpaper.core.domain.time

import com.livewallpaper.core.data.model.Location
import com.livewallpaper.core.data.model.TimePhase
import com.livewallpaper.core.data.model.TimeProgress
import com.livewallpaper.core.domain.location.LocationManager
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 壁纸时间管理器
 * 整合时间计算和位置服务，为壁纸提供统一的时间状态
 */
@Singleton
class WallpaperTimeManager @Inject constructor(
    private val locationManager: LocationManager
) {

    private var cachedLocation: Location? = null
    private var lastLocationUpdate: Instant = Instant.DISTANT_PAST

    /**
     * 获取当前时间状态
     */
    suspend fun getCurrentTimeState(): WallpaperTimeState {
        return try {
            val location = getCurrentLocation()
            if (location != null) {
                val timeProgress = timeAndSunCalcManager.calculateTimeProgress(location)
                WallpaperTimeState.Success(
                    timeProgress = timeProgress,
                    location = location,
                    timestamp = Clock.System.now()
                )
            } else {
                WallpaperTimeState.Error("无法获取位置信息")
            }
        } catch (e: Exception) {
            Logger.e("获取时间状态失败", e)
            WallpaperTimeState.Error("获取时间状态失败: ${e.message}")
        }
    }

    /**
     * 获取时间状态流
     * 自动处理位置更新和时间计算
     */
    fun getTimeStateFlow(): Flow<WallpaperTimeState> = flow {
        emit(WallpaperTimeState.Loading)

        // 获取位置
        val locationResult = locationManager.getCurrentLocation()
        when (locationResult) {
            is Resource.Success -> {
                cachedLocation = locationResult.data
                lastLocationUpdate = Clock.System.now()

                // 计算时间进度
                val timeProgress = calculateTimeProgress(locationResult.data)
                emit(WallpaperTimeState.Success(
                    timeProgress = timeProgress,
                    location = locationResult.data,
                    timestamp = Clock.System.now()
                ))
            }
            is Resource.Error -> {
                emit(WallpaperTimeState.Error(locationResult.message ?: "位置获取失败"))
            }
            is Resource.Loading -> {
                emit(WallpaperTimeState.Loading)
            }
        }
    }

    /**
     * 获取当前位置（带缓存）
     */
    private suspend fun getCurrentLocation(): Location? {
        val now = Clock.System.now()

        // 如果缓存的位置还有效（30分钟内），直接使用
        if (cachedLocation != null &&
            (now - lastLocationUpdate).inWholeMinutes < 30) {
            return cachedLocation
        }

        // 获取新的位置
        val locationResult = locationManager.getCurrentLocation()
        return when (locationResult) {
            is Resource.Success -> {
                cachedLocation = locationResult.data
                lastLocationUpdate = now
                locationResult.data
            }
            else -> {
                Logger.w("位置获取失败，使用缓存位置")
                cachedLocation
            }
        }
    }

    /**
     * 强制刷新位置
     */
    suspend fun refreshLocation(): WallpaperTimeState {
        cachedLocation = null
        lastLocationUpdate = Instant.DISTANT_PAST
        return getCurrentTimeState()
    }

    /**
     * 获取简化的时间进度（仅数值）
     * 用于性能敏感的场景，如壁纸渲染
     */
    suspend fun getSimpleTimeProgress(): Double {
        return try {
            val location = getCurrentLocation()
            if (location != null) {
                calculateTimeProgress(location).progress
            } else {
                // 降级到基于时钟的简单计算
                val now = Clock.System.now()
                val localTime = now.toEpochMilliseconds() % (24 * 60 * 60 * 1000)
                localTime.toDouble() / (24 * 60 * 60 * 1000)
            }
        } catch (e: Exception) {
            Logger.e("获取简单时间进度失败", e)
            0.5 // 默认返回正午
        }
    }

    /**
     * 检查是否为白天
     */
    suspend fun isDaytime(): Boolean {
        return try {
            val location = getCurrentLocation()
            if (location != null) {
                calculateTimeProgress(location).isDay
            } else {
                // 简单的时间判断
                val hour = Clock.System.now().toEpochMilliseconds() / (60 * 60 * 1000) % 24
                hour in 6..18
            }
        } catch (e: Exception) {
            Logger.e("检查白天状态失败", e)
            true
        }
    }

    /**
     * 获取当前时间阶段
     */
    suspend fun getCurrentTimePhase(): TimePhase {
        return try {
            val location = getCurrentLocation()
            if (location != null) {
                calculateTimeProgress(location).phase
            } else {
                TimePhase.UNKNOWN
            }
        } catch (e: Exception) {
            Logger.e("获取时间阶段失败", e)
            TimePhase.UNKNOWN
        }
    }

    /**
     * 计算时间进度（简化版本）
     */
    private fun calculateTimeProgress(location: Location): TimeProgress {
        val now = Clock.System.now()
        val localDateTime = now.toLocalDateTime(TimeZone.currentSystemDefault())
        val hour = localDateTime.hour
        val minute = localDateTime.minute

        // 简化的时间进度计算
        val timeInMinutes = hour * 60 + minute
        val progress = timeInMinutes.toDouble() / (24 * 60)

        // 简化的日夜判断
        val isDay = hour in 6..18

        // 简化的时间阶段判断
        val phase = when (hour) {
            in 0..5 -> TimePhase.NIGHT
            in 6..8 -> TimePhase.DAWN
            in 9..16 -> TimePhase.DAY
            in 17..19 -> TimePhase.DUSK
            else -> TimePhase.NIGHT
        }

        return TimeProgress(
            progress = progress,
            isDay = isDay,
            phase = phase,
            sunriseTime = 6.0,
            sunsetTime = 18.0,
            timestamp = now
        )
    }
}

/**
 * 壁纸时间状态密封类
 */
sealed class WallpaperTimeState {
    object Loading : WallpaperTimeState()

    data class Success(
        val timeProgress: TimeProgress,
        val location: Location,
        val timestamp: Instant
    ) : WallpaperTimeState()

    data class Error(val message: String) : WallpaperTimeState()
}

/**
 * 时间状态扩展函数
 */
fun WallpaperTimeState.getProgressOrDefault(default: Double = 0.5): Double {
    return when (this) {
        is WallpaperTimeState.Success -> timeProgress.progress
        else -> default
    }
}

fun WallpaperTimeState.isDayOrDefault(default: Boolean = true): Boolean {
    return when (this) {
        is WallpaperTimeState.Success -> timeProgress.isDay
        else -> default
    }
}

fun WallpaperTimeState.getPhaseOrDefault(default: TimePhase = TimePhase.DAY): TimePhase {
    return when (this) {
        is WallpaperTimeState.Success -> timeProgress.phase
        else -> default
    }
}
