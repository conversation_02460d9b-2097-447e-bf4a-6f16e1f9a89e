package com.livewallpaper.core.domain.weather

import com.livewallpaper.core.data.model.Weather
import com.livewallpaper.core.data.model.WeatherForecast
import com.livewallpaper.core.data.model.WeatherType
import com.livewallpaper.core.data.repository.WeatherRepository
import com.livewallpaper.core.domain.location.LocationManager
import com.livewallpaper.core.utils.Logger
import com.livewallpaper.core.utils.Resource
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 天气管理器
 * 负责天气数据的获取、处理和状态管理
 */
@Singleton
class WeatherManager @Inject constructor(
    private val weatherRepository: WeatherRepository,
    private val locationManager: LocationManager
) {
    
    private var lastWeatherUpdate = Instant.DISTANT_PAST
    private var cachedWeather: Weather? = null
    
    /**
     * 获取当前天气状态
     */
    suspend fun getCurrentWeatherState(): WeatherState {
        return try {
            val location = locationManager.getCurrentLocation()
            when (location) {
                is Resource.Success -> {
                    val weatherResult = weatherRepository.getCurrentWeather(
                        latitude = location.data.latitude,
                        longitude = location.data.longitude
                    )
                    
                    when (weatherResult) {
                        is Resource.Success -> {
                            cachedWeather = weatherResult.data
                            lastWeatherUpdate = Clock.System.now()
                            WeatherState.Success(
                                weather = weatherResult.data,
                                location = location.data,
                                timestamp = Clock.System.now()
                            )
                        }
                        is Resource.Error -> {
                            WeatherState.Error(weatherResult.message ?: "获取天气数据失败")
                        }
                        is Resource.Loading -> {
                            WeatherState.Loading
                        }
                    }
                }
                is Resource.Error -> {
                    WeatherState.Error("位置获取失败: ${location.message}")
                }
                is Resource.Loading -> {
                    WeatherState.Loading
                }
            }
        } catch (e: Exception) {
            Logger.e("获取天气状态失败", e)
            WeatherState.Error("天气状态获取失败: ${e.message}")
        }
    }
    
    /**
     * 获取天气状态流
     */
    fun getWeatherStateFlow(): Flow<WeatherState> = flow {
        emit(WeatherState.Loading)
        
        // 获取位置
        val locationResult = locationManager.getCurrentLocation()
        when (locationResult) {
            is Resource.Success -> {
                val location = locationResult.data
                
                // 获取天气数据流
                weatherRepository.getWeatherFlow(location.latitude, location.longitude)
                    .catch { e ->
                        Logger.e("天气数据流错误", e)
                        emit(WeatherState.Error("天气数据获取失败: ${e.message}"))
                    }
                    .collect { weatherResource ->
                        when (weatherResource) {
                            is Resource.Success -> {
                                cachedWeather = weatherResource.data
                                lastWeatherUpdate = Clock.System.now()
                                emit(WeatherState.Success(
                                    weather = weatherResource.data,
                                    location = location,
                                    timestamp = Clock.System.now()
                                ))
                            }
                            is Resource.Error -> {
                                emit(WeatherState.Error(weatherResource.message ?: "天气数据获取失败"))
                            }
                            is Resource.Loading -> {
                                emit(WeatherState.Loading)
                            }
                        }
                    }
            }
            is Resource.Error -> {
                emit(WeatherState.Error("位置获取失败: ${locationResult.message}"))
            }
            is Resource.Loading -> {
                emit(WeatherState.Loading)
            }
        }
    }
    
    /**
     * 获取天气预报
     */
    suspend fun getWeatherForecast(): Resource<List<WeatherForecast>> {
        return try {
            val location = locationManager.getCurrentLocation()
            when (location) {
                is Resource.Success -> {
                    weatherRepository.getWeatherForecast(
                        latitude = location.data.latitude,
                        longitude = location.data.longitude
                    )
                }
                is Resource.Error -> {
                    Resource.Error("位置获取失败: ${location.message}")
                }
                is Resource.Loading -> {
                    Resource.Loading()
                }
            }
        } catch (e: Exception) {
            Logger.e("获取天气预报失败", e)
            Resource.Error("天气预报获取失败: ${e.message}")
        }
    }
    
    /**
     * 强制刷新天气数据
     */
    suspend fun refreshWeather(): WeatherState {
        return try {
            val location = locationManager.getCurrentLocation()
            when (location) {
                is Resource.Success -> {
                    val weatherResult = weatherRepository.getCurrentWeather(
                        latitude = location.data.latitude,
                        longitude = location.data.longitude,
                        forceRefresh = true
                    )
                    
                    when (weatherResult) {
                        is Resource.Success -> {
                            cachedWeather = weatherResult.data
                            lastWeatherUpdate = Clock.System.now()
                            WeatherState.Success(
                                weather = weatherResult.data,
                                location = location.data,
                                timestamp = Clock.System.now()
                            )
                        }
                        is Resource.Error -> {
                            WeatherState.Error(weatherResult.message ?: "刷新天气数据失败")
                        }
                        is Resource.Loading -> {
                            WeatherState.Loading
                        }
                    }
                }
                is Resource.Error -> {
                    WeatherState.Error("位置获取失败: ${location.message}")
                }
                is Resource.Loading -> {
                    WeatherState.Loading
                }
            }
        } catch (e: Exception) {
            Logger.e("刷新天气数据失败", e)
            WeatherState.Error("刷新失败: ${e.message}")
        }
    }
    
    /**
     * 获取当前天气类型（用于场景选择）
     */
    suspend fun getCurrentWeatherType(): WeatherType {
        return try {
            cachedWeather?.weatherType ?: run {
                val weatherState = getCurrentWeatherState()
                when (weatherState) {
                    is WeatherState.Success -> weatherState.weather.weatherType
                    else -> WeatherType.CLEAR // 默认晴朗
                }
            }
        } catch (e: Exception) {
            Logger.e("获取天气类型失败", e)
            WeatherType.CLEAR
        }
    }
    
    /**
     * 检查是否需要更新天气数据
     */
    fun shouldUpdateWeather(): Boolean {
        val now = Clock.System.now()
        val timeSinceLastUpdate = now - lastWeatherUpdate
        return timeSinceLastUpdate.inWholeMinutes >= 30 // 30分钟更新一次
    }
    
    /**
     * 获取天气描述文本
     */
    fun getWeatherDescription(weather: Weather): String {
        return when (weather.weatherType) {
            WeatherType.CLEAR -> "晴朗"
            WeatherType.CLOUDY -> "多云"
            WeatherType.RAINY -> "雨天"
            WeatherType.SNOWY -> "雪天"
            WeatherType.FOGGY -> "雾天"
            WeatherType.STORMY -> "暴风雨"
            WeatherType.WINDY -> "大风"
        }
    }
    
    /**
     * 获取天气图标资源ID
     */
    fun getWeatherIconResource(weather: Weather): String {
        return when (weather.weatherType) {
            WeatherType.CLEAR -> "☀️"
            WeatherType.CLOUDY -> "☁️"
            WeatherType.RAINY -> "🌧️"
            WeatherType.SNOWY -> "❄️"
            WeatherType.FOGGY -> "🌫️"
            WeatherType.STORMY -> "⛈️"
            WeatherType.WINDY -> "💨"
        }
    }
    
    /**
     * 检查API密钥是否已配置
     */
    suspend fun isApiKeyConfigured(): Boolean {
        return weatherRepository.isApiKeyConfigured()
    }
    
    /**
     * 设置API密钥
     */
    suspend fun setApiKey(apiKey: String) {
        weatherRepository.setApiKey(apiKey)
    }
}

/**
 * 天气状态密封类
 */
sealed class WeatherState {
    object Loading : WeatherState()
    
    data class Success(
        val weather: Weather,
        val location: com.livewallpaper.core.data.model.Location,
        val timestamp: Instant
    ) : WeatherState()
    
    data class Error(val message: String) : WeatherState()
}

/**
 * 天气状态扩展函数
 */
fun WeatherState.getWeatherOrNull(): Weather? {
    return when (this) {
        is WeatherState.Success -> weather
        else -> null
    }
}

fun WeatherState.getWeatherTypeOrDefault(default: WeatherType = WeatherType.CLEAR): WeatherType {
    return when (this) {
        is WeatherState.Success -> weather.weatherType
        else -> default
    }
}
