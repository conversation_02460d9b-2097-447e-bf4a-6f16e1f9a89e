package com.livewallpaper.core.domain.weather

import android.graphics.*
import com.livewallpaper.core.data.model.Weather
import com.livewallpaper.core.data.model.WeatherType
import com.livewallpaper.core.utils.Logger
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*
import kotlin.random.Random

/**
 * 天气效果渲染器
 * 负责渲染各种天气相关的视觉效果
 */
@Singleton
class WeatherEffectRenderer @Inject constructor() {
    
    private val rainDrops = mutableListOf<RainDrop>()
    private val snowFlakes = mutableListOf<SnowFlake>()
    private val fogParticles = mutableListOf<FogParticle>()
    private val lightningFlashes = mutableListOf<LightningFlash>()
    
    private val rainPaint = Paint().apply {
        color = Color.argb(180, 100, 150, 255)
        strokeWidth = 2f
        isAntiAlias = true
    }
    
    private val snowPaint = Paint().apply {
        color = Color.argb(200, 255, 255, 255)
        isAntiAlias = true
    }
    
    private val fogPaint = Paint().apply {
        color = Color.argb(60, 200, 200, 200)
        isAntiAlias = true
    }
    
    private val lightningPaint = Paint().apply {
        color = Color.argb(255, 255, 255, 150)
        strokeWidth = 3f
        isAntiAlias = true
    }
    
    /**
     * 渲染天气效果
     */
    fun renderWeatherEffects(
        canvas: Canvas,
        weather: Weather?,
        animationTime: Float,
        width: Float,
        height: Float
    ) {
        if (weather == null) return
        
        try {
            when (weather.weatherType) {
                WeatherType.RAINY -> renderRainEffect(canvas, animationTime, width, height, weather.windSpeed.toFloat())
                WeatherType.SNOWY -> renderSnowEffect(canvas, animationTime, width, height, weather.windSpeed.toFloat())
                WeatherType.FOGGY -> renderFogEffect(canvas, animationTime, width, height)
                WeatherType.STORMY -> {
                    renderRainEffect(canvas, animationTime, width, height, weather.windSpeed.toFloat() * 1.5f)
                    renderLightningEffect(canvas, animationTime, width, height)
                }
                WeatherType.CLOUDY -> renderCloudShadows(canvas, animationTime, width, height)
                WeatherType.WINDY -> renderWindEffect(canvas, animationTime, width, height, weather.windSpeed.toFloat())
                else -> {} // 晴朗天气不需要特殊效果
            }
        } catch (e: Exception) {
            Logger.e("渲染天气效果失败", e)
        }
    }
    
    /**
     * 渲染雨效果
     */
    private fun renderRainEffect(canvas: Canvas, animationTime: Float, width: Float, height: Float, windSpeed: Float) {
        // 更新雨滴
        updateRainDrops(width, height, windSpeed)
        
        // 绘制雨滴
        rainDrops.forEach { drop ->
            val startX = drop.x
            val startY = drop.y
            val endX = drop.x + drop.length * sin(Math.toRadians(drop.angle.toDouble())).toFloat()
            val endY = drop.y + drop.length * cos(Math.toRadians(drop.angle.toDouble())).toFloat()
            
            rainPaint.alpha = (drop.alpha * 255).toInt()
            canvas.drawLine(startX, startY, endX, endY, rainPaint)
        }
    }
    
    /**
     * 渲染雪效果
     */
    private fun renderSnowEffect(canvas: Canvas, animationTime: Float, width: Float, height: Float, windSpeed: Float) {
        // 更新雪花
        updateSnowFlakes(width, height, windSpeed)
        
        // 绘制雪花
        snowFlakes.forEach { flake ->
            snowPaint.alpha = (flake.alpha * 255).toInt()
            canvas.drawCircle(flake.x, flake.y, flake.size, snowPaint)
            
            // 绘制雪花的简单形状
            val armLength = flake.size * 1.5f
            for (i in 0 until 6) {
                val angle = i * 60f
                val armX = armLength * cos(Math.toRadians(angle.toDouble())).toFloat()
                val armY = armLength * sin(Math.toRadians(angle.toDouble())).toFloat()
                canvas.drawLine(
                    flake.x, flake.y,
                    flake.x + armX, flake.y + armY,
                    snowPaint
                )
            }
        }
    }
    
    /**
     * 渲染雾效果
     */
    private fun renderFogEffect(canvas: Canvas, animationTime: Float, width: Float, height: Float) {
        // 更新雾粒子
        updateFogParticles(width, height)
        
        // 绘制雾效果
        fogParticles.forEach { particle ->
            fogPaint.alpha = (particle.alpha * 255).toInt()
            canvas.drawCircle(particle.x, particle.y, particle.size, fogPaint)
        }
        
        // 添加整体雾气覆盖层
        val fogOverlay = Paint().apply {
            color = Color.argb(30, 200, 200, 200)
        }
        canvas.drawRect(0f, 0f, width, height, fogOverlay)
    }
    
    /**
     * 渲染闪电效果
     */
    private fun renderLightningEffect(canvas: Canvas, animationTime: Float, width: Float, height: Float) {
        // 随机生成闪电
        if (Random.nextFloat() < 0.002f) { // 0.2%的概率
            lightningFlashes.add(LightningFlash(
                x = Random.nextFloat() * width,
                y = Random.nextFloat() * height * 0.3f,
                duration = 0.2f,
                startTime = animationTime
            ))
        }
        
        // 更新和绘制闪电
        lightningFlashes.removeAll { flash ->
            val elapsed = animationTime - flash.startTime
            if (elapsed > flash.duration) {
                true
            } else {
                val alpha = 1f - (elapsed / flash.duration)
                lightningPaint.alpha = (alpha * 255).toInt()
                
                // 绘制闪电路径
                val path = Path()
                path.moveTo(flash.x, flash.y)
                var currentX = flash.x
                var currentY = flash.y
                
                for (i in 0 until 5) {
                    currentX += (Random.nextFloat() - 0.5f) * 100f
                    currentY += height * 0.15f + Random.nextFloat() * 50f
                    path.lineTo(currentX, currentY)
                }
                
                canvas.drawPath(path, lightningPaint)
                
                // 闪电时的屏幕闪光效果
                if (elapsed < flash.duration * 0.3f) {
                    val flashOverlay = Paint().apply {
                        color = Color.argb((alpha * 50).toInt(), 255, 255, 255)
                    }
                    canvas.drawRect(0f, 0f, width, height, flashOverlay)
                }
                
                false
            }
        }
    }
    
    /**
     * 渲染云影效果
     */
    private fun renderCloudShadows(canvas: Canvas, animationTime: Float, width: Float, height: Float) {
        val shadowPaint = Paint().apply {
            color = Color.argb(40, 0, 0, 0)
            isAntiAlias = true
        }
        
        // 移动的云影
        for (i in 0 until 3) {
            val x = (animationTime * 20f + i * width / 3f) % (width + 200f) - 100f
            val y = height * (0.2f + i * 0.1f)
            val shadowWidth = 150f + i * 50f
            val shadowHeight = 80f + i * 20f
            
            canvas.drawOval(x, y, x + shadowWidth, y + shadowHeight, shadowPaint)
        }
    }
    
    /**
     * 渲染风效果
     */
    private fun renderWindEffect(canvas: Canvas, animationTime: Float, width: Float, height: Float, windSpeed: Float) {
        val windPaint = Paint().apply {
            color = Color.argb(60, 255, 255, 255)
            strokeWidth = 1f
            isAntiAlias = true
        }
        
        // 风线效果
        for (i in 0 until 20) {
            val x = (animationTime * windSpeed * 2f + i * 50f) % (width + 100f) - 50f
            val y = Random.nextFloat() * height
            val lineLength = 30f + windSpeed * 2f
            
            windPaint.alpha = (60 + windSpeed * 2).toInt().coerceAtMost(120)
            canvas.drawLine(x, y, x + lineLength, y, windPaint)
        }
    }
    
    /**
     * 更新雨滴
     */
    private fun updateRainDrops(width: Float, height: Float, windSpeed: Float) {
        // 移除超出屏幕的雨滴
        rainDrops.removeAll { it.y > height + 50f }
        
        // 添加新雨滴
        while (rainDrops.size < 100) {
            rainDrops.add(RainDrop(
                x = Random.nextFloat() * (width + 100f) - 50f,
                y = -Random.nextFloat() * 100f,
                speed = 8f + Random.nextFloat() * 12f,
                length = 15f + Random.nextFloat() * 25f,
                angle = windSpeed * 2f,
                alpha = 0.6f + Random.nextFloat() * 0.4f
            ))
        }
        
        // 更新雨滴位置
        rainDrops.forEach { drop ->
            drop.y += drop.speed
            drop.x += windSpeed * 0.5f
        }
    }
    
    /**
     * 更新雪花
     */
    private fun updateSnowFlakes(width: Float, height: Float, windSpeed: Float) {
        // 移除超出屏幕的雪花
        snowFlakes.removeAll { it.y > height + 20f }
        
        // 添加新雪花
        while (snowFlakes.size < 50) {
            snowFlakes.add(SnowFlake(
                x = Random.nextFloat() * (width + 100f) - 50f,
                y = -Random.nextFloat() * 50f,
                speed = 2f + Random.nextFloat() * 4f,
                size = 3f + Random.nextFloat() * 7f,
                alpha = 0.7f + Random.nextFloat() * 0.3f,
                swayAmount = 20f + Random.nextFloat() * 30f,
                swaySpeed = 0.02f + Random.nextFloat() * 0.03f
            ))
        }
        
        // 更新雪花位置
        snowFlakes.forEach { flake ->
            flake.y += flake.speed
            flake.x += sin(flake.y * flake.swaySpeed) * flake.swayAmount * 0.1f + windSpeed * 0.3f
        }
    }
    
    /**
     * 更新雾粒子
     */
    private fun updateFogParticles(width: Float, height: Float) {
        // 移除超出屏幕的粒子
        fogParticles.removeAll { it.x > width + 50f }
        
        // 添加新粒子
        while (fogParticles.size < 30) {
            fogParticles.add(FogParticle(
                x = -Random.nextFloat() * 100f,
                y = Random.nextFloat() * height,
                speed = 1f + Random.nextFloat() * 2f,
                size = 20f + Random.nextFloat() * 40f,
                alpha = 0.3f + Random.nextFloat() * 0.4f
            ))
        }
        
        // 更新粒子位置
        fogParticles.forEach { particle ->
            particle.x += particle.speed
        }
    }
}

// 数据类定义
data class RainDrop(
    var x: Float,
    var y: Float,
    val speed: Float,
    val length: Float,
    val angle: Float,
    val alpha: Float
)

data class SnowFlake(
    var x: Float,
    var y: Float,
    val speed: Float,
    val size: Float,
    val alpha: Float,
    val swayAmount: Float,
    val swaySpeed: Float
)

data class FogParticle(
    var x: Float,
    var y: Float,
    val speed: Float,
    val size: Float,
    val alpha: Float
)

data class LightningFlash(
    val x: Float,
    val y: Float,
    val duration: Float,
    val startTime: Float
)
