package com.livewallpaper.core.service

import android.app.Notification
import android.content.Intent
import android.graphics.Bitmap
// import android.graphics.drawable.BitmapDrawable
import android.media.MediaMetadata
import android.media.session.MediaController
import android.media.session.MediaSession
import android.media.session.PlaybackState
import android.os.Bundle
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import com.livewallpaper.core.data.model.MusicAppInfo
import com.livewallpaper.core.data.model.MusicNotificationData
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import android.graphics.drawable.BitmapDrawable
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音乐通知监听服务
 * 监听系统通知，提取音乐播放信息
 */
@AndroidEntryPoint
class MusicNotificationListenerService : NotificationListenerService() {

    @Inject
    lateinit var musicDataBroadcaster: MusicDataBroadcaster

    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeMediaControllers = mutableMapOf<String, MediaController>()

    override fun onCreate() {
        super.onCreate()
        Logger.d("MusicNotificationListenerService created")
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.d("MusicNotificationListenerService destroyed")
        serviceScope.cancel()
        cleanupMediaControllers()
    }

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        super.onNotificationPosted(sbn)

        serviceScope.launch {
            try {
                handleNotification(sbn)
            } catch (e: Exception) {
                Logger.e("Error handling notification", e)
            }
        }
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        super.onNotificationRemoved(sbn)

        serviceScope.launch {
            try {
                handleNotificationRemoved(sbn)
            } catch (e: Exception) {
                Logger.e("Error handling notification removal", e)
            }
        }
    }

    /**
     * 处理通知
     */
    private suspend fun handleNotification(sbn: StatusBarNotification) {
        val packageName = sbn.packageName

        // 检查是否为支持的音乐应用
        if (!MusicAppInfo.isSupportedMusicApp(packageName)) {
            return
        }

        val notification = sbn.notification
        if (notification == null) {
            Logger.w("Notification is null for package: $packageName")
            return
        }

        // 检查是否为媒体通知
        if (!isMediaNotification(notification)) {
            return
        }

        Logger.d("Processing music notification from: $packageName")

        // 尝试从MediaSession获取信息
        val mediaSessionData = extractMediaSessionData(notification, packageName)
        if (mediaSessionData != null) {
            musicDataBroadcaster.broadcastMusicData(mediaSessionData)
            return
        }

        // 降级到通知内容解析
        val notificationData = extractNotificationData(notification, packageName)
        if (notificationData != null && notificationData.isValid()) {
            musicDataBroadcaster.broadcastMusicData(notificationData)
        }
    }

    /**
     * 处理通知移除
     */
    private suspend fun handleNotificationRemoved(sbn: StatusBarNotification) {
        val packageName = sbn.packageName

        if (MusicAppInfo.isSupportedMusicApp(packageName)) {
            // 清理对应的MediaController
            activeMediaControllers.remove(packageName)?.let { controller ->
                try {
                    mediaControllerCallbacks[packageName]?.let { callback ->
                        controller.unregisterCallback(callback)
                    }
                    mediaControllerCallbacks.remove(packageName)
                } catch (e: Exception) {
                    Logger.e("Error unregistering media controller callback", e)
                }
            }

            // 广播音乐停止事件
            val stopData = MusicNotificationData(
                title = null,
                artist = null,
                album = null,
                albumArt = null,
                isPlaying = false,
                packageName = packageName
            )
            musicDataBroadcaster.broadcastMusicData(stopData)
        }
    }

    /**
     * 检查是否为媒体通知
     */
    private fun isMediaNotification(notification: Notification): Boolean {
        return notification.extras?.containsKey(Notification.EXTRA_MEDIA_SESSION) == true ||
               notification.category == Notification.CATEGORY_TRANSPORT ||
               notification.actions?.any { action ->
                   action.title?.toString()?.lowercase()?.let { title ->
                       title.contains("play") || title.contains("pause") ||
                       title.contains("next") || title.contains("previous") ||
                       title.contains("播放") || title.contains("暂停") ||
                       title.contains("下一首") || title.contains("上一首")
                   } == true
               } == true
    }

    /**
     * 从MediaSession提取数据
     */
    private suspend fun extractMediaSessionData(
        notification: Notification,
        packageName: String
    ): MusicNotificationData? {
        return try {
            val mediaSessionToken = notification.extras?.getParcelable<MediaSession.Token>(
                Notification.EXTRA_MEDIA_SESSION
            )

            if (mediaSessionToken != null) {
                val mediaController = MediaController(this, mediaSessionToken)
                activeMediaControllers[packageName] = mediaController

                // 注册回调监听播放状态变化
                registerMediaControllerCallback(mediaController, packageName)

                val metadata = mediaController.metadata
                val playbackState = mediaController.playbackState

                if (metadata != null) {
                    val albumArt = metadata.getBitmap(MediaMetadata.METADATA_KEY_ALBUM_ART)
                        ?: metadata.getBitmap(MediaMetadata.METADATA_KEY_ART)

                    MusicNotificationData(
                        title = metadata.getString(MediaMetadata.METADATA_KEY_TITLE),
                        artist = metadata.getString(MediaMetadata.METADATA_KEY_ARTIST),
                        album = metadata.getString(MediaMetadata.METADATA_KEY_ALBUM),
                        albumArt = albumArt,
                        isPlaying = playbackState?.state == PlaybackState.STATE_PLAYING,
                        packageName = packageName
                    )
                } else {
                    null
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.e("Error extracting MediaSession data", e)
            null
        }
    }

    private val mediaControllerCallbacks = mutableMapOf<String, MediaController.Callback>()

    /**
     * 注册MediaController回调
     */
    private fun registerMediaControllerCallback(controller: MediaController, packageName: String) {
        val callback = object : MediaController.Callback() {
            override fun onMetadataChanged(metadata: MediaMetadata?) {
                super.onMetadataChanged(metadata)
                serviceScope.launch {
                    handleMediaMetadataChanged(metadata, packageName, controller.playbackState)
                }
            }

            override fun onPlaybackStateChanged(state: PlaybackState?) {
                super.onPlaybackStateChanged(state)
                serviceScope.launch {
                    handlePlaybackStateChanged(state, packageName, controller.metadata)
                }
            }
        }

        mediaControllerCallbacks[packageName] = callback
        controller.registerCallback(callback)
    }

    /**
     * 处理媒体元数据变化
     */
    private suspend fun handleMediaMetadataChanged(
        metadata: MediaMetadata?,
        packageName: String,
        playbackState: PlaybackState?
    ) {
        if (metadata != null) {
            val albumArt = metadata.getBitmap(MediaMetadata.METADATA_KEY_ALBUM_ART)
                ?: metadata.getBitmap(MediaMetadata.METADATA_KEY_ART)

            val musicData = MusicNotificationData(
                title = metadata.getString(MediaMetadata.METADATA_KEY_TITLE),
                artist = metadata.getString(MediaMetadata.METADATA_KEY_ARTIST),
                album = metadata.getString(MediaMetadata.METADATA_KEY_ALBUM),
                albumArt = albumArt,
                isPlaying = playbackState?.state == PlaybackState.STATE_PLAYING,
                packageName = packageName
            )

            if (musicData.isValid()) {
                musicDataBroadcaster.broadcastMusicData(musicData)
            }
        }
    }

    /**
     * 处理播放状态变化
     */
    private suspend fun handlePlaybackStateChanged(
        state: PlaybackState?,
        packageName: String,
        metadata: MediaMetadata?
    ) {
        if (metadata != null) {
            val albumArt = metadata.getBitmap(MediaMetadata.METADATA_KEY_ALBUM_ART)
                ?: metadata.getBitmap(MediaMetadata.METADATA_KEY_ART)

            val musicData = MusicNotificationData(
                title = metadata.getString(MediaMetadata.METADATA_KEY_TITLE),
                artist = metadata.getString(MediaMetadata.METADATA_KEY_ARTIST),
                album = metadata.getString(MediaMetadata.METADATA_KEY_ALBUM),
                albumArt = albumArt,
                isPlaying = state?.state == PlaybackState.STATE_PLAYING,
                packageName = packageName
            )

            if (musicData.isValid()) {
                musicDataBroadcaster.broadcastMusicData(musicData)
            }
        }
    }

    /**
     * 从通知内容提取数据（降级方案）
     */
    private fun extractNotificationData(
        notification: Notification,
        packageName: String
    ): MusicNotificationData? {
        return try {
            val extras = notification.extras
            val title = extras?.getCharSequence(Notification.EXTRA_TITLE)?.toString()
            val text = extras?.getCharSequence(Notification.EXTRA_TEXT)?.toString()
            val subText = extras?.getCharSequence(Notification.EXTRA_SUB_TEXT)?.toString()

            // 尝试从大图标获取专辑封面
            val albumArt = notification.getLargeIcon()?.let { icon ->
                try {
                    (icon.loadDrawable(this) as? android.graphics.drawable.BitmapDrawable)?.bitmap
                } catch (e: Exception) {
                    null
                }
            }

            // 简单的播放状态检测
            val isPlaying = notification.actions?.any { action ->
                action.title?.toString()?.lowercase()?.let { actionTitle ->
                    actionTitle.contains("pause") || actionTitle.contains("暂停")
                } == true
            } ?: false

            MusicNotificationData(
                title = title,
                artist = text ?: subText,
                album = null,
                albumArt = albumArt,
                isPlaying = isPlaying,
                packageName = packageName
            )
        } catch (e: Exception) {
            Logger.e("Error extracting notification data", e)
            null
        }
    }

    /**
     * 清理MediaController
     */
    private fun cleanupMediaControllers() {
        activeMediaControllers.values.forEach { controller ->
            try {
                val packageName = activeMediaControllers.entries.find { it.value == controller }?.key
                packageName?.let { pkg ->
                    mediaControllerCallbacks[pkg]?.let { callback ->
                        controller.unregisterCallback(callback)
                    }
                }
            } catch (e: Exception) {
                Logger.e("Error cleaning up media controller", e)
            }
        }
        activeMediaControllers.clear()
        mediaControllerCallbacks.clear()
    }
}

/**
 * 音乐数据广播器
 * 负责将音乐数据广播给其他组件
 */
@Singleton
class MusicDataBroadcaster @Inject constructor() {

    private val _musicDataFlow = MutableSharedFlow<MusicNotificationData>(
        replay = 1,
        extraBufferCapacity = 10
    )
    val musicDataFlow: SharedFlow<MusicNotificationData> = _musicDataFlow.asSharedFlow()

    /**
     * 广播音乐数据
     */
    suspend fun broadcastMusicData(data: MusicNotificationData) {
        try {
            _musicDataFlow.emit(data)
            Logger.d("Music data broadcasted: ${data.title} by ${data.artist}")
        } catch (e: Exception) {
            Logger.e("Error broadcasting music data", e)
        }
    }
}
