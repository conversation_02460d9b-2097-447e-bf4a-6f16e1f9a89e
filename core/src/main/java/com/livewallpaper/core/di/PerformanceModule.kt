package com.livewallpaper.core.di

import android.content.Context
import com.livewallpaper.core.data.repository.SettingsRepository
import com.livewallpaper.core.performance.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 性能系统依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object PerformanceModule {
    
    @Provides
    @Singleton
    fun providePerformanceMonitor(
        @ApplicationContext context: Context
    ): PerformanceMonitor {
        return PerformanceMonitor(context)
    }
    
    @Provides
    @Singleton
    fun provideObjectPoolManager(): ObjectPoolManager {
        return ObjectPoolManager()
    }
    
    @Provides
    @Singleton
    fun provideDirtyRegionManager(
        objectPoolManager: ObjectPoolManager
    ): DirtyRegionManager {
        return DirtyRegionManager(objectPoolManager)
    }
    
    @Provides
    @Singleton
    fun providePerformanceOptimizer(
        @ApplicationContext context: Context,
        performanceMonitor: PerformanceMonitor,
        settingsRepository: SettingsRepository
    ): PerformanceOptimizer {
        return PerformanceOptimizer(context, performanceMonitor, settingsRepository)
    }
}
