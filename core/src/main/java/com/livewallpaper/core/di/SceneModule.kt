package com.livewallpaper.core.di

import android.content.Context
import com.livewallpaper.core.data.preferences.WallpaperPreferences
import com.livewallpaper.core.data.repository.SceneRepository
import com.livewallpaper.core.domain.scene.SceneInitializer
import com.livewallpaper.core.domain.scene.SceneLoader
import com.livewallpaper.core.domain.scene.SceneManager
import com.livewallpaper.core.domain.scene.SceneRenderer
import com.livewallpaper.core.domain.time.WallpaperTimeManager
import com.livewallpaper.core.domain.weather.WeatherManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 场景管理依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object SceneModule {

    @Provides
    @Singleton
    fun provideSceneLoader(
        @ApplicationContext context: Context
    ): SceneLoader {
        return SceneLoader(context)
    }

    @Provides
    @Singleton
    fun provideSceneRenderer(): SceneRenderer {
        return SceneRenderer()
    }

    @Provides
    @Singleton
    fun provideSceneManager(
        sceneRepository: SceneRepository,
        sceneLoader: SceneLoader,
        timeManager: WallpaperTimeManager,
        weatherManager: WeatherManager,
        preferences: WallpaperPreferences
    ): SceneManager {
        return SceneManager(sceneRepository, sceneLoader, timeManager, weatherManager, preferences)
    }

    @Provides
    @Singleton
    fun provideSceneInitializer(
        sceneRepository: SceneRepository
    ): SceneInitializer {
        return SceneInitializer(sceneRepository)
    }
}
