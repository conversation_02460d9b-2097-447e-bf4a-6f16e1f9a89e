package com.livewallpaper.core.di

import android.content.Context
import com.livewallpaper.core.domain.ads.AdManager
import com.livewallpaper.core.domain.billing.BillingManager
import com.livewallpaper.core.domain.scene.CustomSceneManager
import com.livewallpaper.core.domain.update.AppUpdateChecker
import com.livewallpaper.core.data.repository.SceneRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Singleton

/**
 * 商业化功能依赖注入模块
 * 提供计费、广告、自定义场景和应用更新相关的依赖
 */
@Module
@InstallIn(SingletonComponent::class)
object CommercialModule {
    
    /**
     * 提供计费管理器
     */
    @Provides
    @Singleton
    fun provideBillingManager(
        @ApplicationContext context: Context
    ): BillingManager {
        return BillingManager(context)
    }
    
    /**
     * 提供广告管理器
     */
    @Provides
    @Singleton
    fun provideAdManager(
        @ApplicationContext context: Context
    ): AdManager {
        return AdManager(context)
    }
    
    /**
     * 提供自定义场景管理器
     */
    @Provides
    @Singleton
    fun provideCustomSceneManager(
        @ApplicationContext context: Context,
        sceneRepository: SceneRepository
    ): CustomSceneManager {
        return CustomSceneManager(context, sceneRepository)
    }
    
    /**
     * 提供应用更新检查器
     */
    @Provides
    @Singleton
    fun provideAppUpdateChecker(
        @ApplicationContext context: Context,
        httpClient: OkHttpClient
    ): AppUpdateChecker {
        return AppUpdateChecker(context, httpClient)
    }
}
