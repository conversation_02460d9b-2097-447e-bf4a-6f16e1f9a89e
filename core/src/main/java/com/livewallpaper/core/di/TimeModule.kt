package com.livewallpaper.core.di

import android.content.Context
import com.livewallpaper.core.data.preferences.WallpaperPreferences
import com.livewallpaper.core.domain.location.LocationManager
// import com.livewallpaper.core.domain.time.TimeAndSunCalcManager
import com.livewallpaper.core.domain.time.WallpaperTimeManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 时间和位置服务依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object TimeModule {

    @Provides
    @Singleton
    fun provideWallpaperPreferences(
        @ApplicationContext context: Context
    ): WallpaperPreferences {
        return WallpaperPreferences(context)
    }

    @Provides
    @Singleton
    fun provideLocationManager(
        @ApplicationContext context: Context,
        preferences: WallpaperPreferences
    ): LocationManager {
        return LocationManager(context, preferences)
    }

    // @Provides
    // @Singleton
    // fun provideTimeAndSunCalcManager(): TimeAndSunCalcManager {
    //     return TimeAndSunCalcManager()
    // }

    @Provides
    @Singleton
    fun provideWallpaperTimeManager(
        locationManager: LocationManager
    ): WallpaperTimeManager {
        return WallpaperTimeManager(locationManager)
    }
}
