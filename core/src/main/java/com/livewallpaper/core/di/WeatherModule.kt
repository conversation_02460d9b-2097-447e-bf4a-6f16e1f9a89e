package com.livewallpaper.core.di

import com.livewallpaper.core.data.database.dao.WeatherDao
import com.livewallpaper.core.data.network.WeatherApiService
import com.livewallpaper.core.data.preferences.WallpaperPreferences
import com.livewallpaper.core.data.repository.WeatherRepository
import com.livewallpaper.core.domain.location.LocationManager
import com.livewallpaper.core.domain.weather.WeatherEffectRenderer
import com.livewallpaper.core.domain.weather.WeatherManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 天气系统依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object WeatherModule {
    
    @Provides
    @Singleton
    fun provideWeatherRepository(
        weatherApiService: WeatherApiService,
        weatherDao: WeatherDao,
        preferences: WallpaperPreferences
    ): WeatherRepository {
        return WeatherRepository(weatherApiService, weatherDao, preferences)
    }
    
    @Provides
    @Singleton
    fun provideWeatherManager(
        weatherRepository: WeatherRepository,
        locationManager: LocationManager
    ): WeatherManager {
        return WeatherManager(weatherRepository, locationManager)
    }
    
    @Provides
    @Singleton
    fun provideWeatherEffectRenderer(): WeatherEffectRenderer {
        return WeatherEffectRenderer()
    }
}
