package com.livewallpaper.core.di

import android.content.Context
import androidx.room.Room
import com.livewallpaper.core.data.database.WallpaperDatabase
import com.livewallpaper.core.data.database.dao.SceneDao
import com.livewallpaper.core.data.database.dao.WeatherDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideWallpaperDatabase(
        @ApplicationContext context: Context
    ): WallpaperDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            WallpaperDatabase::class.java,
            "wallpaper_database"
        )
        .fallbackToDestructiveMigration()
        .build()
    }
    
    @Provides
    fun provideSceneDao(database: WallpaperDatabase): SceneDao {
        return database.sceneDao()
    }
    
    @Provides
    fun provideWeatherDao(database: WallpaperDatabase): WeatherDao {
        return database.weatherDao()
    }
}
