package com.livewallpaper.core.di

import android.content.Context
import com.livewallpaper.core.domain.music.MusicCardRenderer
import com.livewallpaper.core.domain.music.MusicManager
import com.livewallpaper.core.service.MusicDataBroadcaster
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 音乐系统依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object MusicModule {
    
    @Provides
    @Singleton
    fun provideMusicDataBroadcaster(): MusicDataBroadcaster {
        return MusicDataBroadcaster()
    }
    
    @Provides
    @Singleton
    fun provideMusicManager(
        @ApplicationContext context: Context,
        musicDataBroadcaster: MusicDataBroadcaster
    ): MusicManager {
        return MusicManager(context, musicDataBroadcaster)
    }
    
    @Provides
    @Singleton
    fun provideMusicCardRenderer(): MusicCardRenderer {
        return MusicCardRenderer()
    }
}
