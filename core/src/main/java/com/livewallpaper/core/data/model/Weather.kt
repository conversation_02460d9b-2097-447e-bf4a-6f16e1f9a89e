package com.livewallpaper.core.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 天气数据模型
 */
@Entity(tableName = "weather")
data class Weather(
    @PrimaryKey
    val id: String,
    val location: String,
    val latitude: Double,
    val longitude: Double,
    val temperature: Double,
    val feelsLike: Double,
    val humidity: Int,
    val pressure: Double,
    val visibility: Double,
    val uvIndex: Double,
    val windSpeed: Double,
    val windDirection: Int,
    val weatherType: WeatherType,
    val description: String,
    val iconCode: String,
    val sunrise: Long,
    val sunset: Long,
    val timestamp: Long = System.currentTimeMillis(),
    val isCurrentLocation: Boolean = false
)

/**
 * 天气预报数据模型
 */
@Entity(tableName = "weather_forecast")
data class WeatherForecast(
    @PrimaryKey
    val id: String,
    val location: String,
    val date: Long,
    val maxTemperature: Double,
    val minTemperature: Double,
    val weatherType: WeatherType,
    val description: String,
    val iconCode: String,
    val humidity: Int,
    val windSpeed: Double,
    val precipitationProbability: Int,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 位置信息
 */
data class Location(
    val latitude: Double,
    val longitude: Double,
    val city: String,
    val country: String,
    val timezone: String
)
