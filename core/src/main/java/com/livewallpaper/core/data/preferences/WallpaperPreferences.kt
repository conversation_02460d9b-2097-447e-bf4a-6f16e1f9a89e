package com.livewallpaper.core.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 壁纸设置管理类
 * 使用DataStore存储用户偏好设置
 */
@Singleton
class WallpaperPreferences @Inject constructor(
    private val context: Context
) {
    
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "wallpaper_preferences")
    
    companion object {
        // 基础设置
        val CURRENT_SCENE_ID = stringPreferencesKey("current_scene_id")
        val AUTO_CHANGE_ENABLED = booleanPreferencesKey("auto_change_enabled")
        val AUTO_CHANGE_INTERVAL = intPreferencesKey("auto_change_interval") // 分钟
        
        // 时间相关设置
        val TIME_BASED_CHANGE_ENABLED = booleanPreferencesKey("time_based_change_enabled")
        val LOCATION_PERMISSION_GRANTED = booleanPreferencesKey("location_permission_granted")
        val USE_CURRENT_LOCATION = booleanPreferencesKey("use_current_location")
        val MANUAL_LATITUDE = doublePreferencesKey("manual_latitude")
        val MANUAL_LONGITUDE = doublePreferencesKey("manual_longitude")
        val MANUAL_CITY_NAME = stringPreferencesKey("manual_city_name")
        
        // 天气相关设置
        val WEATHER_BASED_CHANGE_ENABLED = booleanPreferencesKey("weather_based_change_enabled")
        val WEATHER_API_KEY = stringPreferencesKey("weather_api_key")
        val WEATHER_UPDATE_INTERVAL = intPreferencesKey("weather_update_interval") // 分钟
        
        // 音乐相关设置
        val MUSIC_VISUALIZATION_ENABLED = booleanPreferencesKey("music_visualization_enabled")
        val NOTIFICATION_PERMISSION_GRANTED = booleanPreferencesKey("notification_permission_granted")
        val MUSIC_CARD_ENABLED = booleanPreferencesKey("music_card_enabled")
        val MUSIC_CARD_POSITION = stringPreferencesKey("music_card_position") // TOP, BOTTOM, CENTER
        
        // 性能设置
        val FRAME_RATE = intPreferencesKey("frame_rate") // FPS
        val BATTERY_OPTIMIZATION_ENABLED = booleanPreferencesKey("battery_optimization_enabled")
        val REDUCE_MOTION_ENABLED = booleanPreferencesKey("reduce_motion_enabled")
        
        // 显示设置
        val BRIGHTNESS_ADJUSTMENT = floatPreferencesKey("brightness_adjustment") // -1.0 to 1.0
        val CONTRAST_ADJUSTMENT = floatPreferencesKey("contrast_adjustment") // -1.0 to 1.0
        val SATURATION_ADJUSTMENT = floatPreferencesKey("saturation_adjustment") // -1.0 to 1.0
        
        // 高级设置
        val PARALLAX_ENABLED = booleanPreferencesKey("parallax_enabled")
        val PARTICLE_EFFECTS_ENABLED = booleanPreferencesKey("particle_effects_enabled")
        val PARTICLE_DENSITY = floatPreferencesKey("particle_density") // 0.0 to 1.0
        
        // 应用设置
        val FIRST_LAUNCH = booleanPreferencesKey("first_launch")
        val ONBOARDING_COMPLETED = booleanPreferencesKey("onboarding_completed")
        val PREMIUM_PURCHASED = booleanPreferencesKey("premium_purchased")
        val ANALYTICS_ENABLED = booleanPreferencesKey("analytics_enabled")
    }
    
    // 当前场景ID
    val currentSceneId: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[CURRENT_SCENE_ID] ?: "default_nature_1"
    }
    
    suspend fun setCurrentSceneId(sceneId: String) {
        context.dataStore.edit { preferences ->
            preferences[CURRENT_SCENE_ID] = sceneId
        }
    }
    
    // 自动切换设置
    val autoChangeEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[AUTO_CHANGE_ENABLED] ?: false
    }
    
    suspend fun setAutoChangeEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_CHANGE_ENABLED] = enabled
        }
    }
    
    val autoChangeInterval: Flow<Int> = context.dataStore.data.map { preferences ->
        preferences[AUTO_CHANGE_INTERVAL] ?: 60 // 默认60分钟
    }
    
    suspend fun setAutoChangeInterval(interval: Int) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_CHANGE_INTERVAL] = interval
        }
    }
    
    // 基于时间的切换
    val timeBasedChangeEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[TIME_BASED_CHANGE_ENABLED] ?: true
    }
    
    suspend fun setTimeBasedChangeEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[TIME_BASED_CHANGE_ENABLED] = enabled
        }
    }
    
    // 位置设置
    val useCurrentLocation: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[USE_CURRENT_LOCATION] ?: true
    }
    
    suspend fun setUseCurrentLocation(use: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[USE_CURRENT_LOCATION] = use
        }
    }
    
    // 天气设置
    val weatherBasedChangeEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[WEATHER_BASED_CHANGE_ENABLED] ?: true
    }
    
    suspend fun setWeatherBasedChangeEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[WEATHER_BASED_CHANGE_ENABLED] = enabled
        }
    }
    
    val weatherApiKey: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[WEATHER_API_KEY] ?: ""
    }
    
    suspend fun setWeatherApiKey(apiKey: String) {
        context.dataStore.edit { preferences ->
            preferences[WEATHER_API_KEY] = apiKey
        }
    }
    
    // 音乐可视化设置
    val musicVisualizationEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[MUSIC_VISUALIZATION_ENABLED] ?: false
    }
    
    suspend fun setMusicVisualizationEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[MUSIC_VISUALIZATION_ENABLED] = enabled
        }
    }
    
    // 性能设置
    val frameRate: Flow<Int> = context.dataStore.data.map { preferences ->
        preferences[FRAME_RATE] ?: 60
    }
    
    suspend fun setFrameRate(fps: Int) {
        context.dataStore.edit { preferences ->
            preferences[FRAME_RATE] = fps
        }
    }
    
    val batteryOptimizationEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[BATTERY_OPTIMIZATION_ENABLED] ?: true
    }
    
    suspend fun setBatteryOptimizationEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[BATTERY_OPTIMIZATION_ENABLED] = enabled
        }
    }
    
    // 首次启动
    val isFirstLaunch: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[FIRST_LAUNCH] ?: true
    }
    
    suspend fun setFirstLaunch(isFirst: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[FIRST_LAUNCH] = isFirst
        }
    }
    
    // 引导完成
    val onboardingCompleted: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[ONBOARDING_COMPLETED] ?: false
    }
    
    suspend fun setOnboardingCompleted(completed: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[ONBOARDING_COMPLETED] = completed
        }
    }
}
