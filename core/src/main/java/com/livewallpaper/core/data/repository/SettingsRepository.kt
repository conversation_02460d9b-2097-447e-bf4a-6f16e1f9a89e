package com.livewallpaper.core.data.repository

import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.data.preferences.SettingsDataStore
import com.livewallpaper.core.utils.Logger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设置仓库
 * 负责管理用户偏好设置的读写和验证
 */
@Singleton
class SettingsRepository @Inject constructor(
    private val settingsDataStore: SettingsDataStore
) {
    
    /**
     * 设置数据流
     */
    val settingsFlow: Flow<WallpaperSettings> = settingsDataStore.settingsFlow
    
    /**
     * 获取当前设置
     */
    suspend fun getCurrentSettings(): WallpaperSettings {
        return try {
            settingsFlow.first()
        } catch (e: Exception) {
            Logger.e("Error getting current settings", e)
            WallpaperSettings()
        }
    }
    
    /**
     * 更新基础设置
     */
    suspend fun updateBasicSettings(
        isEnabled: Boolean? = null,
        frameRate: Int? = null,
        quality: RenderQuality? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                isEnabled = isEnabled ?: current.isEnabled,
                frameRate = frameRate ?: current.frameRate,
                quality = quality ?: current.quality
            )
        }
    }
    
    /**
     * 更新场景设置
     */
    suspend fun updateSceneSettings(
        enableAutoSceneSwitch: Boolean? = null,
        sceneChangeInterval: Long? = null,
        enableParallaxEffect: Boolean? = null,
        parallaxIntensity: Float? = null,
        preferredSceneCategory: SceneCategory? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableAutoSceneSwitch = enableAutoSceneSwitch ?: current.enableAutoSceneSwitch,
                sceneChangeInterval = sceneChangeInterval ?: current.sceneChangeInterval,
                enableParallaxEffect = enableParallaxEffect ?: current.enableParallaxEffect,
                parallaxIntensity = parallaxIntensity ?: current.parallaxIntensity,
                preferredSceneCategory = preferredSceneCategory ?: current.preferredSceneCategory
            )
        }
    }
    
    /**
     * 更新时间设置
     */
    suspend fun updateTimeSettings(
        enableTimeBasedScenes: Boolean? = null,
        enableSeasonalScenes: Boolean? = null,
        userTimeZone: String? = null,
        customLatitude: Double? = null,
        customLongitude: Double? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableTimeBasedScenes = enableTimeBasedScenes ?: current.enableTimeBasedScenes,
                enableSeasonalScenes = enableSeasonalScenes ?: current.enableSeasonalScenes,
                userTimeZone = userTimeZone ?: current.userTimeZone,
                customLatitude = customLatitude ?: current.customLatitude,
                customLongitude = customLongitude ?: current.customLongitude
            )
        }
    }
    
    /**
     * 更新天气设置
     */
    suspend fun updateWeatherSettings(
        enableWeatherEffects: Boolean? = null,
        enableWeatherBasedScenes: Boolean? = null,
        weatherUpdateInterval: Long? = null,
        weatherApiKey: String? = null,
        enableLocationServices: Boolean? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableWeatherEffects = enableWeatherEffects ?: current.enableWeatherEffects,
                enableWeatherBasedScenes = enableWeatherBasedScenes ?: current.enableWeatherBasedScenes,
                weatherUpdateInterval = weatherUpdateInterval ?: current.weatherUpdateInterval,
                weatherApiKey = weatherApiKey ?: current.weatherApiKey,
                enableLocationServices = enableLocationServices ?: current.enableLocationServices
            )
        }
    }
    
    /**
     * 更新音乐设置
     */
    suspend fun updateMusicSettings(
        enableMusicVisualization: Boolean? = null,
        musicCardPosition: CardPosition? = null,
        musicCardOpacity: Float? = null,
        enableMusicCardAutoHide: Boolean? = null,
        musicCardAutoHideDelay: Long? = null,
        enableAudioVisualization: Boolean? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableMusicVisualization = enableMusicVisualization ?: current.enableMusicVisualization,
                musicCardPosition = musicCardPosition ?: current.musicCardPosition,
                musicCardOpacity = musicCardOpacity ?: current.musicCardOpacity,
                enableMusicCardAutoHide = enableMusicCardAutoHide ?: current.enableMusicCardAutoHide,
                musicCardAutoHideDelay = musicCardAutoHideDelay ?: current.musicCardAutoHideDelay,
                enableAudioVisualization = enableAudioVisualization ?: current.enableAudioVisualization
            )
        }
    }
    
    /**
     * 更新性能设置
     */
    suspend fun updatePerformanceSettings(
        enableBatteryOptimization: Boolean? = null,
        enableLowPowerMode: Boolean? = null,
        maxMemoryUsage: Int? = null,
        enableGpuAcceleration: Boolean? = null,
        enableObjectPooling: Boolean? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableBatteryOptimization = enableBatteryOptimization ?: current.enableBatteryOptimization,
                enableLowPowerMode = enableLowPowerMode ?: current.enableLowPowerMode,
                maxMemoryUsage = maxMemoryUsage ?: current.maxMemoryUsage,
                enableGpuAcceleration = enableGpuAcceleration ?: current.enableGpuAcceleration,
                enableObjectPooling = enableObjectPooling ?: current.enableObjectPooling
            )
        }
    }
    
    /**
     * 更新显示设置
     */
    suspend fun updateDisplaySettings(
        enableDebugInfo: Boolean? = null,
        debugInfoPosition: DebugInfoPosition? = null,
        enableFpsCounter: Boolean? = null,
        enableMemoryMonitor: Boolean? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableDebugInfo = enableDebugInfo ?: current.enableDebugInfo,
                debugInfoPosition = debugInfoPosition ?: current.debugInfoPosition,
                enableFpsCounter = enableFpsCounter ?: current.enableFpsCounter,
                enableMemoryMonitor = enableMemoryMonitor ?: current.enableMemoryMonitor
            )
        }
    }
    
    /**
     * 更新主题设置
     */
    suspend fun updateThemeSettings(
        themeMode: ThemeMode? = null,
        accentColor: String? = null,
        enableDynamicColors: Boolean? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                themeMode = themeMode ?: current.themeMode,
                accentColor = accentColor ?: current.accentColor,
                enableDynamicColors = enableDynamicColors ?: current.enableDynamicColors
            )
        }
    }
    
    /**
     * 更新高级设置
     */
    suspend fun updateAdvancedSettings(
        enableExperimentalFeatures: Boolean? = null,
        enableCloudSync: Boolean? = null,
        enableAnalytics: Boolean? = null,
        enableCrashReporting: Boolean? = null
    ) {
        settingsDataStore.updateSettings { current ->
            current.copy(
                enableExperimentalFeatures = enableExperimentalFeatures ?: current.enableExperimentalFeatures,
                enableCloudSync = enableCloudSync ?: current.enableCloudSync,
                enableAnalytics = enableAnalytics ?: current.enableAnalytics,
                enableCrashReporting = enableCrashReporting ?: current.enableCrashReporting
            )
        }
    }
    
    /**
     * 重置设置为默认值
     */
    suspend fun resetToDefaults() {
        settingsDataStore.resetToDefaults()
    }
    
    /**
     * 导出设置
     */
    suspend fun exportSettings(): Map<String, Any> {
        return settingsDataStore.exportSettings()
    }
    
    /**
     * 验证设置
     */
    suspend fun validateSettings(): List<String> {
        val issues = mutableListOf<String>()
        val settings = getCurrentSettings()
        
        try {
            // 验证帧率
            if (settings.frameRate < 1 || settings.frameRate > 60) {
                issues.add("帧率应在1-60之间")
            }
            
            // 验证视差强度
            if (settings.parallaxIntensity < 0f || settings.parallaxIntensity > 2f) {
                issues.add("视差强度应在0-2之间")
            }
            
            // 验证音乐卡片透明度
            if (settings.musicCardOpacity < 0f || settings.musicCardOpacity > 1f) {
                issues.add("音乐卡片透明度应在0-1之间")
            }
            
            // 验证内存使用限制
            if (settings.maxMemoryUsage < 50 || settings.maxMemoryUsage > 500) {
                issues.add("内存使用限制应在50-500MB之间")
            }
            
            // 验证颜色格式
            if (!settings.accentColor.matches(Regex("^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{8}$"))) {
                issues.add("主题色格式不正确")
            }
            
            // 验证时间间隔
            if (settings.sceneChangeInterval < 60000L) { // 最少1分钟
                issues.add("场景切换间隔不能少于1分钟")
            }
            
            if (settings.weatherUpdateInterval < 300000L) { // 最少5分钟
                issues.add("天气更新间隔不能少于5分钟")
            }
            
        } catch (e: Exception) {
            Logger.e("Error validating settings", e)
            issues.add("设置验证时发生错误: ${e.message}")
        }
        
        return issues
    }
    
    /**
     * 获取设置统计信息
     */
    suspend fun getSettingsStats(): Map<String, Any> {
        return try {
            val settings = getCurrentSettings()
            mapOf(
                "total_settings" to 30,
                "enabled_features" to listOf(
                    "auto_scene_switch" to settings.enableAutoSceneSwitch,
                    "weather_effects" to settings.enableWeatherEffects,
                    "music_visualization" to settings.enableMusicVisualization,
                    "parallax_effect" to settings.enableParallaxEffect,
                    "battery_optimization" to settings.enableBatteryOptimization
                ).count { it.second },
                "performance_level" to when {
                    settings.enableLowPowerMode -> "低功耗"
                    settings.enableBatteryOptimization -> "平衡"
                    else -> "高性能"
                },
                "quality_level" to settings.quality.displayName,
                "frame_rate" to settings.frameRate
            )
        } catch (e: Exception) {
            Logger.e("Error getting settings stats", e)
            emptyMap()
        }
    }
}
