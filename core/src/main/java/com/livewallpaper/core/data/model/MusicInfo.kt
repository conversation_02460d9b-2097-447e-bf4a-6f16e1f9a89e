package com.livewallpaper.core.data.model

import android.graphics.Bitmap
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 音乐信息数据类
 */
@Entity(tableName = "music_info")
data class MusicInfo(
    @PrimaryKey
    val id: String,
    val title: String,
    val artist: String,
    val album: String,
    val packageName: String,
    val isPlaying: Boolean,
    val duration: Long, // 总时长（毫秒）
    val position: Long, // 当前位置（毫秒）
    val albumArtPath: String? = null, // 专辑封面路径
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 获取播放进度（0.0 - 1.0）
     */
    fun getProgress(): Float {
        return if (duration > 0) {
            (position.toFloat() / duration.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }
    
    /**
     * 格式化时间显示
     */
    fun getFormattedPosition(): String {
        return formatTime(position)
    }
    
    fun getFormattedDuration(): String {
        return formatTime(duration)
    }
    
    private fun formatTime(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        return String.format("%d:%02d", minutes, seconds)
    }
}

/**
 * 音乐播放状态
 */
enum class PlaybackState {
    PLAYING,
    PAUSED,
    STOPPED,
    UNKNOWN
}

/**
 * 音乐应用信息
 */
data class MusicAppInfo(
    val packageName: String,
    val appName: String,
    val isSupported: Boolean = true
) {
    companion object {
        /**
         * 支持的音乐应用列表
         */
        val SUPPORTED_MUSIC_APPS = listOf(
            MusicAppInfo("com.spotify.music", "Spotify"),
            MusicAppInfo("com.google.android.music", "Google Play Music"),
            MusicAppInfo("com.google.android.apps.youtube.music", "YouTube Music"),
            MusicAppInfo("com.amazon.mp3", "Amazon Music"),
            MusicAppInfo("com.apple.android.music", "Apple Music"),
            MusicAppInfo("com.netease.cloudmusic", "网易云音乐"),
            MusicAppInfo("com.tencent.qqmusic", "QQ音乐"),
            MusicAppInfo("com.kugou.android", "酷狗音乐"),
            MusicAppInfo("com.kuwo.kwmusic", "酷我音乐"),
            MusicAppInfo("fm.xiami.main", "虾米音乐"),
            MusicAppInfo("com.miui.player", "小米音乐"),
            MusicAppInfo("com.huawei.music", "华为音乐"),
            MusicAppInfo("com.android.music", "系统音乐"),
            MusicAppInfo("com.sec.android.app.music", "三星音乐"),
            MusicAppInfo("com.poweramp.v2", "Poweramp"),
            MusicAppInfo("com.vlingo.midas", "VLC"),
            MusicAppInfo("org.videolan.vlc", "VLC for Android")
        )
        
        /**
         * 检查是否为支持的音乐应用
         */
        fun isSupportedMusicApp(packageName: String): Boolean {
            return SUPPORTED_MUSIC_APPS.any { it.packageName == packageName }
        }
        
        /**
         * 获取应用名称
         */
        fun getAppName(packageName: String): String {
            return SUPPORTED_MUSIC_APPS.find { it.packageName == packageName }?.appName 
                ?: packageName
        }
    }
}

/**
 * 音乐可视化数据
 */
data class MusicVisualizationData(
    val isPlaying: Boolean,
    val volume: Float, // 0.0 - 1.0
    val progress: Float, // 0.0 - 1.0
    val bassLevel: Float = 0f, // 低音强度
    val midLevel: Float = 0f, // 中音强度
    val trebleLevel: Float = 0f, // 高音强度
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 获取整体音量强度
     */
    fun getOverallIntensity(): Float {
        return if (isPlaying) {
            (bassLevel + midLevel + trebleLevel) / 3f * volume
        } else {
            0f
        }
    }
}

/**
 * 音乐卡片显示配置
 */
data class MusicCardConfig(
    val showAlbumArt: Boolean = true,
    val showProgress: Boolean = true,
    val showControls: Boolean = false,
    val cardOpacity: Float = 0.8f,
    val cardPosition: CardPosition = CardPosition.BOTTOM_RIGHT,
    val autoHideDelay: Long = 5000L, // 5秒后自动隐藏
    val enableVisualization: Boolean = true
)

/**
 * 音乐卡片位置
 */
enum class CardPosition {
    TOP_LEFT,
    TOP_RIGHT,
    BOTTOM_LEFT,
    BOTTOM_RIGHT,
    CENTER
}

/**
 * 音乐通知解析结果
 */
data class MusicNotificationData(
    val title: String?,
    val artist: String?,
    val album: String?,
    val albumArt: Bitmap?,
    val isPlaying: Boolean,
    val packageName: String,
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 转换为MusicInfo
     */
    fun toMusicInfo(): MusicInfo {
        return MusicInfo(
            id = "${packageName}_${timestamp}",
            title = title ?: "未知歌曲",
            artist = artist ?: "未知艺术家",
            album = album ?: "未知专辑",
            packageName = packageName,
            isPlaying = isPlaying,
            duration = 0L, // 通知中通常没有时长信息
            position = 0L,
            albumArtPath = null, // 专辑封面需要单独处理
            timestamp = timestamp
        )
    }
    
    /**
     * 检查数据是否有效
     */
    fun isValid(): Boolean {
        return !title.isNullOrBlank() || !artist.isNullOrBlank()
    }
}
