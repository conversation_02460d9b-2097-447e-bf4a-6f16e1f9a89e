package com.livewallpaper.core.data.model

import kotlinx.datetime.Instant

/**
 * 时间阶段枚举
 */
enum class TimePhase(val displayName: String) {
    NIGHT("夜晚"),
    DAWN("黎明"),
    DAY("白天"),
    DUSK("黄昏"),
    UNKNOWN("未知")
}

/**
 * 时间进度数据类
 */
data class TimeProgress(
    val progress: Double,
    val isDay: Boolean,
    val phase: TimePhase,
    val sunriseTime: Double,
    val sunsetTime: Double,
    val timestamp: Instant
) {
    /**
     * 获取时间进度百分比
     */
    fun getProgressPercent(): Int {
        return (progress * 100).toInt()
    }
    
    /**
     * 获取光照强度
     */
    fun getLightIntensity(): Float {
        return when (phase) {
            TimePhase.NIGHT -> 0.1f
            TimePhase.DAWN -> 0.6f
            TimePhase.DAY -> 1.0f
            TimePhase.DUSK -> 0.7f
            TimePhase.UNKNOWN -> 0.5f
        }
    }
    
    /**
     * 获取色温
     */
    fun getColorTemperature(): Float {
        return when (phase) {
            TimePhase.NIGHT -> 2700f // 暖色
            TimePhase.DAWN -> 3500f
            TimePhase.DAY -> 6500f // 冷色
            TimePhase.DUSK -> 3000f
            TimePhase.UNKNOWN -> 5000f
        }
    }
}
