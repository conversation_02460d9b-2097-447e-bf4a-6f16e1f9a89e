package com.livewallpaper.core.data.model

/**
 * 音乐信息数据模型
 */
data class MusicInfo(
    val title: String,
    val artist: String,
    val album: String,
    val albumArt: String? = null,
    val duration: Long = 0,
    val position: Long = 0,
    val isPlaying: Boolean = false,
    val packageName: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 音频可视化数据
 */
data class AudioVisualization(
    val volume: Float,
    val frequencies: FloatArray? = null,
    val amplitude: Float = 0f,
    val timestamp: Long = System.currentTimeMillis()
) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AudioVisualization

        if (volume != other.volume) return false
        if (frequencies != null) {
            if (other.frequencies == null) return false
            if (!frequencies.contentEquals(other.frequencies)) return false
        } else if (other.frequencies != null) return false
        if (amplitude != other.amplitude) return false
        if (timestamp != other.timestamp) return false

        return true
    }

    override fun hashCode(): Int {
        var result = volume.hashCode()
        result = 31 * result + (frequencies?.contentHashCode() ?: 0)
        result = 31 * result + amplitude.hashCode()
        result = 31 * result + timestamp.hashCode()
        return result
    }
}

/**
 * 播放状态
 */
enum class PlaybackState {
    PLAYING,
    PAUSED,
    STOPPED,
    UNKNOWN
}
