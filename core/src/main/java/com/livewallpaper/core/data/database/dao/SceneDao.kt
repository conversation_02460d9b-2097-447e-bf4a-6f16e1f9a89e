package com.livewallpaper.core.data.database.dao

import androidx.room.*
import com.livewallpaper.core.data.model.Scene
import com.livewallpaper.core.data.model.SceneCategory
import com.livewallpaper.core.data.model.Season
import com.livewallpaper.core.data.model.TimeOfDay
import com.livewallpaper.core.data.model.WeatherType
import kotlinx.coroutines.flow.Flow

/**
 * 场景数据访问对象
 */
@Dao
interface SceneDao {
    
    @Query("SELECT * FROM scenes ORDER BY name ASC")
    fun getAllScenes(): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE id = :id")
    suspend fun getSceneById(id: String): Scene?
    
    @Query("SELECT * FROM scenes WHERE category = :category ORDER BY name ASC")
    fun getScenesByCategory(category: SceneCategory): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE season = :season ORDER BY name ASC")
    fun getScenesBySeason(season: Season): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE timeOfDay = :timeOfDay ORDER BY name ASC")
    fun getScenesByTimeOfDay(timeOfDay: TimeOfDay): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE weatherType = :weatherType ORDER BY name ASC")
    fun getScenesByWeatherType(weatherType: WeatherType): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE isPremium = 0 ORDER BY name ASC")
    fun getFreeScenes(): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE isPremium = 1 ORDER BY name ASC")
    fun getPremiumScenes(): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE isCustom = 1 ORDER BY createdAt DESC")
    fun getCustomScenes(): Flow<List<Scene>>
    
    @Query("SELECT * FROM scenes WHERE name LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchScenes(query: String): Flow<List<Scene>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScene(scene: Scene)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScenes(scenes: List<Scene>)
    
    @Update
    suspend fun updateScene(scene: Scene)
    
    @Delete
    suspend fun deleteScene(scene: Scene)
    
    @Query("DELETE FROM scenes WHERE id = :id")
    suspend fun deleteSceneById(id: String)
    
    @Query("DELETE FROM scenes WHERE isCustom = 1")
    suspend fun deleteAllCustomScenes()
    
    @Query("SELECT COUNT(*) FROM scenes")
    suspend fun getSceneCount(): Int
    
    @Query("SELECT COUNT(*) FROM scenes WHERE isPremium = 1")
    suspend fun getPremiumSceneCount(): Int
}
