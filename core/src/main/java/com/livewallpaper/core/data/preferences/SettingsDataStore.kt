package com.livewallpaper.core.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设置数据存储
 * 使用DataStore管理用户偏好设置
 */
@Singleton
class SettingsDataStore @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "wallpaper_settings")

    // 基础设置键
    private val IS_ENABLED = booleanPreferencesKey("is_enabled")
    private val FRAME_RATE = intPreferencesKey("frame_rate")
    private val QUALITY = stringPreferencesKey("quality")

    // 场景设置键
    private val ENABLE_AUTO_SCENE_SWITCH = booleanPreferencesKey("enable_auto_scene_switch")
    private val SCENE_CHANGE_INTERVAL = longPreferencesKey("scene_change_interval")
    private val ENABLE_PARALLAX_EFFECT = booleanPreferencesKey("enable_parallax_effect")
    private val PARALLAX_INTENSITY = floatPreferencesKey("parallax_intensity")
    private val PREFERRED_SCENE_CATEGORY = stringPreferencesKey("preferred_scene_category")

    // 时间设置键
    private val ENABLE_TIME_BASED_SCENES = booleanPreferencesKey("enable_time_based_scenes")
    private val ENABLE_SEASONAL_SCENES = booleanPreferencesKey("enable_seasonal_scenes")
    private val USER_TIME_ZONE = stringPreferencesKey("user_time_zone")
    private val CUSTOM_LATITUDE = floatPreferencesKey("custom_latitude")
    private val CUSTOM_LONGITUDE = floatPreferencesKey("custom_longitude")

    // 天气设置键
    private val ENABLE_WEATHER_EFFECTS = booleanPreferencesKey("enable_weather_effects")
    private val ENABLE_WEATHER_BASED_SCENES = booleanPreferencesKey("enable_weather_based_scenes")
    private val WEATHER_UPDATE_INTERVAL = longPreferencesKey("weather_update_interval")
    private val WEATHER_API_KEY = stringPreferencesKey("weather_api_key")
    private val ENABLE_LOCATION_SERVICES = booleanPreferencesKey("enable_location_services")

    // 音乐设置键
    private val ENABLE_MUSIC_VISUALIZATION = booleanPreferencesKey("enable_music_visualization")
    private val MUSIC_CARD_POSITION = stringPreferencesKey("music_card_position")
    private val MUSIC_CARD_OPACITY = floatPreferencesKey("music_card_opacity")
    private val ENABLE_MUSIC_CARD_AUTO_HIDE = booleanPreferencesKey("enable_music_card_auto_hide")
    private val MUSIC_CARD_AUTO_HIDE_DELAY = longPreferencesKey("music_card_auto_hide_delay")
    private val ENABLE_AUDIO_VISUALIZATION = booleanPreferencesKey("enable_audio_visualization")

    // 性能设置键
    private val ENABLE_BATTERY_OPTIMIZATION = booleanPreferencesKey("enable_battery_optimization")
    private val ENABLE_LOW_POWER_MODE = booleanPreferencesKey("enable_low_power_mode")
    private val MAX_MEMORY_USAGE = intPreferencesKey("max_memory_usage")
    private val ENABLE_GPU_ACCELERATION = booleanPreferencesKey("enable_gpu_acceleration")
    private val ENABLE_OBJECT_POOLING = booleanPreferencesKey("enable_object_pooling")

    // 显示设置键
    private val ENABLE_DEBUG_INFO = booleanPreferencesKey("enable_debug_info")
    private val DEBUG_INFO_POSITION = stringPreferencesKey("debug_info_position")
    private val ENABLE_FPS_COUNTER = booleanPreferencesKey("enable_fps_counter")
    private val ENABLE_MEMORY_MONITOR = booleanPreferencesKey("enable_memory_monitor")

    // 主题设置键
    private val THEME_MODE = stringPreferencesKey("theme_mode")
    private val ACCENT_COLOR = stringPreferencesKey("accent_color")
    private val ENABLE_DYNAMIC_COLORS = booleanPreferencesKey("enable_dynamic_colors")

    // 高级设置键
    private val ENABLE_EXPERIMENTAL_FEATURES = booleanPreferencesKey("enable_experimental_features")
    private val ENABLE_CLOUD_SYNC = booleanPreferencesKey("enable_cloud_sync")
    private val ENABLE_ANALYTICS = booleanPreferencesKey("enable_analytics")
    private val ENABLE_CRASH_REPORTING = booleanPreferencesKey("enable_crash_reporting")

    /**
     * 获取设置数据流
     */
    val settingsFlow: Flow<WallpaperSettings> = context.dataStore.data
        .catch { exception ->
            Logger.e("Error reading settings", exception)
            emit(emptyPreferences())
        }
        .map { preferences ->
            WallpaperSettings(
                // 基础设置
                isEnabled = preferences[IS_ENABLED] ?: true,
                frameRate = preferences[FRAME_RATE] ?: 30,
                quality = preferences[QUALITY]?.let {
                    try { RenderQuality.valueOf(it) } catch (e: Exception) { RenderQuality.HIGH }
                } ?: RenderQuality.HIGH,

                // 场景设置
                enableAutoSceneSwitch = preferences[ENABLE_AUTO_SCENE_SWITCH] ?: true,
                sceneChangeInterval = preferences[SCENE_CHANGE_INTERVAL] ?: 3600000L,
                enableParallaxEffect = preferences[ENABLE_PARALLAX_EFFECT] ?: true,
                parallaxIntensity = preferences[PARALLAX_INTENSITY] ?: 1.0f,
                preferredSceneCategory = preferences[PREFERRED_SCENE_CATEGORY]?.let {
                    try { SceneCategory.valueOf(it) } catch (e: Exception) { null }
                },

                // 时间设置
                enableTimeBasedScenes = preferences[ENABLE_TIME_BASED_SCENES] ?: true,
                enableSeasonalScenes = preferences[ENABLE_SEASONAL_SCENES] ?: true,
                userTimeZone = preferences[USER_TIME_ZONE] ?: "Asia/Shanghai",
                customLatitude = preferences[CUSTOM_LATITUDE]?.toDouble(),
                customLongitude = preferences[CUSTOM_LONGITUDE]?.toDouble(),

                // 天气设置
                enableWeatherEffects = preferences[ENABLE_WEATHER_EFFECTS] ?: true,
                enableWeatherBasedScenes = preferences[ENABLE_WEATHER_BASED_SCENES] ?: true,
                weatherUpdateInterval = preferences[WEATHER_UPDATE_INTERVAL] ?: 1800000L,
                weatherApiKey = preferences[WEATHER_API_KEY] ?: "",
                enableLocationServices = preferences[ENABLE_LOCATION_SERVICES] ?: true,

                // 音乐设置
                enableMusicVisualization = preferences[ENABLE_MUSIC_VISUALIZATION] ?: true,
                musicCardPosition = preferences[MUSIC_CARD_POSITION]?.let {
                    try { CardPosition.valueOf(it) } catch (e: Exception) { CardPosition.BOTTOM_RIGHT }
                } ?: CardPosition.BOTTOM_RIGHT,
                musicCardOpacity = preferences[MUSIC_CARD_OPACITY] ?: 0.8f,
                enableMusicCardAutoHide = preferences[ENABLE_MUSIC_CARD_AUTO_HIDE] ?: true,
                musicCardAutoHideDelay = preferences[MUSIC_CARD_AUTO_HIDE_DELAY] ?: 5000L,
                enableAudioVisualization = preferences[ENABLE_AUDIO_VISUALIZATION] ?: true,

                // 性能设置
                enableBatteryOptimization = preferences[ENABLE_BATTERY_OPTIMIZATION] ?: true,
                enableLowPowerMode = preferences[ENABLE_LOW_POWER_MODE] ?: false,
                maxMemoryUsage = preferences[MAX_MEMORY_USAGE] ?: 100,
                enableGpuAcceleration = preferences[ENABLE_GPU_ACCELERATION] ?: true,
                enableObjectPooling = preferences[ENABLE_OBJECT_POOLING] ?: true,

                // 显示设置
                enableDebugInfo = preferences[ENABLE_DEBUG_INFO] ?: false,
                debugInfoPosition = preferences[DEBUG_INFO_POSITION]?.let {
                    try { DebugInfoPosition.valueOf(it) } catch (e: Exception) { DebugInfoPosition.TOP_LEFT }
                } ?: DebugInfoPosition.TOP_LEFT,
                enableFpsCounter = preferences[ENABLE_FPS_COUNTER] ?: false,
                enableMemoryMonitor = preferences[ENABLE_MEMORY_MONITOR] ?: false,

                // 主题设置
                themeMode = preferences[THEME_MODE]?.let {
                    try { ThemeMode.valueOf(it) } catch (e: Exception) { ThemeMode.AUTO }
                } ?: ThemeMode.AUTO,
                accentColor = preferences[ACCENT_COLOR] ?: "#FF6200EE",
                enableDynamicColors = preferences[ENABLE_DYNAMIC_COLORS] ?: true,

                // 高级设置
                enableExperimentalFeatures = preferences[ENABLE_EXPERIMENTAL_FEATURES] ?: false,
                enableCloudSync = preferences[ENABLE_CLOUD_SYNC] ?: false,
                enableAnalytics = preferences[ENABLE_ANALYTICS] ?: true,
                enableCrashReporting = preferences[ENABLE_CRASH_REPORTING] ?: true
            )
        }

    /**
     * 更新设置
     */
    suspend fun updateSettings(update: (WallpaperSettings) -> WallpaperSettings) {
        try {
            context.dataStore.edit { preferences ->
                val currentSettings = settingsFlow.map { it }.catch { emit(WallpaperSettings()) }
                val newSettings = update(WallpaperSettings()) // 简化处理，实际应该获取当前值

                // 基础设置
                preferences[IS_ENABLED] = newSettings.isEnabled
                preferences[FRAME_RATE] = newSettings.frameRate
                preferences[QUALITY] = newSettings.quality.name

                // 场景设置
                preferences[ENABLE_AUTO_SCENE_SWITCH] = newSettings.enableAutoSceneSwitch
                preferences[SCENE_CHANGE_INTERVAL] = newSettings.sceneChangeInterval
                preferences[ENABLE_PARALLAX_EFFECT] = newSettings.enableParallaxEffect
                preferences[PARALLAX_INTENSITY] = newSettings.parallaxIntensity
                newSettings.preferredSceneCategory?.let {
                    preferences[PREFERRED_SCENE_CATEGORY] = it.name
                }

                // 时间设置
                preferences[ENABLE_TIME_BASED_SCENES] = newSettings.enableTimeBasedScenes
                preferences[ENABLE_SEASONAL_SCENES] = newSettings.enableSeasonalScenes
                preferences[USER_TIME_ZONE] = newSettings.userTimeZone
                newSettings.customLatitude?.let { preferences[CUSTOM_LATITUDE] = it.toFloat() }
                newSettings.customLongitude?.let { preferences[CUSTOM_LONGITUDE] = it.toFloat() }

                // 天气设置
                preferences[ENABLE_WEATHER_EFFECTS] = newSettings.enableWeatherEffects
                preferences[ENABLE_WEATHER_BASED_SCENES] = newSettings.enableWeatherBasedScenes
                preferences[WEATHER_UPDATE_INTERVAL] = newSettings.weatherUpdateInterval
                preferences[WEATHER_API_KEY] = newSettings.weatherApiKey
                preferences[ENABLE_LOCATION_SERVICES] = newSettings.enableLocationServices

                // 音乐设置
                preferences[ENABLE_MUSIC_VISUALIZATION] = newSettings.enableMusicVisualization
                preferences[MUSIC_CARD_POSITION] = newSettings.musicCardPosition.name
                preferences[MUSIC_CARD_OPACITY] = newSettings.musicCardOpacity
                preferences[ENABLE_MUSIC_CARD_AUTO_HIDE] = newSettings.enableMusicCardAutoHide
                preferences[MUSIC_CARD_AUTO_HIDE_DELAY] = newSettings.musicCardAutoHideDelay
                preferences[ENABLE_AUDIO_VISUALIZATION] = newSettings.enableAudioVisualization

                // 性能设置
                preferences[ENABLE_BATTERY_OPTIMIZATION] = newSettings.enableBatteryOptimization
                preferences[ENABLE_LOW_POWER_MODE] = newSettings.enableLowPowerMode
                preferences[MAX_MEMORY_USAGE] = newSettings.maxMemoryUsage
                preferences[ENABLE_GPU_ACCELERATION] = newSettings.enableGpuAcceleration
                preferences[ENABLE_OBJECT_POOLING] = newSettings.enableObjectPooling

                // 显示设置
                preferences[ENABLE_DEBUG_INFO] = newSettings.enableDebugInfo
                preferences[DEBUG_INFO_POSITION] = newSettings.debugInfoPosition.name
                preferences[ENABLE_FPS_COUNTER] = newSettings.enableFpsCounter
                preferences[ENABLE_MEMORY_MONITOR] = newSettings.enableMemoryMonitor

                // 主题设置
                preferences[THEME_MODE] = newSettings.themeMode.name
                preferences[ACCENT_COLOR] = newSettings.accentColor
                preferences[ENABLE_DYNAMIC_COLORS] = newSettings.enableDynamicColors

                // 高级设置
                preferences[ENABLE_EXPERIMENTAL_FEATURES] = newSettings.enableExperimentalFeatures
                preferences[ENABLE_CLOUD_SYNC] = newSettings.enableCloudSync
                preferences[ENABLE_ANALYTICS] = newSettings.enableAnalytics
                preferences[ENABLE_CRASH_REPORTING] = newSettings.enableCrashReporting
            }
            Logger.d("Settings updated successfully")
        } catch (e: Exception) {
            Logger.e("Error updating settings", e)
            throw e
        }
    }

    /**
     * 重置设置为默认值
     */
    suspend fun resetToDefaults() {
        try {
            context.dataStore.edit { preferences ->
                preferences.clear()
            }
            Logger.d("Settings reset to defaults")
        } catch (e: Exception) {
            Logger.e("Error resetting settings", e)
            throw e
        }
    }

    /**
     * 导出设置
     */
    suspend fun exportSettings(): Map<String, Any> {
        return try {
            val preferences = context.dataStore.data.catch { emit(emptyPreferences()) }
            val result = mutableMapOf<String, Any>()

            preferences.collect { prefs ->
                prefs.asMap().forEach { (key, value) ->
                    result[key.name] = value
                }
            }

            result
        } catch (e: Exception) {
            Logger.e("Error exporting settings", e)
            emptyMap()
        }
    }
}
