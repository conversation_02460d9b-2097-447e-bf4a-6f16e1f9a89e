package com.livewallpaper.core.data.database.dao

import androidx.room.*
import com.livewallpaper.core.data.model.Weather
import com.livewallpaper.core.data.model.WeatherForecast
import kotlinx.coroutines.flow.Flow

/**
 * 天气数据访问对象
 */
@Dao
interface WeatherDao {
    
    @Query("SELECT * FROM weather WHERE isCurrentLocation = 1 ORDER BY timestamp DESC LIMIT 1")
    suspend fun getCurrentLocationWeather(): Weather?
    
    @Query("SELECT * FROM weather WHERE location = :location ORDER BY timestamp DESC LIMIT 1")
    suspend fun getWeatherByLocation(location: String): Weather?
    
    @Query("SELECT * FROM weather ORDER BY timestamp DESC")
    fun getAllWeather(): Flow<List<Weather>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWeather(weather: Weather)
    
    @Update
    suspend fun updateWeather(weather: Weather)
    
    @Delete
    suspend fun deleteWeather(weather: Weather)
    
    @Query("DELETE FROM weather WHERE timestamp < :timestamp")
    suspend fun deleteOldWeather(timestamp: Long)
    
    // 天气预报相关
    @Query("SELECT * FROM weather_forecast WHERE location = :location ORDER BY date ASC")
    fun getWeatherForecast(location: String): Flow<List<WeatherForecast>>
    
    @Query("SELECT * FROM weather_forecast WHERE location = :location AND date >= :startDate AND date <= :endDate ORDER BY date ASC")
    fun getWeatherForecastByDateRange(location: String, startDate: Long, endDate: Long): Flow<List<WeatherForecast>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWeatherForecast(forecast: WeatherForecast)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWeatherForecasts(forecasts: List<WeatherForecast>)
    
    @Query("DELETE FROM weather_forecast WHERE timestamp < :timestamp")
    suspend fun deleteOldWeatherForecast(timestamp: Long)
    
    @Query("DELETE FROM weather_forecast WHERE location = :location")
    suspend fun deleteWeatherForecastByLocation(location: String)
}
