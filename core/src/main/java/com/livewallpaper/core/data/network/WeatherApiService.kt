package com.livewallpaper.core.data.network

import com.livewallpaper.core.data.model.WeatherForecastResponse
import com.livewallpaper.core.data.model.WeatherResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * OpenWeatherMap API服务接口
 */
interface WeatherApiService {
    
    /**
     * 根据坐标获取当前天气
     */
    @GET("weather")
    suspend fun getCurrentWeatherByCoordinates(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "zh_cn"
    ): Response<WeatherResponse>
    
    /**
     * 根据城市名获取当前天气
     */
    @GET("weather")
    suspend fun getCurrentWeatherByCity(
        @Query("q") cityName: String,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "zh_cn"
    ): Response<WeatherResponse>
    
    /**
     * 根据城市ID获取当前天气
     */
    @GET("weather")
    suspend fun getCurrentWeatherByCityId(
        @Query("id") cityId: Int,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "zh_cn"
    ): Response<WeatherResponse>
    
    /**
     * 根据坐标获取5天天气预报
     */
    @GET("forecast")
    suspend fun getWeatherForecastByCoordinates(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "zh_cn",
        @Query("cnt") count: Int = 40 // 5天 * 8次/天 (每3小时一次)
    ): Response<WeatherForecastResponse>
    
    /**
     * 根据城市名获取5天天气预报
     */
    @GET("forecast")
    suspend fun getWeatherForecastByCity(
        @Query("q") cityName: String,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "zh_cn",
        @Query("cnt") count: Int = 40
    ): Response<WeatherForecastResponse>
    
    companion object {
        const val BASE_URL = "https://api.openweathermap.org/data/2.5/"
        
        // 免费API密钥（示例，实际使用时需要申请真实密钥）
        const val DEFAULT_API_KEY = "your_openweathermap_api_key_here"
        
        // API限制
        const val FREE_TIER_CALLS_PER_MINUTE = 60
        const val FREE_TIER_CALLS_PER_MONTH = 1000000
        
        // 缓存时间（毫秒）
        const val CACHE_DURATION_CURRENT_WEATHER = 10 * 60 * 1000L // 10分钟
        const val CACHE_DURATION_FORECAST = 60 * 60 * 1000L // 1小时
    }
}
