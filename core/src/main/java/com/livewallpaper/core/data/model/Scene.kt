package com.livewallpaper.core.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 场景数据模型
 * 用于存储壁纸场景的基本信息
 */
@Entity(tableName = "scenes")
data class Scene(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String,
    val backgroundImagePath: String,
    val foregroundImagePath: String? = null,
    val middlegroundImagePath: String? = null,
    val category: SceneCategory,
    val season: Season? = null,
    val timeOfDay: TimeOfDay? = null,
    val weatherType: WeatherType? = null,
    val isPremium: Boolean = false,
    val isCustom: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 场景分类
 */
enum class SceneCategory {
    NATURE,      // 自然风景
    CITY,        // 城市风光
    ABSTRACT,    // 抽象艺术
    SPACE,       // 太空宇宙
    OCEAN,       // 海洋
    MOUNTAIN,    // 山脉
    FOREST,      // 森林
    DESERT,      // 沙漠
    CUSTOM       // 自定义
}

/**
 * 季节
 */
enum class Season {
    SPRING,      // 春季
    SUMMER,      // 夏季
    AUTUMN,      // 秋季
    WINTER       // 冬季
}

/**
 * 时间段
 */
enum class TimeOfDay {
    DAWN,        // 黎明
    MORNING,     // 上午
    NOON,        // 正午
    AFTERNOON,   // 下午
    DUSK,        // 黄昏
    NIGHT        // 夜晚
}

/**
 * 天气类型
 */
enum class WeatherType {
    CLEAR,       // 晴朗
    CLOUDY,      // 多云
    RAINY,       // 雨天
    SNOWY,       // 雪天
    FOGGY,       // 雾天
    STORMY,      // 暴风雨
    WINDY        // 大风
}
