package com.livewallpaper.core.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.livewallpaper.core.data.database.dao.SceneDao
import com.livewallpaper.core.data.database.dao.WeatherDao
import com.livewallpaper.core.data.model.Scene
import com.livewallpaper.core.data.model.Weather
import com.livewallpaper.core.data.model.WeatherForecast

/**
 * 应用主数据库
 */
@Database(
    entities = [
        Scene::class,
        Weather::class,
        WeatherForecast::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class WallpaperDatabase : RoomDatabase() {
    
    abstract fun sceneDao(): SceneDao
    abstract fun weatherDao(): WeatherDao
    
    companion object {
        @Volatile
        private var INSTANCE: WallpaperDatabase? = null
        
        fun getDatabase(context: Context): WallpaperDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    WallpaperDatabase::class.java,
                    "wallpaper_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
