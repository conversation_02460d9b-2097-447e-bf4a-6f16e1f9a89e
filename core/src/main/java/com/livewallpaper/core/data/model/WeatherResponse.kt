package com.livewallpaper.core.data.model

import com.google.gson.annotations.SerializedName

/**
 * OpenWeatherMap API响应数据模型
 */
data class WeatherResponse(
    @SerializedName("coord")
    val coordinates: Coordinates,
    
    @SerializedName("weather")
    val weather: List<WeatherInfo>,
    
    @SerializedName("base")
    val base: String,
    
    @SerializedName("main")
    val main: MainWeatherData,
    
    @SerializedName("visibility")
    val visibility: Int,
    
    @SerializedName("wind")
    val wind: Wind,
    
    @SerializedName("clouds")
    val clouds: Clouds,
    
    @SerializedName("dt")
    val timestamp: Long,
    
    @SerializedName("sys")
    val sys: SystemData,
    
    @SerializedName("timezone")
    val timezone: Int,
    
    @SerializedName("id")
    val cityId: Int,
    
    @SerializedName("name")
    val cityName: String,
    
    @SerializedName("cod")
    val responseCode: Int
)

data class Coordinates(
    @SerializedName("lon")
    val longitude: Double,
    
    @SerializedName("lat")
    val latitude: Double
)

data class WeatherInfo(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("main")
    val main: String,
    
    @SerializedName("description")
    val description: String,
    
    @SerializedName("icon")
    val icon: String
)

data class MainWeatherData(
    @SerializedName("temp")
    val temperature: Double,
    
    @SerializedName("feels_like")
    val feelsLike: Double,
    
    @SerializedName("temp_min")
    val tempMin: Double,
    
    @SerializedName("temp_max")
    val tempMax: Double,
    
    @SerializedName("pressure")
    val pressure: Int,
    
    @SerializedName("humidity")
    val humidity: Int,
    
    @SerializedName("sea_level")
    val seaLevel: Int? = null,
    
    @SerializedName("grnd_level")
    val groundLevel: Int? = null
)

data class Wind(
    @SerializedName("speed")
    val speed: Double,
    
    @SerializedName("deg")
    val direction: Int,
    
    @SerializedName("gust")
    val gust: Double? = null
)

data class Clouds(
    @SerializedName("all")
    val cloudiness: Int
)

data class SystemData(
    @SerializedName("type")
    val type: Int? = null,
    
    @SerializedName("id")
    val id: Int? = null,
    
    @SerializedName("country")
    val country: String,
    
    @SerializedName("sunrise")
    val sunrise: Long,
    
    @SerializedName("sunset")
    val sunset: Long
)

/**
 * 5天天气预报响应
 */
data class WeatherForecastResponse(
    @SerializedName("cod")
    val responseCode: String,
    
    @SerializedName("message")
    val message: Int,
    
    @SerializedName("cnt")
    val count: Int,
    
    @SerializedName("list")
    val forecasts: List<ForecastItem>,
    
    @SerializedName("city")
    val city: CityInfo
)

data class ForecastItem(
    @SerializedName("dt")
    val timestamp: Long,
    
    @SerializedName("main")
    val main: MainWeatherData,
    
    @SerializedName("weather")
    val weather: List<WeatherInfo>,
    
    @SerializedName("clouds")
    val clouds: Clouds,
    
    @SerializedName("wind")
    val wind: Wind,
    
    @SerializedName("visibility")
    val visibility: Int,
    
    @SerializedName("pop")
    val precipitationProbability: Double,
    
    @SerializedName("sys")
    val sys: ForecastSys,
    
    @SerializedName("dt_txt")
    val dateText: String
)

data class ForecastSys(
    @SerializedName("pod")
    val partOfDay: String
)

data class CityInfo(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("coord")
    val coordinates: Coordinates,
    
    @SerializedName("country")
    val country: String,
    
    @SerializedName("population")
    val population: Int,
    
    @SerializedName("timezone")
    val timezone: Int,
    
    @SerializedName("sunrise")
    val sunrise: Long,
    
    @SerializedName("sunset")
    val sunset: Long
)

/**
 * 扩展函数：将API响应转换为应用内部数据模型
 */
fun WeatherResponse.toWeather(): Weather {
    val weatherInfo = weather.firstOrNull()
    return Weather(
        id = "${cityId}_${timestamp}",
        location = cityName,
        latitude = coordinates.latitude,
        longitude = coordinates.longitude,
        temperature = main.temperature - 273.15, // 转换为摄氏度
        feelsLike = main.feelsLike - 273.15,
        humidity = main.humidity,
        pressure = main.pressure.toDouble(),
        visibility = visibility / 1000.0, // 转换为公里
        uvIndex = 0.0, // OpenWeatherMap免费版不提供UV指数
        windSpeed = wind.speed,
        windDirection = wind.direction,
        weatherType = mapWeatherType(weatherInfo?.main ?: "Clear"),
        description = weatherInfo?.description ?: "",
        iconCode = weatherInfo?.icon ?: "",
        sunrise = sys.sunrise * 1000, // 转换为毫秒
        sunset = sys.sunset * 1000,
        timestamp = timestamp * 1000,
        isCurrentLocation = false
    )
}

fun ForecastItem.toWeatherForecast(location: String): WeatherForecast {
    val weatherInfo = weather.firstOrNull()
    return WeatherForecast(
        id = "${location}_${timestamp}",
        location = location,
        date = timestamp * 1000,
        maxTemperature = main.tempMax - 273.15,
        minTemperature = main.tempMin - 273.15,
        weatherType = mapWeatherType(weatherInfo?.main ?: "Clear"),
        description = weatherInfo?.description ?: "",
        iconCode = weatherInfo?.icon ?: "",
        humidity = main.humidity,
        windSpeed = wind.speed,
        precipitationProbability = (precipitationProbability * 100).toInt(),
        timestamp = System.currentTimeMillis()
    )
}

/**
 * 将OpenWeatherMap的天气类型映射到应用内部枚举
 */
private fun mapWeatherType(weatherMain: String): WeatherType {
    return when (weatherMain.lowercase()) {
        "clear" -> WeatherType.CLEAR
        "clouds" -> WeatherType.CLOUDY
        "rain", "drizzle" -> WeatherType.RAINY
        "snow" -> WeatherType.SNOWY
        "mist", "fog", "haze" -> WeatherType.FOGGY
        "thunderstorm" -> WeatherType.STORMY
        else -> WeatherType.CLEAR
    }
}
