package com.livewallpaper.core.data.database

import androidx.room.TypeConverter
import com.livewallpaper.core.data.model.*

/**
 * Room数据库类型转换器
 */
class Converters {
    
    @TypeConverter
    fun fromSceneCategory(category: SceneCategory): String {
        return category.name
    }
    
    @TypeConverter
    fun toSceneCategory(category: String): SceneCategory {
        return SceneCategory.valueOf(category)
    }
    
    @TypeConverter
    fun fromSeason(season: Season?): String? {
        return season?.name
    }
    
    @TypeConverter
    fun toSeason(season: String?): Season? {
        return season?.let { Season.valueOf(it) }
    }
    
    @TypeConverter
    fun fromTimeOfDay(timeOfDay: TimeOfDay?): String? {
        return timeOfDay?.name
    }
    
    @TypeConverter
    fun toTimeOfDay(timeOfDay: String?): TimeOfDay? {
        return timeOfDay?.let { TimeOfDay.valueOf(it) }
    }
    
    @TypeConverter
    fun fromWeatherType(weatherType: WeatherType?): String? {
        return weatherType?.name
    }
    
    @TypeConverter
    fun toWeatherType(weatherType: String?): WeatherType? {
        return weatherType?.let { WeatherType.valueOf(it) }
    }
}
