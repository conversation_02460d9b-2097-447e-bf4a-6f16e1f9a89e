package com.livewallpaper.core.data.model

import kotlinx.serialization.Serializable

/**
 * 动态壁纸设置数据类
 */
@Serializable
data class WallpaperSettings(
    // 基础设置
    val isEnabled: Boolean = true,
    val frameRate: Int = 30, // FPS
    val quality: RenderQuality = RenderQuality.HIGH,

    // 场景设置
    val enableAutoSceneSwitch: Boolean = true,
    val sceneChangeInterval: Long = 3600000L, // 1小时
    val enableParallaxEffect: Boolean = true,
    val parallaxIntensity: Float = 1.0f,
    val preferredSceneCategory: SceneCategory? = null,

    // 时间设置
    val enableTimeBasedScenes: Boolean = true,
    val enableSeasonalScenes: Boolean = true,
    val userTimeZone: String = "Asia/Shanghai",
    val customLatitude: Double? = null,
    val customLongitude: Double? = null,

    // 天气设置
    val enableWeatherEffects: Boolean = true,
    val enableWeatherBasedScenes: Boolean = true,
    val weatherUpdateInterval: Long = 1800000L, // 30分钟
    val weatherApiKey: String = "",
    val enableLocationServices: Boolean = true,

    // 音乐设置
    val enableMusicVisualization: Boolean = true,
    val musicCardPosition: CardPosition = CardPosition.BOTTOM_RIGHT,
    val musicCardOpacity: Float = 0.8f,
    val enableMusicCardAutoHide: Boolean = true,
    val musicCardAutoHideDelay: Long = 5000L,
    val enableAudioVisualization: Boolean = true,

    // 性能设置
    val enableBatteryOptimization: Boolean = true,
    val enableLowPowerMode: Boolean = false,
    val maxMemoryUsage: Int = 100, // MB
    val enableGpuAcceleration: Boolean = true,
    val enableObjectPooling: Boolean = true,

    // 显示设置
    val enableDebugInfo: Boolean = false,
    val debugInfoPosition: DebugInfoPosition = DebugInfoPosition.TOP_LEFT,
    val enableFpsCounter: Boolean = false,
    val enableMemoryMonitor: Boolean = false,

    // 主题设置
    val themeMode: ThemeMode = ThemeMode.AUTO,
    val accentColor: String = "#FF6200EE",
    val enableDynamicColors: Boolean = true,

    // 高级设置
    val enableExperimentalFeatures: Boolean = false,
    val enableCloudSync: Boolean = false,
    val enableAnalytics: Boolean = true,
    val enableCrashReporting: Boolean = true
) {
    /**
     * 获取实际帧率（考虑性能优化）
     */
    fun getEffectiveFrameRate(): Int {
        return when {
            enableLowPowerMode -> minOf(frameRate, 15)
            enableBatteryOptimization -> minOf(frameRate, 24)
            else -> frameRate
        }
    }

    /**
     * 获取实际渲染质量（考虑性能优化）
     */
    fun getEffectiveQuality(): RenderQuality {
        return when {
            enableLowPowerMode -> RenderQuality.LOW
            enableBatteryOptimization && quality == RenderQuality.ULTRA -> RenderQuality.HIGH
            else -> quality
        }
    }

    /**
     * 检查是否启用了位置服务相关功能
     */
    fun requiresLocationServices(): Boolean {
        return enableLocationServices && (enableWeatherBasedScenes || enableTimeBasedScenes)
    }

    /**
     * 检查是否启用了网络相关功能
     */
    fun requiresNetworkAccess(): Boolean {
        return enableWeatherEffects || enableWeatherBasedScenes || enableCloudSync
    }
}

/**
 * 渲染质量枚举
 */
@Serializable
enum class RenderQuality(val displayName: String, val scale: Float) {
    LOW("低", 0.5f),
    MEDIUM("中", 0.75f),
    HIGH("高", 1.0f),
    ULTRA("超高", 1.25f)
}

/**
 * 主题模式枚举
 */
@Serializable
enum class ThemeMode(val displayName: String) {
    LIGHT("浅色"),
    DARK("深色"),
    AUTO("跟随系统")
}

/**
 * 调试信息位置枚举
 */
@Serializable
enum class DebugInfoPosition(val displayName: String) {
    TOP_LEFT("左上角"),
    TOP_RIGHT("右上角"),
    BOTTOM_LEFT("左下角"),
    BOTTOM_RIGHT("右下角")
}

/**
 * 设置分类枚举
 */
enum class SettingsCategory(val displayName: String, val icon: String) {
    GENERAL("通用", "⚙️"),
    SCENE("场景", "🎨"),
    TIME("时间", "🕐"),
    WEATHER("天气", "🌤️"),
    MUSIC("音乐", "🎵"),
    PERFORMANCE("性能", "⚡"),
    DISPLAY("显示", "📱"),
    THEME("主题", "🎨"),
    ADVANCED("高级", "🔧")
}

/**
 * 设置项数据类
 */
data class SettingItem(
    val key: String,
    val title: String,
    val description: String? = null,
    val category: SettingsCategory,
    val type: SettingType,
    val defaultValue: Any? = null,
    val options: List<SettingOption>? = null,
    val min: Float? = null,
    val max: Float? = null,
    val step: Float? = null,
    val unit: String? = null,
    val requiresRestart: Boolean = false,
    val requiresPermission: String? = null,
    val isExperimental: Boolean = false
)

/**
 * 设置类型枚举
 */
enum class SettingType {
    BOOLEAN,
    INTEGER,
    FLOAT,
    STRING,
    SELECTION,
    SLIDER,
    COLOR,
    ACTION
}

/**
 * 设置选项数据类
 */
data class SettingOption(
    val value: Any,
    val label: String,
    val description: String? = null
)

/**
 * 设置验证结果
 */
sealed class SettingValidationResult {
    object Valid : SettingValidationResult()
    data class Invalid(val message: String) : SettingValidationResult()
    data class Warning(val message: String) : SettingValidationResult()
}

/**
 * 应用版本数据类
 */
@Serializable
data class AppVersion(
    val versionName: String,
    val versionCode: Long,
    val buildTime: Long
) {
    /**
     * 格式化版本显示
     */
    fun getDisplayVersion(): String {
        return "v$versionName ($versionCode)"
    }

    /**
     * 获取构建时间格式化字符串
     */
    fun getFormattedBuildTime(): String {
        val date = java.util.Date(buildTime)
        val format = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
}

/**
 * 设置工具类
 */
object SettingsUtils {

    /**
     * 验证设置值
     */
    fun validateSetting(item: SettingItem, value: Any?): SettingValidationResult {
        if (value == null) {
            return SettingValidationResult.Invalid("值不能为空")
        }

        return when (item.type) {
            SettingType.INTEGER -> {
                val intValue = (value as? Number)?.toInt()
                    ?: return SettingValidationResult.Invalid("无效的整数值")

                when {
                    item.min != null && intValue < item.min ->
                        SettingValidationResult.Invalid("值不能小于 ${item.min}")
                    item.max != null && intValue > item.max ->
                        SettingValidationResult.Invalid("值不能大于 ${item.max}")
                    else -> SettingValidationResult.Valid
                }
            }

            SettingType.FLOAT -> {
                val floatValue = (value as? Number)?.toFloat()
                    ?: return SettingValidationResult.Invalid("无效的浮点数值")

                when {
                    item.min != null && floatValue < item.min ->
                        SettingValidationResult.Invalid("值不能小于 ${item.min}")
                    item.max != null && floatValue > item.max ->
                        SettingValidationResult.Invalid("值不能大于 ${item.max}")
                    else -> SettingValidationResult.Valid
                }
            }

            SettingType.SELECTION -> {
                val options = item.options ?: return SettingValidationResult.Valid
                if (options.any { it.value == value }) {
                    SettingValidationResult.Valid
                } else {
                    SettingValidationResult.Invalid("无效的选项值")
                }
            }

            SettingType.COLOR -> {
                val colorString = value as? String
                    ?: return SettingValidationResult.Invalid("无效的颜色值")

                if (colorString.matches(Regex("^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{8}$"))) {
                    SettingValidationResult.Valid
                } else {
                    SettingValidationResult.Invalid("颜色格式应为 #RRGGBB 或 #AARRGGBB")
                }
            }

            else -> SettingValidationResult.Valid
        }
    }

    /**
     * 格式化设置值显示
     */
    fun formatValue(item: SettingItem, value: Any?): String {
        return when (item.type) {
            SettingType.BOOLEAN -> if (value == true) "开启" else "关闭"
            SettingType.INTEGER, SettingType.FLOAT -> {
                val numberValue = value?.toString() ?: "0"
                if (item.unit != null) "$numberValue ${item.unit}" else numberValue
            }
            SettingType.SELECTION -> {
                item.options?.find { it.value == value }?.label ?: value?.toString() ?: ""
            }
            else -> value?.toString() ?: ""
        }
    }
}
