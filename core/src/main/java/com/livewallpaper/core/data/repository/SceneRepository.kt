package com.livewallpaper.core.data.repository

import com.livewallpaper.core.data.database.dao.SceneDao
import com.livewallpaper.core.data.model.*
import com.livewallpaper.core.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 场景数据仓库
 * 负责管理场景数据的获取、存储和缓存
 */
@Singleton
class SceneRepository @Inject constructor(
    private val sceneDao: SceneDao
) {
    
    /**
     * 获取所有场景
     */
    fun getAllScenes(): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getAllScenes().collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取场景列表失败: ${e.message}"))
        }
    }
    
    /**
     * 根据ID获取场景
     */
    suspend fun getSceneById(id: String): Resource<Scene> {
        return try {
            val scene = sceneDao.getSceneById(id)
            if (scene != null) {
                Resource.Success(scene)
            } else {
                Resource.Error("场景不存在")
            }
        } catch (e: Exception) {
            Resource.Error("获取场景失败: ${e.message}")
        }
    }
    
    /**
     * 根据分类获取场景
     */
    fun getScenesByCategory(category: SceneCategory): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getScenesByCategory(category).collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取分类场景失败: ${e.message}"))
        }
    }
    
    /**
     * 根据季节获取场景
     */
    fun getScenesBySeason(season: Season): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getScenesBySeason(season).collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取季节场景失败: ${e.message}"))
        }
    }
    
    /**
     * 根据时间段获取场景
     */
    fun getScenesByTimeOfDay(timeOfDay: TimeOfDay): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getScenesByTimeOfDay(timeOfDay).collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取时间段场景失败: ${e.message}"))
        }
    }
    
    /**
     * 根据天气类型获取场景
     */
    fun getScenesByWeatherType(weatherType: WeatherType): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getScenesByWeatherType(weatherType).collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取天气场景失败: ${e.message}"))
        }
    }
    
    /**
     * 获取免费场景
     */
    fun getFreeScenes(): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getFreeScenes().collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取免费场景失败: ${e.message}"))
        }
    }
    
    /**
     * 获取自定义场景
     */
    fun getCustomScenes(): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.getCustomScenes().collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("获取自定义场景失败: ${e.message}"))
        }
    }
    
    /**
     * 搜索场景
     */
    fun searchScenes(query: String): Flow<Resource<List<Scene>>> = flow {
        emit(Resource.Loading())
        try {
            sceneDao.searchScenes(query).collect { scenes ->
                emit(Resource.Success(scenes))
            }
        } catch (e: Exception) {
            emit(Resource.Error("搜索场景失败: ${e.message}"))
        }
    }
    
    /**
     * 添加场景
     */
    suspend fun insertScene(scene: Scene): Resource<Unit> {
        return try {
            sceneDao.insertScene(scene)
            Resource.Success(Unit)
        } catch (e: Exception) {
            Resource.Error("添加场景失败: ${e.message}")
        }
    }
    
    /**
     * 更新场景
     */
    suspend fun updateScene(scene: Scene): Resource<Unit> {
        return try {
            sceneDao.updateScene(scene.copy(updatedAt = System.currentTimeMillis()))
            Resource.Success(Unit)
        } catch (e: Exception) {
            Resource.Error("更新场景失败: ${e.message}")
        }
    }
    
    /**
     * 删除场景
     */
    suspend fun deleteScene(scene: Scene): Resource<Unit> {
        return try {
            sceneDao.deleteScene(scene)
            Resource.Success(Unit)
        } catch (e: Exception) {
            Resource.Error("删除场景失败: ${e.message}")
        }
    }
    
    /**
     * 初始化默认场景
     */
    suspend fun initializeDefaultScenes(): Resource<Unit> {
        return try {
            val defaultScenes = createDefaultScenes()
            sceneDao.insertScenes(defaultScenes)
            Resource.Success(Unit)
        } catch (e: Exception) {
            Resource.Error("初始化默认场景失败: ${e.message}")
        }
    }
    
    /**
     * 创建默认场景列表
     */
    private fun createDefaultScenes(): List<Scene> {
        return listOf(
            Scene(
                id = "default_nature_1",
                name = "森林晨曦",
                description = "清晨的森林，阳光透过树叶洒下",
                backgroundImagePath = "scenes/forest_dawn.jpg",
                category = SceneCategory.FOREST,
                season = Season.SPRING,
                timeOfDay = TimeOfDay.DAWN,
                weatherType = WeatherType.CLEAR
            ),
            Scene(
                id = "default_city_1",
                name = "都市夜景",
                description = "繁华都市的夜晚，灯火辉煌",
                backgroundImagePath = "scenes/city_night.jpg",
                category = SceneCategory.CITY,
                timeOfDay = TimeOfDay.NIGHT,
                weatherType = WeatherType.CLEAR
            ),
            Scene(
                id = "default_ocean_1",
                name = "海边日落",
                description = "夕阳西下，海浪轻拍沙滩",
                backgroundImagePath = "scenes/ocean_sunset.jpg",
                category = SceneCategory.OCEAN,
                timeOfDay = TimeOfDay.DUSK,
                weatherType = WeatherType.CLEAR
            )
        )
    }
}
