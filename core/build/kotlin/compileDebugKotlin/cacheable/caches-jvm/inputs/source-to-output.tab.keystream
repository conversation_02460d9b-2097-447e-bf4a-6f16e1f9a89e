Qcore/src/main/java/com/livewallpaper/core/domain/weather/WeatherEffectRenderer.ktLcore/src/main/java/com/livewallpaper/core/data/database/WallpaperDatabase.kt=core/src/main/java/com/livewallpaper/core/di/NetworkModule.ktGcore/src/main/java/com/livewallpaper/core/data/database/dao/SceneDao.ktKcore/src/main/java/com/livewallpaper/core/domain/music/MusicCardRenderer.ktUcore/src/main/java/com/livewallpaper/core/service/MusicNotificationListenerService.ktKcore/src/main/java/com/livewallpaper/core/data/network/WeatherApiService.ktEcore/src/main/java/com/livewallpaper/core/data/database/Converters.ktMcore/src/main/java/com/livewallpaper/core/domain/time/WallpaperTimeManager.kt;core/src/main/java/com/livewallpaper/core/di/SceneModule.kt=core/src/main/java/com/livewallpaper/core/di/WeatherModule.kt9core/src/main/java/com/livewallpaper/core/utils/Logger.ktLcore/src/main/java/com/livewallpaper/core/utils/PlaceholderImageGenerator.kt:core/src/main/java/com/livewallpaper/core/di/TimeModule.ktJcore/src/main/java/com/livewallpaper/core/domain/weather/WeatherManager.kt@core/src/main/java/com/livewallpaper/core/data/model/TimeData.ktMcore/src/main/java/com/livewallpaper/core/performance/PerformanceOptimizer.ktLcore/src/main/java/com/livewallpaper/core/data/repository/SceneRepository.ktIcore/src/main/java/com/livewallpaper/core/data/database/dao/WeatherDao.ktEcore/src/main/java/com/livewallpaper/core/domain/scene/SceneLoader.ktIcore/src/main/java/com/livewallpaper/core/data/model/WallpaperSettings.ktKcore/src/main/java/com/livewallpaper/core/performance/DirtyRegionManager.ktFcore/src/main/java/com/livewallpaper/core/domain/scene/SceneManager.kt;core/src/main/java/com/livewallpaper/core/di/MusicModule.ktOcore/src/main/java/com/livewallpaper/core/data/repository/SettingsRepository.ktLcore/src/main/java/com/livewallpaper/core/domain/location/LocationManager.kt>core/src/main/java/com/livewallpaper/core/di/DatabaseModule.ktGcore/src/main/java/com/livewallpaper/core/domain/scene/SceneRenderer.ktKcore/src/main/java/com/livewallpaper/core/performance/PerformanceMonitor.ktCcore/src/main/java/com/livewallpaper/core/utils/AppUpdateChecker.ktRcore/src/main/java/com/livewallpaper/core/data/preferences/WallpaperPreferences.kt=core/src/main/java/com/livewallpaper/core/data/model/Scene.ktNcore/src/main/java/com/livewallpaper/core/data/repository/WeatherRepository.ktOcore/src/main/java/com/livewallpaper/core/data/preferences/SettingsDataStore.ktGcore/src/main/java/com/livewallpaper/core/data/model/WeatherResponse.ktJcore/src/main/java/com/livewallpaper/core/domain/scene/SceneInitializer.ktAcore/src/main/java/com/livewallpaper/core/data/model/MusicInfo.kt>core/src/main/java/com/livewallpaper/core/di/SettingsModule.kt;core/src/main/java/com/livewallpaper/core/utils/Resource.ktFcore/src/main/java/com/livewallpaper/core/domain/music/MusicManager.kt?core/src/main/java/com/livewallpaper/core/data/model/Weather.ktCcore/src/main/java/com/livewallpaper/core/performance/ObjectPool.ktAcore/src/main/java/com/livewallpaper/core/di/PerformanceModule.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            