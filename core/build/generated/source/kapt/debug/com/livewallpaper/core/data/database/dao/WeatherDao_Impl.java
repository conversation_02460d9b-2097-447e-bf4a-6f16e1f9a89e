package com.livewallpaper.core.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.livewallpaper.core.data.database.Converters;
import com.livewallpaper.core.data.model.Weather;
import com.livewallpaper.core.data.model.WeatherForecast;
import com.livewallpaper.core.data.model.WeatherType;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class WeatherDao_Impl implements WeatherDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Weather> __insertionAdapterOfWeather;

  private final Converters __converters = new Converters();

  private final EntityInsertionAdapter<WeatherForecast> __insertionAdapterOfWeatherForecast;

  private final EntityDeletionOrUpdateAdapter<Weather> __deletionAdapterOfWeather;

  private final EntityDeletionOrUpdateAdapter<Weather> __updateAdapterOfWeather;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldWeather;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldWeatherForecast;

  private final SharedSQLiteStatement __preparedStmtOfDeleteWeatherForecastByLocation;

  public WeatherDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWeather = new EntityInsertionAdapter<Weather>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `weather` (`id`,`location`,`latitude`,`longitude`,`temperature`,`feelsLike`,`humidity`,`pressure`,`visibility`,`uvIndex`,`windSpeed`,`windDirection`,`weatherType`,`description`,`iconCode`,`sunrise`,`sunset`,`timestamp`,`isCurrentLocation`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Weather entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLocation());
        }
        statement.bindDouble(3, entity.getLatitude());
        statement.bindDouble(4, entity.getLongitude());
        statement.bindDouble(5, entity.getTemperature());
        statement.bindDouble(6, entity.getFeelsLike());
        statement.bindLong(7, entity.getHumidity());
        statement.bindDouble(8, entity.getPressure());
        statement.bindDouble(9, entity.getVisibility());
        statement.bindDouble(10, entity.getUvIndex());
        statement.bindDouble(11, entity.getWindSpeed());
        statement.bindLong(12, entity.getWindDirection());
        final String _tmp = __converters.fromWeatherType(entity.getWeatherType());
        if (_tmp == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, _tmp);
        }
        if (entity.getDescription() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getDescription());
        }
        if (entity.getIconCode() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getIconCode());
        }
        statement.bindLong(16, entity.getSunrise());
        statement.bindLong(17, entity.getSunset());
        statement.bindLong(18, entity.getTimestamp());
        final int _tmp_1 = entity.isCurrentLocation() ? 1 : 0;
        statement.bindLong(19, _tmp_1);
      }
    };
    this.__insertionAdapterOfWeatherForecast = new EntityInsertionAdapter<WeatherForecast>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `weather_forecast` (`id`,`location`,`date`,`maxTemperature`,`minTemperature`,`weatherType`,`description`,`iconCode`,`humidity`,`windSpeed`,`precipitationProbability`,`timestamp`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WeatherForecast entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLocation());
        }
        statement.bindLong(3, entity.getDate());
        statement.bindDouble(4, entity.getMaxTemperature());
        statement.bindDouble(5, entity.getMinTemperature());
        final String _tmp = __converters.fromWeatherType(entity.getWeatherType());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp);
        }
        if (entity.getDescription() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getDescription());
        }
        if (entity.getIconCode() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getIconCode());
        }
        statement.bindLong(9, entity.getHumidity());
        statement.bindDouble(10, entity.getWindSpeed());
        statement.bindLong(11, entity.getPrecipitationProbability());
        statement.bindLong(12, entity.getTimestamp());
      }
    };
    this.__deletionAdapterOfWeather = new EntityDeletionOrUpdateAdapter<Weather>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `weather` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Weather entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfWeather = new EntityDeletionOrUpdateAdapter<Weather>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `weather` SET `id` = ?,`location` = ?,`latitude` = ?,`longitude` = ?,`temperature` = ?,`feelsLike` = ?,`humidity` = ?,`pressure` = ?,`visibility` = ?,`uvIndex` = ?,`windSpeed` = ?,`windDirection` = ?,`weatherType` = ?,`description` = ?,`iconCode` = ?,`sunrise` = ?,`sunset` = ?,`timestamp` = ?,`isCurrentLocation` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Weather entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getLocation() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLocation());
        }
        statement.bindDouble(3, entity.getLatitude());
        statement.bindDouble(4, entity.getLongitude());
        statement.bindDouble(5, entity.getTemperature());
        statement.bindDouble(6, entity.getFeelsLike());
        statement.bindLong(7, entity.getHumidity());
        statement.bindDouble(8, entity.getPressure());
        statement.bindDouble(9, entity.getVisibility());
        statement.bindDouble(10, entity.getUvIndex());
        statement.bindDouble(11, entity.getWindSpeed());
        statement.bindLong(12, entity.getWindDirection());
        final String _tmp = __converters.fromWeatherType(entity.getWeatherType());
        if (_tmp == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, _tmp);
        }
        if (entity.getDescription() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getDescription());
        }
        if (entity.getIconCode() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getIconCode());
        }
        statement.bindLong(16, entity.getSunrise());
        statement.bindLong(17, entity.getSunset());
        statement.bindLong(18, entity.getTimestamp());
        final int _tmp_1 = entity.isCurrentLocation() ? 1 : 0;
        statement.bindLong(19, _tmp_1);
        if (entity.getId() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteOldWeather = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM weather WHERE timestamp < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldWeatherForecast = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM weather_forecast WHERE timestamp < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteWeatherForecastByLocation = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM weather_forecast WHERE location = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertWeather(final Weather weather, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWeather.insert(weather);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertWeatherForecast(final WeatherForecast forecast,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWeatherForecast.insert(forecast);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertWeatherForecasts(final List<WeatherForecast> forecasts,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWeatherForecast.insert(forecasts);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWeather(final Weather weather, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWeather.handle(weather);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWeather(final Weather weather, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWeather.handle(weather);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldWeather(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldWeather.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldWeather.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldWeatherForecast(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldWeatherForecast.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldWeatherForecast.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWeatherForecastByLocation(final String location,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteWeatherForecastByLocation.acquire();
        int _argIndex = 1;
        if (location == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, location);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteWeatherForecastByLocation.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getCurrentLocationWeather(final Continuation<? super Weather> $completion) {
    final String _sql = "SELECT * FROM weather WHERE isCurrentLocation = 1 ORDER BY timestamp DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Weather>() {
      @Override
      @Nullable
      public Weather call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "temperature");
          final int _cursorIndexOfFeelsLike = CursorUtil.getColumnIndexOrThrow(_cursor, "feelsLike");
          final int _cursorIndexOfHumidity = CursorUtil.getColumnIndexOrThrow(_cursor, "humidity");
          final int _cursorIndexOfPressure = CursorUtil.getColumnIndexOrThrow(_cursor, "pressure");
          final int _cursorIndexOfVisibility = CursorUtil.getColumnIndexOrThrow(_cursor, "visibility");
          final int _cursorIndexOfUvIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "uvIndex");
          final int _cursorIndexOfWindSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "windSpeed");
          final int _cursorIndexOfWindDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "windDirection");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconCode = CursorUtil.getColumnIndexOrThrow(_cursor, "iconCode");
          final int _cursorIndexOfSunrise = CursorUtil.getColumnIndexOrThrow(_cursor, "sunrise");
          final int _cursorIndexOfSunset = CursorUtil.getColumnIndexOrThrow(_cursor, "sunset");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsCurrentLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "isCurrentLocation");
          final Weather _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            final double _tmpTemperature;
            _tmpTemperature = _cursor.getDouble(_cursorIndexOfTemperature);
            final double _tmpFeelsLike;
            _tmpFeelsLike = _cursor.getDouble(_cursorIndexOfFeelsLike);
            final int _tmpHumidity;
            _tmpHumidity = _cursor.getInt(_cursorIndexOfHumidity);
            final double _tmpPressure;
            _tmpPressure = _cursor.getDouble(_cursorIndexOfPressure);
            final double _tmpVisibility;
            _tmpVisibility = _cursor.getDouble(_cursorIndexOfVisibility);
            final double _tmpUvIndex;
            _tmpUvIndex = _cursor.getDouble(_cursorIndexOfUvIndex);
            final double _tmpWindSpeed;
            _tmpWindSpeed = _cursor.getDouble(_cursorIndexOfWindSpeed);
            final int _tmpWindDirection;
            _tmpWindDirection = _cursor.getInt(_cursorIndexOfWindDirection);
            final WeatherType _tmpWeatherType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconCode;
            if (_cursor.isNull(_cursorIndexOfIconCode)) {
              _tmpIconCode = null;
            } else {
              _tmpIconCode = _cursor.getString(_cursorIndexOfIconCode);
            }
            final long _tmpSunrise;
            _tmpSunrise = _cursor.getLong(_cursorIndexOfSunrise);
            final long _tmpSunset;
            _tmpSunset = _cursor.getLong(_cursorIndexOfSunset);
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsCurrentLocation;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCurrentLocation);
            _tmpIsCurrentLocation = _tmp_1 != 0;
            _result = new Weather(_tmpId,_tmpLocation,_tmpLatitude,_tmpLongitude,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpVisibility,_tmpUvIndex,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherType,_tmpDescription,_tmpIconCode,_tmpSunrise,_tmpSunset,_tmpTimestamp,_tmpIsCurrentLocation);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getWeatherByLocation(final String location,
      final Continuation<? super Weather> $completion) {
    final String _sql = "SELECT * FROM weather WHERE location = ? ORDER BY timestamp DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (location == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, location);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Weather>() {
      @Override
      @Nullable
      public Weather call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "temperature");
          final int _cursorIndexOfFeelsLike = CursorUtil.getColumnIndexOrThrow(_cursor, "feelsLike");
          final int _cursorIndexOfHumidity = CursorUtil.getColumnIndexOrThrow(_cursor, "humidity");
          final int _cursorIndexOfPressure = CursorUtil.getColumnIndexOrThrow(_cursor, "pressure");
          final int _cursorIndexOfVisibility = CursorUtil.getColumnIndexOrThrow(_cursor, "visibility");
          final int _cursorIndexOfUvIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "uvIndex");
          final int _cursorIndexOfWindSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "windSpeed");
          final int _cursorIndexOfWindDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "windDirection");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconCode = CursorUtil.getColumnIndexOrThrow(_cursor, "iconCode");
          final int _cursorIndexOfSunrise = CursorUtil.getColumnIndexOrThrow(_cursor, "sunrise");
          final int _cursorIndexOfSunset = CursorUtil.getColumnIndexOrThrow(_cursor, "sunset");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsCurrentLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "isCurrentLocation");
          final Weather _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            final double _tmpTemperature;
            _tmpTemperature = _cursor.getDouble(_cursorIndexOfTemperature);
            final double _tmpFeelsLike;
            _tmpFeelsLike = _cursor.getDouble(_cursorIndexOfFeelsLike);
            final int _tmpHumidity;
            _tmpHumidity = _cursor.getInt(_cursorIndexOfHumidity);
            final double _tmpPressure;
            _tmpPressure = _cursor.getDouble(_cursorIndexOfPressure);
            final double _tmpVisibility;
            _tmpVisibility = _cursor.getDouble(_cursorIndexOfVisibility);
            final double _tmpUvIndex;
            _tmpUvIndex = _cursor.getDouble(_cursorIndexOfUvIndex);
            final double _tmpWindSpeed;
            _tmpWindSpeed = _cursor.getDouble(_cursorIndexOfWindSpeed);
            final int _tmpWindDirection;
            _tmpWindDirection = _cursor.getInt(_cursorIndexOfWindDirection);
            final WeatherType _tmpWeatherType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconCode;
            if (_cursor.isNull(_cursorIndexOfIconCode)) {
              _tmpIconCode = null;
            } else {
              _tmpIconCode = _cursor.getString(_cursorIndexOfIconCode);
            }
            final long _tmpSunrise;
            _tmpSunrise = _cursor.getLong(_cursorIndexOfSunrise);
            final long _tmpSunset;
            _tmpSunset = _cursor.getLong(_cursorIndexOfSunset);
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsCurrentLocation;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCurrentLocation);
            _tmpIsCurrentLocation = _tmp_1 != 0;
            _result = new Weather(_tmpId,_tmpLocation,_tmpLatitude,_tmpLongitude,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpVisibility,_tmpUvIndex,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherType,_tmpDescription,_tmpIconCode,_tmpSunrise,_tmpSunset,_tmpTimestamp,_tmpIsCurrentLocation);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Weather>> getAllWeather() {
    final String _sql = "SELECT * FROM weather ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"weather"}, new Callable<List<Weather>>() {
      @Override
      @NonNull
      public List<Weather> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "temperature");
          final int _cursorIndexOfFeelsLike = CursorUtil.getColumnIndexOrThrow(_cursor, "feelsLike");
          final int _cursorIndexOfHumidity = CursorUtil.getColumnIndexOrThrow(_cursor, "humidity");
          final int _cursorIndexOfPressure = CursorUtil.getColumnIndexOrThrow(_cursor, "pressure");
          final int _cursorIndexOfVisibility = CursorUtil.getColumnIndexOrThrow(_cursor, "visibility");
          final int _cursorIndexOfUvIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "uvIndex");
          final int _cursorIndexOfWindSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "windSpeed");
          final int _cursorIndexOfWindDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "windDirection");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconCode = CursorUtil.getColumnIndexOrThrow(_cursor, "iconCode");
          final int _cursorIndexOfSunrise = CursorUtil.getColumnIndexOrThrow(_cursor, "sunrise");
          final int _cursorIndexOfSunset = CursorUtil.getColumnIndexOrThrow(_cursor, "sunset");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfIsCurrentLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "isCurrentLocation");
          final List<Weather> _result = new ArrayList<Weather>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Weather _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            final double _tmpTemperature;
            _tmpTemperature = _cursor.getDouble(_cursorIndexOfTemperature);
            final double _tmpFeelsLike;
            _tmpFeelsLike = _cursor.getDouble(_cursorIndexOfFeelsLike);
            final int _tmpHumidity;
            _tmpHumidity = _cursor.getInt(_cursorIndexOfHumidity);
            final double _tmpPressure;
            _tmpPressure = _cursor.getDouble(_cursorIndexOfPressure);
            final double _tmpVisibility;
            _tmpVisibility = _cursor.getDouble(_cursorIndexOfVisibility);
            final double _tmpUvIndex;
            _tmpUvIndex = _cursor.getDouble(_cursorIndexOfUvIndex);
            final double _tmpWindSpeed;
            _tmpWindSpeed = _cursor.getDouble(_cursorIndexOfWindSpeed);
            final int _tmpWindDirection;
            _tmpWindDirection = _cursor.getInt(_cursorIndexOfWindDirection);
            final WeatherType _tmpWeatherType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconCode;
            if (_cursor.isNull(_cursorIndexOfIconCode)) {
              _tmpIconCode = null;
            } else {
              _tmpIconCode = _cursor.getString(_cursorIndexOfIconCode);
            }
            final long _tmpSunrise;
            _tmpSunrise = _cursor.getLong(_cursorIndexOfSunrise);
            final long _tmpSunset;
            _tmpSunset = _cursor.getLong(_cursorIndexOfSunset);
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final boolean _tmpIsCurrentLocation;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCurrentLocation);
            _tmpIsCurrentLocation = _tmp_1 != 0;
            _item = new Weather(_tmpId,_tmpLocation,_tmpLatitude,_tmpLongitude,_tmpTemperature,_tmpFeelsLike,_tmpHumidity,_tmpPressure,_tmpVisibility,_tmpUvIndex,_tmpWindSpeed,_tmpWindDirection,_tmpWeatherType,_tmpDescription,_tmpIconCode,_tmpSunrise,_tmpSunset,_tmpTimestamp,_tmpIsCurrentLocation);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<WeatherForecast>> getWeatherForecast(final String location) {
    final String _sql = "SELECT * FROM weather_forecast WHERE location = ? ORDER BY date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (location == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, location);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"weather_forecast"}, new Callable<List<WeatherForecast>>() {
      @Override
      @NonNull
      public List<WeatherForecast> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfMaxTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "maxTemperature");
          final int _cursorIndexOfMinTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "minTemperature");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconCode = CursorUtil.getColumnIndexOrThrow(_cursor, "iconCode");
          final int _cursorIndexOfHumidity = CursorUtil.getColumnIndexOrThrow(_cursor, "humidity");
          final int _cursorIndexOfWindSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "windSpeed");
          final int _cursorIndexOfPrecipitationProbability = CursorUtil.getColumnIndexOrThrow(_cursor, "precipitationProbability");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final List<WeatherForecast> _result = new ArrayList<WeatherForecast>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WeatherForecast _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final double _tmpMaxTemperature;
            _tmpMaxTemperature = _cursor.getDouble(_cursorIndexOfMaxTemperature);
            final double _tmpMinTemperature;
            _tmpMinTemperature = _cursor.getDouble(_cursorIndexOfMinTemperature);
            final WeatherType _tmpWeatherType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconCode;
            if (_cursor.isNull(_cursorIndexOfIconCode)) {
              _tmpIconCode = null;
            } else {
              _tmpIconCode = _cursor.getString(_cursorIndexOfIconCode);
            }
            final int _tmpHumidity;
            _tmpHumidity = _cursor.getInt(_cursorIndexOfHumidity);
            final double _tmpWindSpeed;
            _tmpWindSpeed = _cursor.getDouble(_cursorIndexOfWindSpeed);
            final int _tmpPrecipitationProbability;
            _tmpPrecipitationProbability = _cursor.getInt(_cursorIndexOfPrecipitationProbability);
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            _item = new WeatherForecast(_tmpId,_tmpLocation,_tmpDate,_tmpMaxTemperature,_tmpMinTemperature,_tmpWeatherType,_tmpDescription,_tmpIconCode,_tmpHumidity,_tmpWindSpeed,_tmpPrecipitationProbability,_tmpTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<WeatherForecast>> getWeatherForecastByDateRange(final String location,
      final long startDate, final long endDate) {
    final String _sql = "SELECT * FROM weather_forecast WHERE location = ? AND date >= ? AND date <= ? ORDER BY date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (location == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, location);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 3;
    _statement.bindLong(_argIndex, endDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"weather_forecast"}, new Callable<List<WeatherForecast>>() {
      @Override
      @NonNull
      public List<WeatherForecast> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfLocation = CursorUtil.getColumnIndexOrThrow(_cursor, "location");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfMaxTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "maxTemperature");
          final int _cursorIndexOfMinTemperature = CursorUtil.getColumnIndexOrThrow(_cursor, "minTemperature");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconCode = CursorUtil.getColumnIndexOrThrow(_cursor, "iconCode");
          final int _cursorIndexOfHumidity = CursorUtil.getColumnIndexOrThrow(_cursor, "humidity");
          final int _cursorIndexOfWindSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "windSpeed");
          final int _cursorIndexOfPrecipitationProbability = CursorUtil.getColumnIndexOrThrow(_cursor, "precipitationProbability");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final List<WeatherForecast> _result = new ArrayList<WeatherForecast>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WeatherForecast _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpLocation;
            if (_cursor.isNull(_cursorIndexOfLocation)) {
              _tmpLocation = null;
            } else {
              _tmpLocation = _cursor.getString(_cursorIndexOfLocation);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final double _tmpMaxTemperature;
            _tmpMaxTemperature = _cursor.getDouble(_cursorIndexOfMaxTemperature);
            final double _tmpMinTemperature;
            _tmpMinTemperature = _cursor.getDouble(_cursorIndexOfMinTemperature);
            final WeatherType _tmpWeatherType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconCode;
            if (_cursor.isNull(_cursorIndexOfIconCode)) {
              _tmpIconCode = null;
            } else {
              _tmpIconCode = _cursor.getString(_cursorIndexOfIconCode);
            }
            final int _tmpHumidity;
            _tmpHumidity = _cursor.getInt(_cursorIndexOfHumidity);
            final double _tmpWindSpeed;
            _tmpWindSpeed = _cursor.getDouble(_cursorIndexOfWindSpeed);
            final int _tmpPrecipitationProbability;
            _tmpPrecipitationProbability = _cursor.getInt(_cursorIndexOfPrecipitationProbability);
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            _item = new WeatherForecast(_tmpId,_tmpLocation,_tmpDate,_tmpMaxTemperature,_tmpMinTemperature,_tmpWeatherType,_tmpDescription,_tmpIconCode,_tmpHumidity,_tmpWindSpeed,_tmpPrecipitationProbability,_tmpTimestamp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
