// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.domain.music.MusicCardRenderer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicModule_ProvideMusicCardRendererFactory implements Factory<MusicCardRenderer> {
  @Override
  public MusicCardRenderer get() {
    return provideMusicCardRenderer();
  }

  public static MusicModule_ProvideMusicCardRendererFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MusicCardRenderer provideMusicCardRenderer() {
    return Preconditions.checkNotNullFromProvides(MusicModule.INSTANCE.provideMusicCardRenderer());
  }

  private static final class InstanceHolder {
    private static final MusicModule_ProvideMusicCardRendererFactory INSTANCE = new MusicModule_ProvideMusicCardRendererFactory();
  }
}
