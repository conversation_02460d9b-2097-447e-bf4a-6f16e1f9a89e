// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.scene;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneLoader_Factory implements Factory<SceneLoader> {
  private final Provider<Context> contextProvider;

  public SceneLoader_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SceneLoader get() {
    return newInstance(contextProvider.get());
  }

  public static SceneLoader_Factory create(Provider<Context> contextProvider) {
    return new SceneLoader_Factory(contextProvider);
  }

  public static SceneLoader newInstance(Context context) {
    return new SceneLoader(context);
  }
}
