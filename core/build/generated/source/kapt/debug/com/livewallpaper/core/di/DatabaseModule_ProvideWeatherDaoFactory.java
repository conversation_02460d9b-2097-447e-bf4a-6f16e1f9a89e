// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.data.database.WallpaperDatabase;
import com.livewallpaper.core.data.database.dao.WeatherDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideWeatherDaoFactory implements Factory<WeatherDao> {
  private final Provider<WallpaperDatabase> databaseProvider;

  public DatabaseModule_ProvideWeatherDaoFactory(Provider<WallpaperDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public WeatherDao get() {
    return provideWeatherDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideWeatherDaoFactory create(
      Provider<WallpaperDatabase> databaseProvider) {
    return new DatabaseModule_ProvideWeatherDaoFactory(databaseProvider);
  }

  public static WeatherDao provideWeatherDao(WallpaperDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideWeatherDao(database));
  }
}
