// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.location;

import android.content.Context;
import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class LocationManager_Factory implements Factory<LocationManager> {
  private final Provider<Context> contextProvider;

  private final Provider<WallpaperPreferences> preferencesProvider;

  public LocationManager_Factory(Provider<Context> contextProvider,
      Provider<WallpaperPreferences> preferencesProvider) {
    this.contextProvider = contextProvider;
    this.preferencesProvider = preferencesProvider;
  }

  @Override
  public LocationManager get() {
    return newInstance(contextProvider.get(), preferencesProvider.get());
  }

  public static LocationManager_Factory create(Provider<Context> contextProvider,
      Provider<WallpaperPreferences> preferencesProvider) {
    return new LocationManager_Factory(contextProvider, preferencesProvider);
  }

  public static LocationManager newInstance(Context context, WallpaperPreferences preferences) {
    return new LocationManager(context, preferences);
  }
}
