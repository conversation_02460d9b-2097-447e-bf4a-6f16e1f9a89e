// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.scene;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneRenderer_Factory implements Factory<SceneRenderer> {
  @Override
  public SceneRenderer get() {
    return newInstance();
  }

  public static SceneRenderer_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SceneRenderer newInstance() {
    return new SceneRenderer();
  }

  private static final class InstanceHolder {
    private static final SceneRenderer_Factory INSTANCE = new SceneRenderer_Factory();
  }
}
