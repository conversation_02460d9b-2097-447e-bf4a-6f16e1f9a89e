// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.domain.weather.WeatherEffectRenderer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherModule_ProvideWeatherEffectRendererFactory implements Factory<WeatherEffectRenderer> {
  @Override
  public WeatherEffectRenderer get() {
    return provideWeatherEffectRenderer();
  }

  public static WeatherModule_ProvideWeatherEffectRendererFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static WeatherEffectRenderer provideWeatherEffectRenderer() {
    return Preconditions.checkNotNullFromProvides(WeatherModule.INSTANCE.provideWeatherEffectRenderer());
  }

  private static final class InstanceHolder {
    private static final WeatherModule_ProvideWeatherEffectRendererFactory INSTANCE = new WeatherModule_ProvideWeatherEffectRendererFactory();
  }
}
