// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.time;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class TimeAndSunCalcManager_Factory implements Factory<TimeAndSunCalcManager> {
  @Override
  public TimeAndSunCalcManager get() {
    return newInstance();
  }

  public static TimeAndSunCalcManager_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TimeAndSunCalcManager newInstance() {
    return new TimeAndSunCalcManager();
  }

  private static final class InstanceHolder {
    private static final TimeAndSunCalcManager_Factory INSTANCE = new TimeAndSunCalcManager_Factory();
  }
}
