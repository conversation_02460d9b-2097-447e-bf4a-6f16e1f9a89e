// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.data.repository;

import com.livewallpaper.core.data.database.dao.SceneDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneRepository_Factory implements Factory<SceneRepository> {
  private final Provider<SceneDao> sceneDaoProvider;

  public SceneRepository_Factory(Provider<SceneDao> sceneDaoProvider) {
    this.sceneDaoProvider = sceneDaoProvider;
  }

  @Override
  public SceneRepository get() {
    return newInstance(sceneDaoProvider.get());
  }

  public static SceneRepository_Factory create(Provider<SceneDao> sceneDaoProvider) {
    return new SceneRepository_Factory(sceneDaoProvider);
  }

  public static SceneRepository newInstance(SceneDao sceneDao) {
    return new SceneRepository(sceneDao);
  }
}
