// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.domain.scene.SceneRenderer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneModule_ProvideSceneRendererFactory implements Factory<SceneRenderer> {
  @Override
  public SceneRenderer get() {
    return provideSceneRenderer();
  }

  public static SceneModule_ProvideSceneRendererFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SceneRenderer provideSceneRenderer() {
    return Preconditions.checkNotNullFromProvides(SceneModule.INSTANCE.provideSceneRenderer());
  }

  private static final class InstanceHolder {
    private static final SceneModule_ProvideSceneRendererFactory INSTANCE = new SceneModule_ProvideSceneRendererFactory();
  }
}
