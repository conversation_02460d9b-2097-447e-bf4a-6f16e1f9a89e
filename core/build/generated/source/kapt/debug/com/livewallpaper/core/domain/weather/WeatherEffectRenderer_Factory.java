// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.weather;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherEffectRenderer_Factory implements Factory<WeatherEffectRenderer> {
  @Override
  public WeatherEffectRenderer get() {
    return newInstance();
  }

  public static WeatherEffectRenderer_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static WeatherEffectRenderer newInstance() {
    return new WeatherEffectRenderer();
  }

  private static final class InstanceHolder {
    private static final WeatherEffectRenderer_Factory INSTANCE = new WeatherEffectRenderer_Factory();
  }
}
