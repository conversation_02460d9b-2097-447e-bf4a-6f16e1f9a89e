// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.data.repository.SceneRepository;
import com.livewallpaper.core.domain.scene.SceneInitializer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneModule_ProvideSceneInitializerFactory implements Factory<SceneInitializer> {
  private final Provider<SceneRepository> sceneRepositoryProvider;

  public SceneModule_ProvideSceneInitializerFactory(
      Provider<SceneRepository> sceneRepositoryProvider) {
    this.sceneRepositoryProvider = sceneRepositoryProvider;
  }

  @Override
  public SceneInitializer get() {
    return provideSceneInitializer(sceneRepositoryProvider.get());
  }

  public static SceneModule_ProvideSceneInitializerFactory create(
      Provider<SceneRepository> sceneRepositoryProvider) {
    return new SceneModule_ProvideSceneInitializerFactory(sceneRepositoryProvider);
  }

  public static SceneInitializer provideSceneInitializer(SceneRepository sceneRepository) {
    return Preconditions.checkNotNullFromProvides(SceneModule.INSTANCE.provideSceneInitializer(sceneRepository));
  }
}
