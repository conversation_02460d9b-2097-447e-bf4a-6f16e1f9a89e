// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.data.database.WallpaperDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideWallpaperDatabaseFactory implements Factory<WallpaperDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideWallpaperDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public WallpaperDatabase get() {
    return provideWallpaperDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideWallpaperDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideWallpaperDatabaseFactory(contextProvider);
  }

  public static WallpaperDatabase provideWallpaperDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideWallpaperDatabase(context));
  }
}
