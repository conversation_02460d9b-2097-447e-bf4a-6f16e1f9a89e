// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.performance;

import android.content.Context;
import com.livewallpaper.core.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PerformanceOptimizer_Factory implements Factory<PerformanceOptimizer> {
  private final Provider<Context> contextProvider;

  private final Provider<PerformanceMonitor> performanceMonitorProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public PerformanceOptimizer_Factory(Provider<Context> contextProvider,
      Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.performanceMonitorProvider = performanceMonitorProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public PerformanceOptimizer get() {
    return newInstance(contextProvider.get(), performanceMonitorProvider.get(), settingsRepositoryProvider.get());
  }

  public static PerformanceOptimizer_Factory create(Provider<Context> contextProvider,
      Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new PerformanceOptimizer_Factory(contextProvider, performanceMonitorProvider, settingsRepositoryProvider);
  }

  public static PerformanceOptimizer newInstance(Context context,
      PerformanceMonitor performanceMonitor, SettingsRepository settingsRepository) {
    return new PerformanceOptimizer(context, performanceMonitor, settingsRepository);
  }
}
