// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.music;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicCardRenderer_Factory implements Factory<MusicCardRenderer> {
  @Override
  public MusicCardRenderer get() {
    return newInstance();
  }

  public static MusicCardRenderer_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MusicCardRenderer newInstance() {
    return new MusicCardRenderer();
  }

  private static final class InstanceHolder {
    private static final MusicCardRenderer_Factory INSTANCE = new MusicCardRenderer_Factory();
  }
}
