// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import com.livewallpaper.core.domain.location.LocationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class TimeModule_ProvideLocationManagerFactory implements Factory<LocationManager> {
  private final Provider<Context> contextProvider;

  private final Provider<WallpaperPreferences> preferencesProvider;

  public TimeModule_ProvideLocationManagerFactory(Provider<Context> contextProvider,
      Provider<WallpaperPreferences> preferencesProvider) {
    this.contextProvider = contextProvider;
    this.preferencesProvider = preferencesProvider;
  }

  @Override
  public LocationManager get() {
    return provideLocationManager(contextProvider.get(), preferencesProvider.get());
  }

  public static TimeModule_ProvideLocationManagerFactory create(Provider<Context> contextProvider,
      Provider<WallpaperPreferences> preferencesProvider) {
    return new TimeModule_ProvideLocationManagerFactory(contextProvider, preferencesProvider);
  }

  public static LocationManager provideLocationManager(Context context,
      WallpaperPreferences preferences) {
    return Preconditions.checkNotNullFromProvides(TimeModule.INSTANCE.provideLocationManager(context, preferences));
  }
}
