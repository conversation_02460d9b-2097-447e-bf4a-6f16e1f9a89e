// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.data.repository.WeatherRepository;
import com.livewallpaper.core.domain.location.LocationManager;
import com.livewallpaper.core.domain.weather.WeatherManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherModule_ProvideWeatherManagerFactory implements Factory<WeatherManager> {
  private final Provider<WeatherRepository> weatherRepositoryProvider;

  private final Provider<LocationManager> locationManagerProvider;

  public WeatherModule_ProvideWeatherManagerFactory(
      Provider<WeatherRepository> weatherRepositoryProvider,
      Provider<LocationManager> locationManagerProvider) {
    this.weatherRepositoryProvider = weatherRepositoryProvider;
    this.locationManagerProvider = locationManagerProvider;
  }

  @Override
  public WeatherManager get() {
    return provideWeatherManager(weatherRepositoryProvider.get(), locationManagerProvider.get());
  }

  public static WeatherModule_ProvideWeatherManagerFactory create(
      Provider<WeatherRepository> weatherRepositoryProvider,
      Provider<LocationManager> locationManagerProvider) {
    return new WeatherModule_ProvideWeatherManagerFactory(weatherRepositoryProvider, locationManagerProvider);
  }

  public static WeatherManager provideWeatherManager(WeatherRepository weatherRepository,
      LocationManager locationManager) {
    return Preconditions.checkNotNullFromProvides(WeatherModule.INSTANCE.provideWeatherManager(weatherRepository, locationManager));
  }
}
