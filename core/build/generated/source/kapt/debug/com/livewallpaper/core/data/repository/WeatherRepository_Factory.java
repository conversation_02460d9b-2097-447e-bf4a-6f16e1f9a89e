// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.data.repository;

import com.livewallpaper.core.data.database.dao.WeatherDao;
import com.livewallpaper.core.data.network.WeatherApiService;
import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherRepository_Factory implements Factory<WeatherRepository> {
  private final Provider<WeatherApiService> weatherApiServiceProvider;

  private final Provider<WeatherDao> weatherDaoProvider;

  private final Provider<WallpaperPreferences> preferencesProvider;

  public WeatherRepository_Factory(Provider<WeatherApiService> weatherApiServiceProvider,
      Provider<WeatherDao> weatherDaoProvider, Provider<WallpaperPreferences> preferencesProvider) {
    this.weatherApiServiceProvider = weatherApiServiceProvider;
    this.weatherDaoProvider = weatherDaoProvider;
    this.preferencesProvider = preferencesProvider;
  }

  @Override
  public WeatherRepository get() {
    return newInstance(weatherApiServiceProvider.get(), weatherDaoProvider.get(), preferencesProvider.get());
  }

  public static WeatherRepository_Factory create(
      Provider<WeatherApiService> weatherApiServiceProvider,
      Provider<WeatherDao> weatherDaoProvider, Provider<WallpaperPreferences> preferencesProvider) {
    return new WeatherRepository_Factory(weatherApiServiceProvider, weatherDaoProvider, preferencesProvider);
  }

  public static WeatherRepository newInstance(WeatherApiService weatherApiService,
      WeatherDao weatherDao, WallpaperPreferences preferences) {
    return new WeatherRepository(weatherApiService, weatherDao, preferences);
  }
}
