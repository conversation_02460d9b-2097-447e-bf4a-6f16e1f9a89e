// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.performance.DirtyRegionManager;
import com.livewallpaper.core.performance.ObjectPoolManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PerformanceModule_ProvideDirtyRegionManagerFactory implements Factory<DirtyRegionManager> {
  private final Provider<ObjectPoolManager> objectPoolManagerProvider;

  public PerformanceModule_ProvideDirtyRegionManagerFactory(
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    this.objectPoolManagerProvider = objectPoolManagerProvider;
  }

  @Override
  public DirtyRegionManager get() {
    return provideDirtyRegionManager(objectPoolManagerProvider.get());
  }

  public static PerformanceModule_ProvideDirtyRegionManagerFactory create(
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    return new PerformanceModule_ProvideDirtyRegionManagerFactory(objectPoolManagerProvider);
  }

  public static DirtyRegionManager provideDirtyRegionManager(ObjectPoolManager objectPoolManager) {
    return Preconditions.checkNotNullFromProvides(PerformanceModule.INSTANCE.provideDirtyRegionManager(objectPoolManager));
  }
}
