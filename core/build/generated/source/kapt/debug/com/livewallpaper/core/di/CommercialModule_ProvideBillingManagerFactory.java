// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.domain.billing.BillingManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class CommercialModule_ProvideBillingManagerFactory implements Factory<BillingManager> {
  private final Provider<Context> contextProvider;

  public CommercialModule_ProvideBillingManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public BillingManager get() {
    return provideBillingManager(contextProvider.get());
  }

  public static CommercialModule_ProvideBillingManagerFactory create(
      Provider<Context> contextProvider) {
    return new CommercialModule_ProvideBillingManagerFactory(contextProvider);
  }

  public static BillingManager provideBillingManager(Context context) {
    return Preconditions.checkNotNullFromProvides(CommercialModule.INSTANCE.provideBillingManager(context));
  }
}
