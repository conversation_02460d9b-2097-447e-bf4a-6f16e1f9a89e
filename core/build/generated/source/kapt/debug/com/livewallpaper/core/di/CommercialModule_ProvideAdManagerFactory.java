// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.domain.ads.AdManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class CommercialModule_ProvideAdManagerFactory implements Factory<AdManager> {
  private final Provider<Context> contextProvider;

  public CommercialModule_ProvideAdManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AdManager get() {
    return provideAdManager(contextProvider.get());
  }

  public static CommercialModule_ProvideAdManagerFactory create(Provider<Context> contextProvider) {
    return new CommercialModule_ProvideAdManagerFactory(contextProvider);
  }

  public static AdManager provideAdManager(Context context) {
    return Preconditions.checkNotNullFromProvides(CommercialModule.INSTANCE.provideAdManager(context));
  }
}
