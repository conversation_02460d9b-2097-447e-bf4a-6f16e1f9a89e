// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.scene;

import android.content.Context;
import com.livewallpaper.core.data.repository.SceneRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class CustomSceneManager_Factory implements Factory<CustomSceneManager> {
  private final Provider<Context> contextProvider;

  private final Provider<SceneRepository> sceneRepositoryProvider;

  public CustomSceneManager_Factory(Provider<Context> contextProvider,
      Provider<SceneRepository> sceneRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.sceneRepositoryProvider = sceneRepositoryProvider;
  }

  @Override
  public CustomSceneManager get() {
    return newInstance(contextProvider.get(), sceneRepositoryProvider.get());
  }

  public static CustomSceneManager_Factory create(Provider<Context> contextProvider,
      Provider<SceneRepository> sceneRepositoryProvider) {
    return new CustomSceneManager_Factory(contextProvider, sceneRepositoryProvider);
  }

  public static CustomSceneManager newInstance(Context context, SceneRepository sceneRepository) {
    return new CustomSceneManager(context, sceneRepository);
  }
}
