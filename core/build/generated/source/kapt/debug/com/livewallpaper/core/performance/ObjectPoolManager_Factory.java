// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.performance;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class ObjectPoolManager_Factory implements Factory<ObjectPoolManager> {
  @Override
  public ObjectPoolManager get() {
    return newInstance();
  }

  public static ObjectPoolManager_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ObjectPoolManager newInstance() {
    return new ObjectPoolManager();
  }

  private static final class InstanceHolder {
    private static final ObjectPoolManager_Factory INSTANCE = new ObjectPoolManager_Factory();
  }
}
