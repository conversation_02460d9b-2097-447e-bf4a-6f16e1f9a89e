// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.domain.music.MusicManager;
import com.livewallpaper.core.service.MusicDataBroadcaster;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicModule_ProvideMusicManagerFactory implements Factory<MusicManager> {
  private final Provider<Context> contextProvider;

  private final Provider<MusicDataBroadcaster> musicDataBroadcasterProvider;

  public MusicModule_ProvideMusicManagerFactory(Provider<Context> contextProvider,
      Provider<MusicDataBroadcaster> musicDataBroadcasterProvider) {
    this.contextProvider = contextProvider;
    this.musicDataBroadcasterProvider = musicDataBroadcasterProvider;
  }

  @Override
  public MusicManager get() {
    return provideMusicManager(contextProvider.get(), musicDataBroadcasterProvider.get());
  }

  public static MusicModule_ProvideMusicManagerFactory create(Provider<Context> contextProvider,
      Provider<MusicDataBroadcaster> musicDataBroadcasterProvider) {
    return new MusicModule_ProvideMusicManagerFactory(contextProvider, musicDataBroadcasterProvider);
  }

  public static MusicManager provideMusicManager(Context context,
      MusicDataBroadcaster musicDataBroadcaster) {
    return Preconditions.checkNotNullFromProvides(MusicModule.INSTANCE.provideMusicManager(context, musicDataBroadcaster));
  }
}
