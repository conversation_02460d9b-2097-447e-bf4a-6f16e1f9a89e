// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.service;

import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicNotificationListenerService_MembersInjector implements MembersInjector<MusicNotificationListenerService> {
  private final Provider<MusicDataBroadcaster> musicDataBroadcasterProvider;

  public MusicNotificationListenerService_MembersInjector(
      Provider<MusicDataBroadcaster> musicDataBroadcasterProvider) {
    this.musicDataBroadcasterProvider = musicDataBroadcasterProvider;
  }

  public static MembersInjector<MusicNotificationListenerService> create(
      Provider<MusicDataBroadcaster> musicDataBroadcasterProvider) {
    return new MusicNotificationListenerService_MembersInjector(musicDataBroadcasterProvider);
  }

  @Override
  public void injectMembers(MusicNotificationListenerService instance) {
    injectMusicDataBroadcaster(instance, musicDataBroadcasterProvider.get());
  }

  @InjectedFieldSignature("com.livewallpaper.core.service.MusicNotificationListenerService.musicDataBroadcaster")
  public static void injectMusicDataBroadcaster(MusicNotificationListenerService instance,
      MusicDataBroadcaster musicDataBroadcaster) {
    instance.musicDataBroadcaster = musicDataBroadcaster;
  }
}
