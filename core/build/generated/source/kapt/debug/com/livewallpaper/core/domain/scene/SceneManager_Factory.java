// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.scene;

import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import com.livewallpaper.core.data.repository.SceneRepository;
import com.livewallpaper.core.domain.time.WallpaperTimeManager;
import com.livewallpaper.core.domain.weather.WeatherManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneManager_Factory implements Factory<SceneManager> {
  private final Provider<SceneRepository> sceneRepositoryProvider;

  private final Provider<SceneLoader> sceneLoaderProvider;

  private final Provider<WallpaperTimeManager> timeManagerProvider;

  private final Provider<WeatherManager> weatherManagerProvider;

  private final Provider<WallpaperPreferences> preferencesProvider;

  public SceneManager_Factory(Provider<SceneRepository> sceneRepositoryProvider,
      Provider<SceneLoader> sceneLoaderProvider, Provider<WallpaperTimeManager> timeManagerProvider,
      Provider<WeatherManager> weatherManagerProvider,
      Provider<WallpaperPreferences> preferencesProvider) {
    this.sceneRepositoryProvider = sceneRepositoryProvider;
    this.sceneLoaderProvider = sceneLoaderProvider;
    this.timeManagerProvider = timeManagerProvider;
    this.weatherManagerProvider = weatherManagerProvider;
    this.preferencesProvider = preferencesProvider;
  }

  @Override
  public SceneManager get() {
    return newInstance(sceneRepositoryProvider.get(), sceneLoaderProvider.get(), timeManagerProvider.get(), weatherManagerProvider.get(), preferencesProvider.get());
  }

  public static SceneManager_Factory create(Provider<SceneRepository> sceneRepositoryProvider,
      Provider<SceneLoader> sceneLoaderProvider, Provider<WallpaperTimeManager> timeManagerProvider,
      Provider<WeatherManager> weatherManagerProvider,
      Provider<WallpaperPreferences> preferencesProvider) {
    return new SceneManager_Factory(sceneRepositoryProvider, sceneLoaderProvider, timeManagerProvider, weatherManagerProvider, preferencesProvider);
  }

  public static SceneManager newInstance(SceneRepository sceneRepository, SceneLoader sceneLoader,
      WallpaperTimeManager timeManager, WeatherManager weatherManager,
      WallpaperPreferences preferences) {
    return new SceneManager(sceneRepository, sceneLoader, timeManager, weatherManager, preferences);
  }
}
