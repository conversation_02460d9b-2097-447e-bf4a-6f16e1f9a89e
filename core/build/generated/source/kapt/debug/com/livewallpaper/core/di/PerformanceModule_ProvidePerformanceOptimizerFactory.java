// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.data.repository.SettingsRepository;
import com.livewallpaper.core.performance.PerformanceMonitor;
import com.livewallpaper.core.performance.PerformanceOptimizer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PerformanceModule_ProvidePerformanceOptimizerFactory implements Factory<PerformanceOptimizer> {
  private final Provider<Context> contextProvider;

  private final Provider<PerformanceMonitor> performanceMonitorProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public PerformanceModule_ProvidePerformanceOptimizerFactory(Provider<Context> contextProvider,
      Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.performanceMonitorProvider = performanceMonitorProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public PerformanceOptimizer get() {
    return providePerformanceOptimizer(contextProvider.get(), performanceMonitorProvider.get(), settingsRepositoryProvider.get());
  }

  public static PerformanceModule_ProvidePerformanceOptimizerFactory create(
      Provider<Context> contextProvider, Provider<PerformanceMonitor> performanceMonitorProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new PerformanceModule_ProvidePerformanceOptimizerFactory(contextProvider, performanceMonitorProvider, settingsRepositoryProvider);
  }

  public static PerformanceOptimizer providePerformanceOptimizer(Context context,
      PerformanceMonitor performanceMonitor, SettingsRepository settingsRepository) {
    return Preconditions.checkNotNullFromProvides(PerformanceModule.INSTANCE.providePerformanceOptimizer(context, performanceMonitor, settingsRepository));
  }
}
