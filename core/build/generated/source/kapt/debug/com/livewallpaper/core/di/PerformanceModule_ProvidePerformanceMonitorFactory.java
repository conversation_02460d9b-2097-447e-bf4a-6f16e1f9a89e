// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.performance.PerformanceMonitor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PerformanceModule_ProvidePerformanceMonitorFactory implements Factory<PerformanceMonitor> {
  private final Provider<Context> contextProvider;

  public PerformanceModule_ProvidePerformanceMonitorFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PerformanceMonitor get() {
    return providePerformanceMonitor(contextProvider.get());
  }

  public static PerformanceModule_ProvidePerformanceMonitorFactory create(
      Provider<Context> contextProvider) {
    return new PerformanceModule_ProvidePerformanceMonitorFactory(contextProvider);
  }

  public static PerformanceMonitor providePerformanceMonitor(Context context) {
    return Preconditions.checkNotNullFromProvides(PerformanceModule.INSTANCE.providePerformanceMonitor(context));
  }
}
