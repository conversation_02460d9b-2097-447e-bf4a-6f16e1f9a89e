// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.data.preferences;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WallpaperPreferences_Factory implements Factory<WallpaperPreferences> {
  private final Provider<Context> contextProvider;

  public WallpaperPreferences_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public WallpaperPreferences get() {
    return newInstance(contextProvider.get());
  }

  public static WallpaperPreferences_Factory create(Provider<Context> contextProvider) {
    return new WallpaperPreferences_Factory(contextProvider);
  }

  public static WallpaperPreferences newInstance(Context context) {
    return new WallpaperPreferences(context);
  }
}
