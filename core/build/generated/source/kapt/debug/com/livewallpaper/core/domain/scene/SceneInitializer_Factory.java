// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.scene;

import com.livewallpaper.core.data.repository.SceneRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneInitializer_Factory implements Factory<SceneInitializer> {
  private final Provider<SceneRepository> sceneRepositoryProvider;

  public SceneInitializer_Factory(Provider<SceneRepository> sceneRepositoryProvider) {
    this.sceneRepositoryProvider = sceneRepositoryProvider;
  }

  @Override
  public SceneInitializer get() {
    return newInstance(sceneRepositoryProvider.get());
  }

  public static SceneInitializer_Factory create(Provider<SceneRepository> sceneRepositoryProvider) {
    return new SceneInitializer_Factory(sceneRepositoryProvider);
  }

  public static SceneInitializer newInstance(SceneRepository sceneRepository) {
    return new SceneInitializer(sceneRepository);
  }
}
