// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicDataBroadcaster_Factory implements Factory<MusicDataBroadcaster> {
  @Override
  public MusicDataBroadcaster get() {
    return newInstance();
  }

  public static MusicDataBroadcaster_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MusicDataBroadcaster newInstance() {
    return new MusicDataBroadcaster();
  }

  private static final class InstanceHolder {
    private static final MusicDataBroadcaster_Factory INSTANCE = new MusicDataBroadcaster_Factory();
  }
}
