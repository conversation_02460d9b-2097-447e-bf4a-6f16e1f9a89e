// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.domain.update.AppUpdateChecker;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class CommercialModule_ProvideAppUpdateCheckerFactory implements Factory<AppUpdateChecker> {
  private final Provider<Context> contextProvider;

  private final Provider<OkHttpClient> httpClientProvider;

  public CommercialModule_ProvideAppUpdateCheckerFactory(Provider<Context> contextProvider,
      Provider<OkHttpClient> httpClientProvider) {
    this.contextProvider = contextProvider;
    this.httpClientProvider = httpClientProvider;
  }

  @Override
  public AppUpdateChecker get() {
    return provideAppUpdateChecker(contextProvider.get(), httpClientProvider.get());
  }

  public static CommercialModule_ProvideAppUpdateCheckerFactory create(
      Provider<Context> contextProvider, Provider<OkHttpClient> httpClientProvider) {
    return new CommercialModule_ProvideAppUpdateCheckerFactory(contextProvider, httpClientProvider);
  }

  public static AppUpdateChecker provideAppUpdateChecker(Context context, OkHttpClient httpClient) {
    return Preconditions.checkNotNullFromProvides(CommercialModule.INSTANCE.provideAppUpdateChecker(context, httpClient));
  }
}
