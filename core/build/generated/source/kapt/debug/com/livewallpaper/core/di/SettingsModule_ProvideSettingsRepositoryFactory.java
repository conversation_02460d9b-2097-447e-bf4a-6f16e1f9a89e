// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.data.preferences.SettingsDataStore;
import com.livewallpaper.core.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SettingsModule_ProvideSettingsRepositoryFactory implements Factory<SettingsRepository> {
  private final Provider<SettingsDataStore> settingsDataStoreProvider;

  public SettingsModule_ProvideSettingsRepositoryFactory(
      Provider<SettingsDataStore> settingsDataStoreProvider) {
    this.settingsDataStoreProvider = settingsDataStoreProvider;
  }

  @Override
  public SettingsRepository get() {
    return provideSettingsRepository(settingsDataStoreProvider.get());
  }

  public static SettingsModule_ProvideSettingsRepositoryFactory create(
      Provider<SettingsDataStore> settingsDataStoreProvider) {
    return new SettingsModule_ProvideSettingsRepositoryFactory(settingsDataStoreProvider);
  }

  public static SettingsRepository provideSettingsRepository(SettingsDataStore settingsDataStore) {
    return Preconditions.checkNotNullFromProvides(SettingsModule.INSTANCE.provideSettingsRepository(settingsDataStore));
  }
}
