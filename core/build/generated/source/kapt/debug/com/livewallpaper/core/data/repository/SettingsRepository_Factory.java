// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.data.repository;

import com.livewallpaper.core.data.preferences.SettingsDataStore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SettingsRepository_Factory implements Factory<SettingsRepository> {
  private final Provider<SettingsDataStore> settingsDataStoreProvider;

  public SettingsRepository_Factory(Provider<SettingsDataStore> settingsDataStoreProvider) {
    this.settingsDataStoreProvider = settingsDataStoreProvider;
  }

  @Override
  public SettingsRepository get() {
    return newInstance(settingsDataStoreProvider.get());
  }

  public static SettingsRepository_Factory create(
      Provider<SettingsDataStore> settingsDataStoreProvider) {
    return new SettingsRepository_Factory(settingsDataStoreProvider);
  }

  public static SettingsRepository newInstance(SettingsDataStore settingsDataStore) {
    return new SettingsRepository(settingsDataStore);
  }
}
