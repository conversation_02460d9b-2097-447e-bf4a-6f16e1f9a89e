// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.performance.ObjectPoolManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class PerformanceModule_ProvideObjectPoolManagerFactory implements Factory<ObjectPoolManager> {
  @Override
  public ObjectPoolManager get() {
    return provideObjectPoolManager();
  }

  public static PerformanceModule_ProvideObjectPoolManagerFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ObjectPoolManager provideObjectPoolManager() {
    return Preconditions.checkNotNullFromProvides(PerformanceModule.INSTANCE.provideObjectPoolManager());
  }

  private static final class InstanceHolder {
    private static final PerformanceModule_ProvideObjectPoolManagerFactory INSTANCE = new PerformanceModule_ProvideObjectPoolManagerFactory();
  }
}
