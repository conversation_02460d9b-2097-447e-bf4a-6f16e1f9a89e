// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class TimeModule_ProvideWallpaperPreferencesFactory implements Factory<WallpaperPreferences> {
  private final Provider<Context> contextProvider;

  public TimeModule_ProvideWallpaperPreferencesFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public WallpaperPreferences get() {
    return provideWallpaperPreferences(contextProvider.get());
  }

  public static TimeModule_ProvideWallpaperPreferencesFactory create(
      Provider<Context> contextProvider) {
    return new TimeModule_ProvideWallpaperPreferencesFactory(contextProvider);
  }

  public static WallpaperPreferences provideWallpaperPreferences(Context context) {
    return Preconditions.checkNotNullFromProvides(TimeModule.INSTANCE.provideWallpaperPreferences(context));
  }
}
