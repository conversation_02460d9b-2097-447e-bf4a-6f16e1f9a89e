// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.service.MusicDataBroadcaster;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicModule_ProvideMusicDataBroadcasterFactory implements Factory<MusicDataBroadcaster> {
  @Override
  public MusicDataBroadcaster get() {
    return provideMusicDataBroadcaster();
  }

  public static MusicModule_ProvideMusicDataBroadcasterFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MusicDataBroadcaster provideMusicDataBroadcaster() {
    return Preconditions.checkNotNullFromProvides(MusicModule.INSTANCE.provideMusicDataBroadcaster());
  }

  private static final class InstanceHolder {
    private static final MusicModule_ProvideMusicDataBroadcasterFactory INSTANCE = new MusicModule_ProvideMusicDataBroadcasterFactory();
  }
}
