// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class AppUpdateChecker_Factory implements Factory<AppUpdateChecker> {
  private final Provider<Context> contextProvider;

  public AppUpdateChecker_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AppUpdateChecker get() {
    return newInstance(contextProvider.get());
  }

  public static AppUpdateChecker_Factory create(Provider<Context> contextProvider) {
    return new AppUpdateChecker_Factory(contextProvider);
  }

  public static AppUpdateChecker newInstance(Context context) {
    return new AppUpdateChecker(context);
  }
}
