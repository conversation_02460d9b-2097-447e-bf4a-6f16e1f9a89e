// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.music;

import android.content.Context;
import com.livewallpaper.core.service.MusicDataBroadcaster;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class MusicManager_Factory implements Factory<MusicManager> {
  private final Provider<Context> contextProvider;

  private final Provider<MusicDataBroadcaster> musicDataBroadcasterProvider;

  public MusicManager_Factory(Provider<Context> contextProvider,
      Provider<MusicDataBroadcaster> musicDataBroadcasterProvider) {
    this.contextProvider = contextProvider;
    this.musicDataBroadcasterProvider = musicDataBroadcasterProvider;
  }

  @Override
  public MusicManager get() {
    return newInstance(contextProvider.get(), musicDataBroadcasterProvider.get());
  }

  public static MusicManager_Factory create(Provider<Context> contextProvider,
      Provider<MusicDataBroadcaster> musicDataBroadcasterProvider) {
    return new MusicManager_Factory(contextProvider, musicDataBroadcasterProvider);
  }

  public static MusicManager newInstance(Context context,
      MusicDataBroadcaster musicDataBroadcaster) {
    return new MusicManager(context, musicDataBroadcaster);
  }
}
