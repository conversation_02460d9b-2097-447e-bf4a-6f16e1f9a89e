package com.livewallpaper.core.data.database.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.livewallpaper.core.data.database.Converters;
import com.livewallpaper.core.data.model.Scene;
import com.livewallpaper.core.data.model.SceneCategory;
import com.livewallpaper.core.data.model.Season;
import com.livewallpaper.core.data.model.TimeOfDay;
import com.livewallpaper.core.data.model.WeatherType;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class SceneDao_Impl implements SceneDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Scene> __insertionAdapterOfScene;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<Scene> __deletionAdapterOfScene;

  private final EntityDeletionOrUpdateAdapter<Scene> __updateAdapterOfScene;

  private final SharedSQLiteStatement __preparedStmtOfDeleteSceneById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllCustomScenes;

  public SceneDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfScene = new EntityInsertionAdapter<Scene>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `scenes` (`id`,`name`,`description`,`backgroundImagePath`,`foregroundImagePath`,`middlegroundImagePath`,`category`,`season`,`timeOfDay`,`weatherType`,`isPremium`,`isCustom`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Scene entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        if (entity.getBackgroundImagePath() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getBackgroundImagePath());
        }
        if (entity.getForegroundImagePath() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getForegroundImagePath());
        }
        if (entity.getMiddlegroundImagePath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getMiddlegroundImagePath());
        }
        final String _tmp = __converters.fromSceneCategory(entity.getCategory());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        final String _tmp_1 = __converters.fromSeason(entity.getSeason());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_1);
        }
        final String _tmp_2 = __converters.fromTimeOfDay(entity.getTimeOfDay());
        if (_tmp_2 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_2);
        }
        final String _tmp_3 = __converters.fromWeatherType(entity.getWeatherType());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_3);
        }
        final int _tmp_4 = entity.isPremium() ? 1 : 0;
        statement.bindLong(11, _tmp_4);
        final int _tmp_5 = entity.isCustom() ? 1 : 0;
        statement.bindLong(12, _tmp_5);
        statement.bindLong(13, entity.getCreatedAt());
        statement.bindLong(14, entity.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfScene = new EntityDeletionOrUpdateAdapter<Scene>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `scenes` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Scene entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfScene = new EntityDeletionOrUpdateAdapter<Scene>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `scenes` SET `id` = ?,`name` = ?,`description` = ?,`backgroundImagePath` = ?,`foregroundImagePath` = ?,`middlegroundImagePath` = ?,`category` = ?,`season` = ?,`timeOfDay` = ?,`weatherType` = ?,`isPremium` = ?,`isCustom` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Scene entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        if (entity.getBackgroundImagePath() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getBackgroundImagePath());
        }
        if (entity.getForegroundImagePath() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getForegroundImagePath());
        }
        if (entity.getMiddlegroundImagePath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getMiddlegroundImagePath());
        }
        final String _tmp = __converters.fromSceneCategory(entity.getCategory());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        final String _tmp_1 = __converters.fromSeason(entity.getSeason());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_1);
        }
        final String _tmp_2 = __converters.fromTimeOfDay(entity.getTimeOfDay());
        if (_tmp_2 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_2);
        }
        final String _tmp_3 = __converters.fromWeatherType(entity.getWeatherType());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_3);
        }
        final int _tmp_4 = entity.isPremium() ? 1 : 0;
        statement.bindLong(11, _tmp_4);
        final int _tmp_5 = entity.isCustom() ? 1 : 0;
        statement.bindLong(12, _tmp_5);
        statement.bindLong(13, entity.getCreatedAt());
        statement.bindLong(14, entity.getUpdatedAt());
        if (entity.getId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteSceneById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM scenes WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllCustomScenes = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM scenes WHERE isCustom = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertScene(final Scene scene, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfScene.insert(scene);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertScenes(final List<Scene> scenes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfScene.insert(scenes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteScene(final Scene scene, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfScene.handle(scene);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateScene(final Scene scene, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfScene.handle(scene);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSceneById(final String id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteSceneById.acquire();
        int _argIndex = 1;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteSceneById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllCustomScenes(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllCustomScenes.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllCustomScenes.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Scene>> getAllScenes() {
    final String _sql = "SELECT * FROM scenes ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp);
            final Season _tmpSeason;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_1);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_2);
            final WeatherType _tmpWeatherType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_3);
            final boolean _tmpIsPremium;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_4 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSceneById(final String id, final Continuation<? super Scene> $completion) {
    final String _sql = "SELECT * FROM scenes WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Scene>() {
      @Override
      @Nullable
      public Scene call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final Scene _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp);
            final Season _tmpSeason;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_1);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_2);
            final WeatherType _tmpWeatherType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_3);
            final boolean _tmpIsPremium;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_4 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Scene>> getScenesByCategory(final SceneCategory category) {
    final String _sql = "SELECT * FROM scenes WHERE category = ? ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromSceneCategory(category);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp_1);
            final Season _tmpSeason;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_2);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_3);
            final WeatherType _tmpWeatherType;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_4);
            final boolean _tmpIsPremium;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_5 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_6 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> getScenesBySeason(final Season season) {
    final String _sql = "SELECT * FROM scenes WHERE season = ? ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromSeason(season);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp_1);
            final Season _tmpSeason;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_2);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_3);
            final WeatherType _tmpWeatherType;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_4);
            final boolean _tmpIsPremium;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_5 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_6 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> getScenesByTimeOfDay(final TimeOfDay timeOfDay) {
    final String _sql = "SELECT * FROM scenes WHERE timeOfDay = ? ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromTimeOfDay(timeOfDay);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp_1);
            final Season _tmpSeason;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_2);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_3);
            final WeatherType _tmpWeatherType;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_4);
            final boolean _tmpIsPremium;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_5 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_6 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> getScenesByWeatherType(final WeatherType weatherType) {
    final String _sql = "SELECT * FROM scenes WHERE weatherType = ? ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromWeatherType(weatherType);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp_1);
            final Season _tmpSeason;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_2);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_3);
            final WeatherType _tmpWeatherType;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_4);
            final boolean _tmpIsPremium;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_5 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_6 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> getFreeScenes() {
    final String _sql = "SELECT * FROM scenes WHERE isPremium = 0 ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp);
            final Season _tmpSeason;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_1);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_2);
            final WeatherType _tmpWeatherType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_3);
            final boolean _tmpIsPremium;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_4 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> getPremiumScenes() {
    final String _sql = "SELECT * FROM scenes WHERE isPremium = 1 ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp);
            final Season _tmpSeason;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_1);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_2);
            final WeatherType _tmpWeatherType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_3);
            final boolean _tmpIsPremium;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_4 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> getCustomScenes() {
    final String _sql = "SELECT * FROM scenes WHERE isCustom = 1 ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp);
            final Season _tmpSeason;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_1);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_2);
            final WeatherType _tmpWeatherType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_3);
            final boolean _tmpIsPremium;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_4 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Scene>> searchScenes(final String query) {
    final String _sql = "SELECT * FROM scenes WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%' ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"scenes"}, new Callable<List<Scene>>() {
      @Override
      @NonNull
      public List<Scene> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfBackgroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundImagePath");
          final int _cursorIndexOfForegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "foregroundImagePath");
          final int _cursorIndexOfMiddlegroundImagePath = CursorUtil.getColumnIndexOrThrow(_cursor, "middlegroundImagePath");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfSeason = CursorUtil.getColumnIndexOrThrow(_cursor, "season");
          final int _cursorIndexOfTimeOfDay = CursorUtil.getColumnIndexOrThrow(_cursor, "timeOfDay");
          final int _cursorIndexOfWeatherType = CursorUtil.getColumnIndexOrThrow(_cursor, "weatherType");
          final int _cursorIndexOfIsPremium = CursorUtil.getColumnIndexOrThrow(_cursor, "isPremium");
          final int _cursorIndexOfIsCustom = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustom");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Scene> _result = new ArrayList<Scene>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Scene _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpBackgroundImagePath;
            if (_cursor.isNull(_cursorIndexOfBackgroundImagePath)) {
              _tmpBackgroundImagePath = null;
            } else {
              _tmpBackgroundImagePath = _cursor.getString(_cursorIndexOfBackgroundImagePath);
            }
            final String _tmpForegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfForegroundImagePath)) {
              _tmpForegroundImagePath = null;
            } else {
              _tmpForegroundImagePath = _cursor.getString(_cursorIndexOfForegroundImagePath);
            }
            final String _tmpMiddlegroundImagePath;
            if (_cursor.isNull(_cursorIndexOfMiddlegroundImagePath)) {
              _tmpMiddlegroundImagePath = null;
            } else {
              _tmpMiddlegroundImagePath = _cursor.getString(_cursorIndexOfMiddlegroundImagePath);
            }
            final SceneCategory _tmpCategory;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCategory);
            }
            _tmpCategory = __converters.toSceneCategory(_tmp);
            final Season _tmpSeason;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfSeason)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfSeason);
            }
            _tmpSeason = __converters.toSeason(_tmp_1);
            final TimeOfDay _tmpTimeOfDay;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimeOfDay)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimeOfDay);
            }
            _tmpTimeOfDay = __converters.toTimeOfDay(_tmp_2);
            final WeatherType _tmpWeatherType;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWeatherType)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfWeatherType);
            }
            _tmpWeatherType = __converters.toWeatherType(_tmp_3);
            final boolean _tmpIsPremium;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPremium);
            _tmpIsPremium = _tmp_4 != 0;
            final boolean _tmpIsCustom;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsCustom);
            _tmpIsCustom = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new Scene(_tmpId,_tmpName,_tmpDescription,_tmpBackgroundImagePath,_tmpForegroundImagePath,_tmpMiddlegroundImagePath,_tmpCategory,_tmpSeason,_tmpTimeOfDay,_tmpWeatherType,_tmpIsPremium,_tmpIsCustom,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSceneCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM scenes";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPremiumSceneCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM scenes WHERE isPremium = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
