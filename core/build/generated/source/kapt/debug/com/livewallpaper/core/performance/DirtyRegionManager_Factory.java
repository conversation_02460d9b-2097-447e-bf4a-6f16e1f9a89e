// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.performance;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DirtyRegionManager_Factory implements Factory<DirtyRegionManager> {
  private final Provider<ObjectPoolManager> objectPoolManagerProvider;

  public DirtyRegionManager_Factory(Provider<ObjectPoolManager> objectPoolManagerProvider) {
    this.objectPoolManagerProvider = objectPoolManagerProvider;
  }

  @Override
  public DirtyRegionManager get() {
    return newInstance(objectPoolManagerProvider.get());
  }

  public static DirtyRegionManager_Factory create(
      Provider<ObjectPoolManager> objectPoolManagerProvider) {
    return new DirtyRegionManager_Factory(objectPoolManagerProvider);
  }

  public static DirtyRegionManager newInstance(ObjectPoolManager objectPoolManager) {
    return new DirtyRegionManager(objectPoolManager);
  }
}
