// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.domain.time.TimeAndSunCalcManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class TimeModule_ProvideTimeAndSunCalcManagerFactory implements Factory<TimeAndSunCalcManager> {
  @Override
  public TimeAndSunCalcManager get() {
    return provideTimeAndSunCalcManager();
  }

  public static TimeModule_ProvideTimeAndSunCalcManagerFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TimeAndSunCalcManager provideTimeAndSunCalcManager() {
    return Preconditions.checkNotNullFromProvides(TimeModule.INSTANCE.provideTimeAndSunCalcManager());
  }

  private static final class InstanceHolder {
    private static final TimeModule_ProvideTimeAndSunCalcManagerFactory INSTANCE = new TimeModule_ProvideTimeAndSunCalcManagerFactory();
  }
}
