// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.data.database.dao.WeatherDao;
import com.livewallpaper.core.data.network.WeatherApiService;
import com.livewallpaper.core.data.preferences.WallpaperPreferences;
import com.livewallpaper.core.data.repository.WeatherRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherModule_ProvideWeatherRepositoryFactory implements Factory<WeatherRepository> {
  private final Provider<WeatherApiService> weatherApiServiceProvider;

  private final Provider<WeatherDao> weatherDaoProvider;

  private final Provider<WallpaperPreferences> preferencesProvider;

  public WeatherModule_ProvideWeatherRepositoryFactory(
      Provider<WeatherApiService> weatherApiServiceProvider,
      Provider<WeatherDao> weatherDaoProvider, Provider<WallpaperPreferences> preferencesProvider) {
    this.weatherApiServiceProvider = weatherApiServiceProvider;
    this.weatherDaoProvider = weatherDaoProvider;
    this.preferencesProvider = preferencesProvider;
  }

  @Override
  public WeatherRepository get() {
    return provideWeatherRepository(weatherApiServiceProvider.get(), weatherDaoProvider.get(), preferencesProvider.get());
  }

  public static WeatherModule_ProvideWeatherRepositoryFactory create(
      Provider<WeatherApiService> weatherApiServiceProvider,
      Provider<WeatherDao> weatherDaoProvider, Provider<WallpaperPreferences> preferencesProvider) {
    return new WeatherModule_ProvideWeatherRepositoryFactory(weatherApiServiceProvider, weatherDaoProvider, preferencesProvider);
  }

  public static WeatherRepository provideWeatherRepository(WeatherApiService weatherApiService,
      WeatherDao weatherDao, WallpaperPreferences preferences) {
    return Preconditions.checkNotNullFromProvides(WeatherModule.INSTANCE.provideWeatherRepository(weatherApiService, weatherDao, preferences));
  }
}
