// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.time;

import com.livewallpaper.core.domain.location.LocationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WallpaperTimeManager_Factory implements Factory<WallpaperTimeManager> {
  private final Provider<LocationManager> locationManagerProvider;

  public WallpaperTimeManager_Factory(Provider<LocationManager> locationManagerProvider) {
    this.locationManagerProvider = locationManagerProvider;
  }

  @Override
  public WallpaperTimeManager get() {
    return newInstance(locationManagerProvider.get());
  }

  public static WallpaperTimeManager_Factory create(
      Provider<LocationManager> locationManagerProvider) {
    return new WallpaperTimeManager_Factory(locationManagerProvider);
  }

  public static WallpaperTimeManager newInstance(LocationManager locationManager) {
    return new WallpaperTimeManager(locationManager);
  }
}
