// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.update;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class AppUpdateChecker_Factory implements Factory<AppUpdateChecker> {
  private final Provider<Context> contextProvider;

  private final Provider<OkHttpClient> httpClientProvider;

  public AppUpdateChecker_Factory(Provider<Context> contextProvider,
      Provider<OkHttpClient> httpClientProvider) {
    this.contextProvider = contextProvider;
    this.httpClientProvider = httpClientProvider;
  }

  @Override
  public AppUpdateChecker get() {
    return newInstance(contextProvider.get(), httpClientProvider.get());
  }

  public static AppUpdateChecker_Factory create(Provider<Context> contextProvider,
      Provider<OkHttpClient> httpClientProvider) {
    return new AppUpdateChecker_Factory(contextProvider, httpClientProvider);
  }

  public static AppUpdateChecker newInstance(Context context, OkHttpClient httpClient) {
    return new AppUpdateChecker(context, httpClient);
  }
}
