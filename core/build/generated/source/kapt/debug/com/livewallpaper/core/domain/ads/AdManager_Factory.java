// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.ads;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class AdManager_Factory implements Factory<AdManager> {
  private final Provider<Context> contextProvider;

  public AdManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AdManager get() {
    return newInstance(contextProvider.get());
  }

  public static AdManager_Factory create(Provider<Context> contextProvider) {
    return new AdManager_Factory(contextProvider);
  }

  public static AdManager newInstance(Context context) {
    return new AdManager(context);
  }
}
