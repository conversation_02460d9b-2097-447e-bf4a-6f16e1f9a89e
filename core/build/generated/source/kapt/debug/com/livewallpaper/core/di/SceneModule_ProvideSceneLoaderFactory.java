// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.domain.scene.SceneLoader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SceneModule_ProvideSceneLoaderFactory implements Factory<SceneLoader> {
  private final Provider<Context> contextProvider;

  public SceneModule_ProvideSceneLoaderFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SceneLoader get() {
    return provideSceneLoader(contextProvider.get());
  }

  public static SceneModule_ProvideSceneLoaderFactory create(Provider<Context> contextProvider) {
    return new SceneModule_ProvideSceneLoaderFactory(contextProvider);
  }

  public static SceneLoader provideSceneLoader(Context context) {
    return Preconditions.checkNotNullFromProvides(SceneModule.INSTANCE.provideSceneLoader(context));
  }
}
