// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.domain.location.LocationManager;
import com.livewallpaper.core.domain.time.WallpaperTimeManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class TimeModule_ProvideWallpaperTimeManagerFactory implements Factory<WallpaperTimeManager> {
  private final Provider<LocationManager> locationManagerProvider;

  public TimeModule_ProvideWallpaperTimeManagerFactory(
      Provider<LocationManager> locationManagerProvider) {
    this.locationManagerProvider = locationManagerProvider;
  }

  @Override
  public WallpaperTimeManager get() {
    return provideWallpaperTimeManager(locationManagerProvider.get());
  }

  public static TimeModule_ProvideWallpaperTimeManagerFactory create(
      Provider<LocationManager> locationManagerProvider) {
    return new TimeModule_ProvideWallpaperTimeManagerFactory(locationManagerProvider);
  }

  public static WallpaperTimeManager provideWallpaperTimeManager(LocationManager locationManager) {
    return Preconditions.checkNotNullFromProvides(TimeModule.INSTANCE.provideWallpaperTimeManager(locationManager));
  }
}
