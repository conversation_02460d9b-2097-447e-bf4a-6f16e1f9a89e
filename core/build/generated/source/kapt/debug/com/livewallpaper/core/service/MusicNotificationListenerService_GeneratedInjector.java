package com.livewallpaper.core.service;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = MusicNotificationListenerService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
public interface MusicNotificationListenerService_GeneratedInjector {
  void injectMusicNotificationListenerService(
      MusicNotificationListenerService musicNotificationListenerService);
}
