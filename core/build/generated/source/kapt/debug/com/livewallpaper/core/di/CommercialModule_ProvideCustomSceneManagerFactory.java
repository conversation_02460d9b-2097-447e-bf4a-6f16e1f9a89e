// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import android.content.Context;
import com.livewallpaper.core.data.repository.SceneRepository;
import com.livewallpaper.core.domain.scene.CustomSceneManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class CommercialModule_ProvideCustomSceneManagerFactory implements Factory<CustomSceneManager> {
  private final Provider<Context> contextProvider;

  private final Provider<SceneRepository> sceneRepositoryProvider;

  public CommercialModule_ProvideCustomSceneManagerFactory(Provider<Context> contextProvider,
      Provider<SceneRepository> sceneRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.sceneRepositoryProvider = sceneRepositoryProvider;
  }

  @Override
  public CustomSceneManager get() {
    return provideCustomSceneManager(contextProvider.get(), sceneRepositoryProvider.get());
  }

  public static CommercialModule_ProvideCustomSceneManagerFactory create(
      Provider<Context> contextProvider, Provider<SceneRepository> sceneRepositoryProvider) {
    return new CommercialModule_ProvideCustomSceneManagerFactory(contextProvider, sceneRepositoryProvider);
  }

  public static CustomSceneManager provideCustomSceneManager(Context context,
      SceneRepository sceneRepository) {
    return Preconditions.checkNotNullFromProvides(CommercialModule.INSTANCE.provideCustomSceneManager(context, sceneRepository));
  }
}
