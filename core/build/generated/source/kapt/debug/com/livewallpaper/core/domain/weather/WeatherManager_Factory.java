// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.domain.weather;

import com.livewallpaper.core.data.repository.WeatherRepository;
import com.livewallpaper.core.domain.location.LocationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class WeatherManager_Factory implements Factory<WeatherManager> {
  private final Provider<WeatherRepository> weatherRepositoryProvider;

  private final Provider<LocationManager> locationManagerProvider;

  public WeatherManager_Factory(Provider<WeatherRepository> weatherRepositoryProvider,
      Provider<LocationManager> locationManagerProvider) {
    this.weatherRepositoryProvider = weatherRepositoryProvider;
    this.locationManagerProvider = locationManagerProvider;
  }

  @Override
  public WeatherManager get() {
    return newInstance(weatherRepositoryProvider.get(), locationManagerProvider.get());
  }

  public static WeatherManager_Factory create(Provider<WeatherRepository> weatherRepositoryProvider,
      Provider<LocationManager> locationManagerProvider) {
    return new WeatherManager_Factory(weatherRepositoryProvider, locationManagerProvider);
  }

  public static WeatherManager newInstance(WeatherRepository weatherRepository,
      LocationManager locationManager) {
    return new WeatherManager(weatherRepository, locationManager);
  }
}
