// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.livewallpaper.core.di;

import com.livewallpaper.core.data.database.WallpaperDatabase;
import com.livewallpaper.core.data.database.dao.SceneDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideSceneDaoFactory implements Factory<SceneDao> {
  private final Provider<WallpaperDatabase> databaseProvider;

  public DatabaseModule_ProvideSceneDaoFactory(Provider<WallpaperDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public SceneDao get() {
    return provideSceneDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideSceneDaoFactory create(
      Provider<WallpaperDatabase> databaseProvider) {
    return new DatabaseModule_ProvideSceneDaoFactory(databaseProvider);
  }

  public static SceneDao provideSceneDao(WallpaperDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSceneDao(database));
  }
}
