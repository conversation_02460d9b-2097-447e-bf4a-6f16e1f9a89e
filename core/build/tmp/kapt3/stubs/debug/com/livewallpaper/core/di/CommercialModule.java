package com.livewallpaper.core.di;

/**
 * 商业化功能依赖注入模块
 * 提供计费、广告、自定义场景和应用更新相关的依赖
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u001a\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\nH\u0007J\u0012\u0010\u000b\u001a\u00020\f2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u001a\u0010\r\u001a\u00020\u000e2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u00a8\u0006\u0011"}, d2 = {"Lcom/livewallpaper/core/di/CommercialModule;", "", "()V", "provideAdManager", "Lcom/livewallpaper/core/domain/ads/AdManager;", "context", "Landroid/content/Context;", "provideAppUpdateChecker", "Lcom/livewallpaper/core/domain/update/AppUpdateChecker;", "httpClient", "Lokhttp3/OkHttpClient;", "provideBillingManager", "Lcom/livewallpaper/core/domain/billing/BillingManager;", "provideCustomSceneManager", "Lcom/livewallpaper/core/domain/scene/CustomSceneManager;", "sceneRepository", "Lcom/livewallpaper/core/data/repository/SceneRepository;", "core_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class CommercialModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.di.CommercialModule INSTANCE = null;
    
    private CommercialModule() {
        super();
    }
    
    /**
     * 提供计费管理器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.billing.BillingManager provideBillingManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 提供广告管理器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.ads.AdManager provideAdManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 提供自定义场景管理器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.CustomSceneManager provideCustomSceneManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SceneRepository sceneRepository) {
        return null;
    }
    
    /**
     * 提供应用更新检查器
     */
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.update.AppUpdateChecker provideAppUpdateChecker(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient httpClient) {
        return null;
    }
}