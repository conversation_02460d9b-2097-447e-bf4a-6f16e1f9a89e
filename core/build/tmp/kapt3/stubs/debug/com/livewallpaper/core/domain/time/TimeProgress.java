package com.livewallpaper.core.domain.time;

/**
 * 时间进度数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0013\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\tH\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J=\u0010\u0019\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u001a\u001a\u00020\t2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010\u00a8\u0006 "}, d2 = {"Lcom/livewallpaper/core/domain/time/TimeProgress;", "", "progress", "", "phase", "Lcom/livewallpaper/core/domain/time/TimePhase;", "solarEvents", "Lcom/livewallpaper/core/domain/time/SolarEvents;", "isDay", "", "sunElevation", "(DLcom/livewallpaper/core/domain/time/TimePhase;Lcom/livewallpaper/core/domain/time/SolarEvents;ZD)V", "()Z", "getPhase", "()Lcom/livewallpaper/core/domain/time/TimePhase;", "getProgress", "()D", "getSolarEvents", "()Lcom/livewallpaper/core/domain/time/SolarEvents;", "getSunElevation", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "", "core_debug"})
public final class TimeProgress {
    private final double progress = 0.0;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.time.TimePhase phase = null;
    @org.jetbrains.annotations.Nullable()
    private final com.livewallpaper.core.domain.time.SolarEvents solarEvents = null;
    private final boolean isDay = false;
    private final double sunElevation = 0.0;
    
    public TimeProgress(double progress, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.TimePhase phase, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.domain.time.SolarEvents solarEvents, boolean isDay, double sunElevation) {
        super();
    }
    
    public final double getProgress() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.time.TimePhase getPhase() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.domain.time.SolarEvents getSolarEvents() {
        return null;
    }
    
    public final boolean isDay() {
        return false;
    }
    
    public final double getSunElevation() {
        return 0.0;
    }
    
    public final double component1() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.time.TimePhase component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.domain.time.SolarEvents component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.time.TimeProgress copy(double progress, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.TimePhase phase, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.domain.time.SolarEvents solarEvents, boolean isDay, double sunElevation) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}