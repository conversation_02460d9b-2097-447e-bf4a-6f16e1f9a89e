package com.livewallpaper.core.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u001e\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\u0006\u0010\u0011\u001a\u00020\u0012\u0012\u0006\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\u0002\u0010\u0015J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010*\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\t\u0010+\u001a\u00020\nH\u00c6\u0003J\t\u0010,\u001a\u00020\fH\u00c6\u0003J\t\u0010-\u001a\u00020\u000eH\u00c6\u0003J\t\u0010.\u001a\u00020\u0010H\u00c6\u0003J\t\u0010/\u001a\u00020\u0012H\u00c6\u0003J\t\u00100\u001a\u00020\u0014H\u00c6\u0003Ji\u00101\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u00c6\u0001J\u0013\u00102\u001a\u0002032\b\u00104\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00105\u001a\u00020\u000eH\u00d6\u0001J\t\u00106\u001a\u00020\u0014H\u00d6\u0001R\u0016\u0010\t\u001a\u00020\n8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0016\u0010\u0013\u001a\u00020\u00148\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0016\u0010\u0004\u001a\u00020\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0016\u0010\u000f\u001a\u00020\u00108\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0016\u0010\u0011\u001a\u00020\u00128\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0016\u0010\r\u001a\u00020\u000e8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u001c\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0016\u0010\u000b\u001a\u00020\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'\u00a8\u00067"}, d2 = {"Lcom/livewallpaper/core/data/model/ForecastItem;", "", "timestamp", "", "main", "Lcom/livewallpaper/core/data/model/MainWeatherData;", "weather", "", "Lcom/livewallpaper/core/data/model/WeatherInfo;", "clouds", "Lcom/livewallpaper/core/data/model/Clouds;", "wind", "Lcom/livewallpaper/core/data/model/Wind;", "visibility", "", "precipitationProbability", "", "sys", "Lcom/livewallpaper/core/data/model/ForecastSys;", "dateText", "", "(JLcom/livewallpaper/core/data/model/MainWeatherData;Ljava/util/List;Lcom/livewallpaper/core/data/model/Clouds;Lcom/livewallpaper/core/data/model/Wind;IDLcom/livewallpaper/core/data/model/ForecastSys;Ljava/lang/String;)V", "getClouds", "()Lcom/livewallpaper/core/data/model/Clouds;", "getDateText", "()Ljava/lang/String;", "getMain", "()Lcom/livewallpaper/core/data/model/MainWeatherData;", "getPrecipitationProbability", "()D", "getSys", "()Lcom/livewallpaper/core/data/model/ForecastSys;", "getTimestamp", "()J", "getVisibility", "()I", "getWeather", "()Ljava/util/List;", "getWind", "()Lcom/livewallpaper/core/data/model/Wind;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "core_debug"})
public final class ForecastItem {
    @com.google.gson.annotations.SerializedName(value = "dt")
    private final long timestamp = 0L;
    @com.google.gson.annotations.SerializedName(value = "main")
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.MainWeatherData main = null;
    @com.google.gson.annotations.SerializedName(value = "weather")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.livewallpaper.core.data.model.WeatherInfo> weather = null;
    @com.google.gson.annotations.SerializedName(value = "clouds")
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.Clouds clouds = null;
    @com.google.gson.annotations.SerializedName(value = "wind")
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.Wind wind = null;
    @com.google.gson.annotations.SerializedName(value = "visibility")
    private final int visibility = 0;
    @com.google.gson.annotations.SerializedName(value = "pop")
    private final double precipitationProbability = 0.0;
    @com.google.gson.annotations.SerializedName(value = "sys")
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.ForecastSys sys = null;
    @com.google.gson.annotations.SerializedName(value = "dt_txt")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dateText = null;
    
    public ForecastItem(long timestamp, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.MainWeatherData main, @org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.WeatherInfo> weather, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Clouds clouds, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Wind wind, int visibility, double precipitationProbability, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.ForecastSys sys, @org.jetbrains.annotations.NotNull()
    java.lang.String dateText) {
        super();
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.MainWeatherData getMain() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.livewallpaper.core.data.model.WeatherInfo> getWeather() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.Clouds getClouds() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.Wind getWind() {
        return null;
    }
    
    public final int getVisibility() {
        return 0;
    }
    
    public final double getPrecipitationProbability() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.ForecastSys getSys() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDateText() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.MainWeatherData component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.livewallpaper.core.data.model.WeatherInfo> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.Clouds component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.Wind component5() {
        return null;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.ForecastSys component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.ForecastItem copy(long timestamp, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.MainWeatherData main, @org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.WeatherInfo> weather, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Clouds clouds, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Wind wind, int visibility, double precipitationProbability, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.ForecastSys sys, @org.jetbrains.annotations.NotNull()
    java.lang.String dateText) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}