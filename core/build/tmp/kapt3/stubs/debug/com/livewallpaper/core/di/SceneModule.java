package com.livewallpaper.core.di;

/**
 * 场景管理依赖注入模块
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\t\u001a\u00020\nH\u0007J0\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0007J\b\u0010\u0014\u001a\u00020\u0015H\u0007\u00a8\u0006\u0016"}, d2 = {"Lcom/livewallpaper/core/di/SceneModule;", "", "()V", "provideSceneInitializer", "Lcom/livewallpaper/core/domain/scene/SceneInitializer;", "sceneRepository", "Lcom/livewallpaper/core/data/repository/SceneRepository;", "provideSceneLoader", "Lcom/livewallpaper/core/domain/scene/SceneLoader;", "context", "Landroid/content/Context;", "provideSceneManager", "Lcom/livewallpaper/core/domain/scene/SceneManager;", "sceneLoader", "timeManager", "Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "weatherManager", "Lcom/livewallpaper/core/domain/weather/WeatherManager;", "preferences", "Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;", "provideSceneRenderer", "Lcom/livewallpaper/core/domain/scene/SceneRenderer;", "core_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class SceneModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.di.SceneModule INSTANCE = null;
    
    private SceneModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneLoader provideSceneLoader(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneRenderer provideSceneRenderer() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneManager provideSceneManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SceneRepository sceneRepository, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneLoader sceneLoader, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeManager timeManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherManager weatherManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.preferences.WallpaperPreferences preferences) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.scene.SceneInitializer provideSceneInitializer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SceneRepository sceneRepository) {
        return null;
    }
}