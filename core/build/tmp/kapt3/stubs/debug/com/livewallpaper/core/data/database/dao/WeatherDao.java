package com.livewallpaper.core.data.database.dao;

/**
 * 天气数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\f\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u00120\u0011H\'J\u0010\u0010\u0013\u001a\u0004\u0018\u00010\nH\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\n2\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00120\u00112\u0006\u0010\r\u001a\u00020\u000eH\'J,\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00120\u00112\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001a\u001a\u00020\u0005H\'J\u0016\u0010\u001b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001d\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u001c\u0010\u001f\u001a\u00020\u00032\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00170\u0012H\u00a7@\u00a2\u0006\u0002\u0010!J\u0016\u0010\"\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006#"}, d2 = {"Lcom/livewallpaper/core/data/database/dao/WeatherDao;", "", "deleteOldWeather", "", "timestamp", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldWeatherForecast", "deleteWeather", "weather", "Lcom/livewallpaper/core/data/model/Weather;", "(Lcom/livewallpaper/core/data/model/Weather;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteWeatherForecastByLocation", "location", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllWeather", "Lkotlinx/coroutines/flow/Flow;", "", "getCurrentLocationWeather", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWeatherByLocation", "getWeatherForecast", "Lcom/livewallpaper/core/data/model/WeatherForecast;", "getWeatherForecastByDateRange", "startDate", "endDate", "insertWeather", "insertWeatherForecast", "forecast", "(Lcom/livewallpaper/core/data/model/WeatherForecast;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertWeatherForecasts", "forecasts", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWeather", "core_debug"})
@androidx.room.Dao()
public abstract interface WeatherDao {
    
    @androidx.room.Query(value = "SELECT * FROM weather WHERE isCurrentLocation = 1 ORDER BY timestamp DESC LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCurrentLocationWeather(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Weather> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM weather WHERE location = :location ORDER BY timestamp DESC LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWeatherByLocation(@org.jetbrains.annotations.NotNull()
    java.lang.String location, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Weather> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM weather ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Weather>> getAllWeather();
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWeather(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Weather weather, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWeather(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Weather weather, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWeather(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Weather weather, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM weather WHERE timestamp < :timestamp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldWeather(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM weather_forecast WHERE location = :location ORDER BY date ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>> getWeatherForecast(@org.jetbrains.annotations.NotNull()
    java.lang.String location);
    
    @androidx.room.Query(value = "SELECT * FROM weather_forecast WHERE location = :location AND date >= :startDate AND date <= :endDate ORDER BY date ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>> getWeatherForecastByDateRange(@org.jetbrains.annotations.NotNull()
    java.lang.String location, long startDate, long endDate);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWeatherForecast(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WeatherForecast forecast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWeatherForecasts(@org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.WeatherForecast> forecasts, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM weather_forecast WHERE timestamp < :timestamp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldWeatherForecast(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM weather_forecast WHERE location = :location")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWeatherForecastByLocation(@org.jetbrains.annotations.NotNull()
    java.lang.String location, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}