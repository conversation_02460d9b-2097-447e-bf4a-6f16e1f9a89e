package com.livewallpaper.core.data.database.dao;

/**
 * 场景数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0014\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u000e\u0010\u0012\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0016\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0018\u001a\u00020\u0019H\'J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001b\u001a\u00020\u001cH\'J\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001e\u001a\u00020\u001fH\'J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010!\u001a\u00020\"H\'J\u0016\u0010#\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010$\u001a\u00020\u00032\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00070\u000fH\u00a7@\u00a2\u0006\u0002\u0010&J\u001c\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010(\u001a\u00020\u000bH\'J\u0016\u0010)\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006*"}, d2 = {"Lcom/livewallpaper/core/data/database/dao/SceneDao;", "", "deleteAllCustomScenes", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteScene", "scene", "Lcom/livewallpaper/core/data/model/Scene;", "(Lcom/livewallpaper/core/data/model/Scene;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSceneById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllScenes", "Lkotlinx/coroutines/flow/Flow;", "", "getCustomScenes", "getFreeScenes", "getPremiumSceneCount", "", "getPremiumScenes", "getSceneById", "getSceneCount", "getScenesByCategory", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "getScenesBySeason", "season", "Lcom/livewallpaper/core/data/model/Season;", "getScenesByTimeOfDay", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "getScenesByWeatherType", "weatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "insertScene", "insertScenes", "scenes", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchScenes", "query", "updateScene", "core_debug"})
@androidx.room.Dao()
public abstract interface SceneDao {
    
    @androidx.room.Query(value = "SELECT * FROM scenes ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getAllScenes();
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSceneById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Scene> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE category = :category ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getScenesByCategory(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category);
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE season = :season ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getScenesBySeason(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Season season);
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE timeOfDay = :timeOfDay ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getScenesByTimeOfDay(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay);
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE weatherType = :weatherType ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getScenesByWeatherType(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WeatherType weatherType);
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE isPremium = 0 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getFreeScenes();
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE isPremium = 1 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getPremiumScenes();
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE isCustom = 1 ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> getCustomScenes();
    
    @androidx.room.Query(value = "SELECT * FROM scenes WHERE name LIKE \'%\' || :query || \'%\' OR description LIKE \'%\' || :query || \'%\' ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.livewallpaper.core.data.model.Scene>> searchScenes(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertScenes(@org.jetbrains.annotations.NotNull()
    java.util.List<com.livewallpaper.core.data.model.Scene> scenes, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM scenes WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSceneById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM scenes WHERE isCustom = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllCustomScenes(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM scenes")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSceneCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM scenes WHERE isPremium = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPremiumSceneCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}