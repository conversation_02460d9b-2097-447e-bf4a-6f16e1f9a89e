package com.livewallpaper.core.di;

/**
 * 音乐系统依赖注入模块
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\b\u0010\u0005\u001a\u00020\u0006H\u0007J\u001a\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0006H\u0007\u00a8\u0006\f"}, d2 = {"Lcom/livewallpaper/core/di/MusicModule;", "", "()V", "provideMusicCardRenderer", "Lcom/livewallpaper/core/domain/music/MusicCardRenderer;", "provideMusicDataBroadcaster", "Lcom/livewallpaper/core/service/MusicDataBroadcaster;", "provideMusicManager", "Lcom/livewallpaper/core/domain/music/MusicManager;", "context", "Landroid/content/Context;", "musicDataBroadcaster", "core_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class MusicModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.di.MusicModule INSTANCE = null;
    
    private MusicModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.service.MusicDataBroadcaster provideMusicDataBroadcaster() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.music.MusicManager provideMusicManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.service.MusicDataBroadcaster musicDataBroadcaster) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.music.MusicCardRenderer provideMusicCardRenderer() {
        return null;
    }
}