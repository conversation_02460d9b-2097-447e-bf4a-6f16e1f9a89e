package com.livewallpaper.core.domain.scene;

/**
 * 自定义场景管理器
 * 处理用户自定义场景的创建、编辑和管理
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J \u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\nH\u0002JP\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u00182\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0086@\u00a2\u0006\u0002\u0010\u001dJF\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u001f\u001a\u00020\u00132\n\b\u0002\u0010 \u001a\u0004\u0018\u00010\u00132\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\"J\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020$0\u00102\u0006\u0010%\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010&J\u0010\u0010\'\u001a\u00020$2\u0006\u0010(\u001a\u00020\u0015H\u0002J@\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010%\u001a\u00020\u00152\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u0013H\u0086@\u00a2\u0006\u0002\u0010-J\u001a\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110/0\u0010H\u0086@\u00a2\u0006\u0002\u00100J\b\u00101\u001a\u000202H\u0002J\u0018\u00103\u001a\u0004\u0018\u0001042\u0006\u00105\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u00106J \u00107\u001a\u0004\u0018\u00010\u00152\u0006\u00108\u001a\u0002042\u0006\u00109\u001a\u00020\u0015H\u0082@\u00a2\u0006\u0002\u0010:R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006;"}, d2 = {"Lcom/livewallpaper/core/domain/scene/CustomSceneManager;", "", "context", "Landroid/content/Context;", "sceneRepository", "Lcom/livewallpaper/core/data/repository/SceneRepository;", "(Landroid/content/Context;Lcom/livewallpaper/core/data/repository/SceneRepository;)V", "customScenesDir", "Ljava/io/File;", "calculateInSampleSize", "", "options", "Landroid/graphics/BitmapFactory$Options;", "reqWidth", "reqHeight", "createCustomSceneFromGallery", "Lcom/livewallpaper/core/utils/Resource;", "Lcom/livewallpaper/core/data/model/Scene;", "imageUri", "Landroid/net/Uri;", "sceneName", "", "description", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "season", "Lcom/livewallpaper/core/data/model/Season;", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "(Landroid/net/Uri;Ljava/lang/String;Ljava/lang/String;Lcom/livewallpaper/core/data/model/SceneCategory;Lcom/livewallpaper/core/data/model/Season;Lcom/livewallpaper/core/data/model/TimeOfDay;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createLayeredCustomScene", "backgroundUri", "middlegroundUri", "foregroundUri", "(Landroid/net/Uri;Landroid/net/Uri;Landroid/net/Uri;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCustomScene", "", "sceneId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteImageFile", "imagePath", "editCustomScene", "newName", "newDescription", "newBackgroundUri", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroid/net/Uri;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllCustomScenes", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "hasStoragePermission", "", "processImageFromUri", "Landroid/graphics/Bitmap;", "uri", "(Landroid/net/Uri;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveImageToCustomDir", "bitmap", "fileName", "(Landroid/graphics/Bitmap;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "core_debug"})
public final class CustomSceneManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.repository.SceneRepository sceneRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final java.io.File customScenesDir = null;
    
    @javax.inject.Inject()
    public CustomSceneManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SceneRepository sceneRepository) {
        super();
    }
    
    /**
     * 从用户相册创建自定义场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createCustomSceneFromGallery(@org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri, @org.jetbrains.annotations.NotNull()
    java.lang.String sceneName, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.Season season, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Scene>> $completion) {
        return null;
    }
    
    /**
     * 创建分层自定义场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createLayeredCustomScene(@org.jetbrains.annotations.NotNull()
    android.net.Uri backgroundUri, @org.jetbrains.annotations.Nullable()
    android.net.Uri middlegroundUri, @org.jetbrains.annotations.Nullable()
    android.net.Uri foregroundUri, @org.jetbrains.annotations.NotNull()
    java.lang.String sceneName, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Scene>> $completion) {
        return null;
    }
    
    /**
     * 编辑自定义场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object editCustomScene(@org.jetbrains.annotations.NotNull()
    java.lang.String sceneId, @org.jetbrains.annotations.Nullable()
    java.lang.String newName, @org.jetbrains.annotations.Nullable()
    java.lang.String newDescription, @org.jetbrains.annotations.Nullable()
    android.net.Uri newBackgroundUri, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Scene>> $completion) {
        return null;
    }
    
    /**
     * 删除自定义场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteCustomScene(@org.jetbrains.annotations.NotNull()
    java.lang.String sceneId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 获取所有自定义场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllCustomScenes(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> $completion) {
        return null;
    }
    
    /**
     * 从URI处理图片
     */
    private final java.lang.Object processImageFromUri(android.net.Uri uri, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 保存图片到自定义目录
     */
    private final java.lang.Object saveImageToCustomDir(android.graphics.Bitmap bitmap, java.lang.String fileName, kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * 删除图片文件
     */
    private final void deleteImageFile(java.lang.String imagePath) {
    }
    
    /**
     * 计算采样率
     */
    private final int calculateInSampleSize(android.graphics.BitmapFactory.Options options, int reqWidth, int reqHeight) {
        return 0;
    }
    
    /**
     * 检查存储权限
     */
    private final boolean hasStoragePermission() {
        return false;
    }
}