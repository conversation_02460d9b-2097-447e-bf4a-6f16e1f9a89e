package com.livewallpaper.core.data.database;

/**
 * Room数据库类型转换器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0014\u0010\u0007\u001a\u0004\u0018\u00010\u00042\b\u0010\b\u001a\u0004\u0018\u00010\tH\u0007J\u0014\u0010\n\u001a\u0004\u0018\u00010\u00042\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0007J\u0014\u0010\r\u001a\u0004\u0018\u00010\u00042\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0007J\u0010\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0007J\u0014\u0010\u0011\u001a\u0004\u0018\u00010\t2\b\u0010\b\u001a\u0004\u0018\u00010\u0004H\u0007J\u0014\u0010\u0012\u001a\u0004\u0018\u00010\f2\b\u0010\u000b\u001a\u0004\u0018\u00010\u0004H\u0007J\u0014\u0010\u0013\u001a\u0004\u0018\u00010\u000f2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0004H\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/livewallpaper/core/data/database/Converters;", "", "()V", "fromSceneCategory", "", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "fromSeason", "season", "Lcom/livewallpaper/core/data/model/Season;", "fromTimeOfDay", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "fromWeatherType", "weatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "toSceneCategory", "toSeason", "toTimeOfDay", "toWeatherType", "core_debug"})
public final class Converters {
    
    public Converters() {
        super();
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromSceneCategory(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.SceneCategory toSceneCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromSeason(@org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.Season season) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.Season toSeason(@org.jetbrains.annotations.Nullable()
    java.lang.String season) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromTimeOfDay(@org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.TimeOfDay toTimeOfDay(@org.jetbrains.annotations.Nullable()
    java.lang.String timeOfDay) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromWeatherType(@org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.WeatherType weatherType) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.WeatherType toWeatherType(@org.jetbrains.annotations.Nullable()
    java.lang.String weatherType) {
        return null;
    }
}