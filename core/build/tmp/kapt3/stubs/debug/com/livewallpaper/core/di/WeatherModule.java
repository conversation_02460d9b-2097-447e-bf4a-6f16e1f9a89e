package com.livewallpaper.core.di;

/**
 * 天气系统依赖注入模块
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007J \u0010\u000b\u001a\u00020\b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/livewallpaper/core/di/WeatherModule;", "", "()V", "provideWeatherEffectRenderer", "Lcom/livewallpaper/core/domain/weather/WeatherEffectRenderer;", "provideWeatherManager", "Lcom/livewallpaper/core/domain/weather/WeatherManager;", "weatherRepository", "Lcom/livewallpaper/core/data/repository/WeatherRepository;", "locationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "provideWeatherRepository", "weatherApiService", "Lcom/livewallpaper/core/data/network/WeatherApiService;", "weatherDao", "Lcom/livewallpaper/core/data/database/dao/WeatherDao;", "preferences", "Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;", "core_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class WeatherModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.di.WeatherModule INSTANCE = null;
    
    private WeatherModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.repository.WeatherRepository provideWeatherRepository(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.network.WeatherApiService weatherApiService, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.database.dao.WeatherDao weatherDao, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.preferences.WallpaperPreferences preferences) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.weather.WeatherManager provideWeatherManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.WeatherRepository weatherRepository, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.weather.WeatherEffectRenderer provideWeatherEffectRenderer() {
        return null;
    }
}