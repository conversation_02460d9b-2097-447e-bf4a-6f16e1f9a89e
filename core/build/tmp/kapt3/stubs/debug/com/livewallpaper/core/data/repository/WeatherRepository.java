package com.livewallpaper.core.data.repository;

/**
 * 天气数据仓库
 * 负责管理天气数据的获取、缓存和同步
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\nH\u0082@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\f\u001a\u00020\rH\u0082@\u00a2\u0006\u0002\u0010\u000bJ\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0086@\u00a2\u0006\u0002\u0010\u000bJ.\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000f0\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00132\b\b\u0002\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\u00112\u0006\u0010\u0019\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u001aJ\"\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00110\u001c2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0013J4\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u00132\b\b\u0002\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u000e\u0010 \u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0018\u0010!\u001a\u00020\u00162\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020#H\u0002J\u0016\u0010%\u001a\u00020\n2\u0006\u0010&\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u001aR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/livewallpaper/core/data/repository/WeatherRepository;", "", "weatherApiService", "Lcom/livewallpaper/core/data/network/WeatherApiService;", "weatherDao", "Lcom/livewallpaper/core/data/database/dao/WeatherDao;", "preferences", "Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;", "(Lcom/livewallpaper/core/data/network/WeatherApiService;Lcom/livewallpaper/core/data/database/dao/WeatherDao;Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;)V", "cleanupOldWeatherData", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getApiKey", "", "getCachedWeather", "Lcom/livewallpaper/core/data/model/Weather;", "getCurrentWeather", "Lcom/livewallpaper/core/utils/Resource;", "latitude", "", "longitude", "forceRefresh", "", "(DDZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWeatherByCity", "cityName", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWeatherFlow", "Lkotlinx/coroutines/flow/Flow;", "getWeatherForecast", "", "Lcom/livewallpaper/core/data/model/WeatherForecast;", "isApiKeyConfigured", "isCacheValid", "timestamp", "", "cacheDuration", "setApiKey", "apiKey", "core_debug"})
public final class WeatherRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.network.WeatherApiService weatherApiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.database.dao.WeatherDao weatherDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.preferences.WallpaperPreferences preferences = null;
    
    @javax.inject.Inject()
    public WeatherRepository(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.network.WeatherApiService weatherApiService, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.database.dao.WeatherDao weatherDao, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.preferences.WallpaperPreferences preferences) {
        super();
    }
    
    /**
     * 获取当前位置的天气信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentWeather(double latitude, double longitude, boolean forceRefresh, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Weather>> $completion) {
        return null;
    }
    
    /**
     * 根据城市名获取天气信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWeatherByCity(@org.jetbrains.annotations.NotNull()
    java.lang.String cityName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Weather>> $completion) {
        return null;
    }
    
    /**
     * 获取天气预报
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWeatherForecast(double latitude, double longitude, boolean forceRefresh, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>>> $completion) {
        return null;
    }
    
    /**
     * 获取天气数据流（包含缓存和网络数据）
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Weather>> getWeatherFlow(double latitude, double longitude) {
        return null;
    }
    
    /**
     * 获取API密钥
     */
    private final java.lang.Object getApiKey(kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * 检查缓存是否有效
     */
    private final boolean isCacheValid(long timestamp, long cacheDuration) {
        return false;
    }
    
    /**
     * 清理旧的天气数据
     */
    private final java.lang.Object cleanupOldWeatherData(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 设置API密钥
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setApiKey(@org.jetbrains.annotations.NotNull()
    java.lang.String apiKey, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取缓存的天气数据
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCachedWeather(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Weather> $completion) {
        return null;
    }
    
    /**
     * 检查API密钥是否已配置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isApiKeyConfigured(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}