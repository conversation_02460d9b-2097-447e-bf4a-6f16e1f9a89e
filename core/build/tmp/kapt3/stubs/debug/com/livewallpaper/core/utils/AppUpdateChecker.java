package com.livewallpaper.core.utils;

/**
 * 应用更新检查器
 * 负责检查应用更新和版本管理
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\b\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eJ\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0082@\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u0012\u001a\u00020\u0013J\u0006\u0010\u0014\u001a\u00020\u0011J\u0006\u0010\u0015\u001a\u00020\u000eJ\u0006\u0010\u0016\u001a\u00020\u0017J\u0006\u0010\u0018\u001a\u00020\u0019R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0007\u00a8\u0006\u001a"}, d2 = {"Lcom/livewallpaper/core/utils/AppUpdateChecker;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "json", "error/NonExistentClass", "Lerror/NonExistentClass;", "checkForUpdates", "Lcom/livewallpaper/core/utils/UpdateCheckResult;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "compareVersions", "", "version1", "", "version2", "fetchLatestVersion", "Lcom/livewallpaper/core/data/model/AppVersion;", "getAppInfo", "Lcom/livewallpaper/core/utils/AppInfo;", "getCurrentVersion", "getInstallSource", "isDebugBuild", "", "openAppStore", "", "core_debug"})
public final class AppUpdateChecker {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass json = null;
    
    @javax.inject.Inject()
    public AppUpdateChecker(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 获取当前应用版本信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.AppVersion getCurrentVersion() {
        return null;
    }
    
    /**
     * 检查应用更新
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object checkForUpdates(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.UpdateCheckResult> $completion) {
        return null;
    }
    
    /**
     * 获取最新版本信息
     */
    private final java.lang.Object fetchLatestVersion(kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.AppVersion> $completion) {
        return null;
    }
    
    /**
     * 打开应用商店页面
     */
    public final void openAppStore() {
    }
    
    /**
     * 获取应用安装来源
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getInstallSource() {
        return null;
    }
    
    /**
     * 检查是否为调试版本
     */
    public final boolean isDebugBuild() {
        return false;
    }
    
    /**
     * 获取应用详细信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.utils.AppInfo getAppInfo() {
        return null;
    }
    
    /**
     * 比较版本号
     */
    public final int compareVersions(@org.jetbrains.annotations.NotNull()
    java.lang.String version1, @org.jetbrains.annotations.NotNull()
    java.lang.String version2) {
        return 0;
    }
}