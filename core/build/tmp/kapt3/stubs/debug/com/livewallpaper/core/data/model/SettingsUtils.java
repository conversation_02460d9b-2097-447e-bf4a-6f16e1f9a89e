package com.livewallpaper.core.data.model;

/**
 * 设置工具类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\u0001J\u0018\u0010\b\u001a\u00020\t2\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\u0001\u00a8\u0006\n"}, d2 = {"Lcom/livewallpaper/core/data/model/SettingsUtils;", "", "()V", "formatValue", "", "item", "Lcom/livewallpaper/core/data/model/SettingItem;", "value", "validateSetting", "Lcom/livewallpaper/core/data/model/SettingValidationResult;", "core_debug"})
public final class SettingsUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.model.SettingsUtils INSTANCE = null;
    
    private SettingsUtils() {
        super();
    }
    
    /**
     * 验证设置值
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.SettingValidationResult validateSetting(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SettingItem item, @org.jetbrains.annotations.Nullable()
    java.lang.Object value) {
        return null;
    }
    
    /**
     * 格式化设置值显示
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatValue(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SettingItem item, @org.jetbrains.annotations.Nullable()
    java.lang.Object value) {
        return null;
    }
}