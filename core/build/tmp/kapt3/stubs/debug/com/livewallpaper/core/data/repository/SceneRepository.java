package com.livewallpaper.core.data.repository;

/**
 * 场景数据仓库
 * 负责管理场景数据的获取、存储和缓存
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002J\u001c\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u000b\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\r\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000eJ\u0018\u0010\u000f\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000eJ\u0018\u0010\u0010\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000eJ\u001c\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\t2\u0006\u0010\u0012\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J \u0010\u0015\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000e2\u0006\u0010\u0016\u001a\u00020\u0017J \u0010\u0018\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000e2\u0006\u0010\u0019\u001a\u00020\u001aJ \u0010\u001b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000e2\u0006\u0010\u001c\u001a\u00020\u001dJ \u0010\u001e\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000e2\u0006\u0010\u001f\u001a\u00020 J\u0014\u0010!\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0086@\u00a2\u0006\u0002\u0010\"J\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u000b\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\fJ \u0010$\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\t0\u000e2\u0006\u0010%\u001a\u00020\u0013J\u001c\u0010&\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u000b\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/livewallpaper/core/data/repository/SceneRepository;", "", "sceneDao", "Lcom/livewallpaper/core/data/database/dao/SceneDao;", "(Lcom/livewallpaper/core/data/database/dao/SceneDao;)V", "createDefaultScenes", "", "Lcom/livewallpaper/core/data/model/Scene;", "deleteScene", "Lcom/livewallpaper/core/utils/Resource;", "", "scene", "(Lcom/livewallpaper/core/data/model/Scene;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllScenes", "Lkotlinx/coroutines/flow/Flow;", "getCustomScenes", "getFreeScenes", "getSceneById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getScenesByCategory", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "getScenesBySeason", "season", "Lcom/livewallpaper/core/data/model/Season;", "getScenesByTimeOfDay", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "getScenesByWeatherType", "weatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "initializeDefaultScenes", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertScene", "searchScenes", "query", "updateScene", "core_debug"})
public final class SceneRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.database.dao.SceneDao sceneDao = null;
    
    @javax.inject.Inject()
    public SceneRepository(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.database.dao.SceneDao sceneDao) {
        super();
    }
    
    /**
     * 获取所有场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getAllScenes() {
        return null;
    }
    
    /**
     * 根据ID获取场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSceneById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Scene>> $completion) {
        return null;
    }
    
    /**
     * 根据分类获取场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getScenesByCategory(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category) {
        return null;
    }
    
    /**
     * 根据季节获取场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getScenesBySeason(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Season season) {
        return null;
    }
    
    /**
     * 根据时间段获取场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getScenesByTimeOfDay(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    /**
     * 根据天气类型获取场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getScenesByWeatherType(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WeatherType weatherType) {
        return null;
    }
    
    /**
     * 获取免费场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getFreeScenes() {
        return null;
    }
    
    /**
     * 获取自定义场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> getCustomScenes() {
        return null;
    }
    
    /**
     * 搜索场景
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.Scene>>> searchScenes(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
    
    /**
     * 添加场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 更新场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 删除场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 初始化默认场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initializeDefaultScenes(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 创建默认场景列表
     */
    private final java.util.List<com.livewallpaper.core.data.model.Scene> createDefaultScenes() {
        return null;
    }
}