package com.livewallpaper.core.domain.time;

/**
 * 太阳事件数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b!\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001Bi\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\rJ\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0081\u0001\u0010#\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020*H\u00d6\u0001R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000fR\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000fR\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000fR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000f\u00a8\u0006+"}, d2 = {"Lcom/livewallpaper/core/domain/time/SolarEvents;", "", "sunrise", "Lkotlinx/datetime/Instant;", "sunset", "noon", "nadir", "civilTwilightDawn", "civilTwilightDusk", "nauticalTwilightDawn", "nauticalTwilightDusk", "astronomicalTwilightDawn", "astronomicalTwilightDusk", "(Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;Lkotlinx/datetime/Instant;)V", "getAstronomicalTwilightDawn", "()Lkotlinx/datetime/Instant;", "getAstronomicalTwilightDusk", "getCivilTwilightDawn", "getCivilTwilightDusk", "getNadir", "getNauticalTwilightDawn", "getNauticalTwilightDusk", "getNoon", "getSunrise", "getSunset", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "", "core_debug"})
public final class SolarEvents {
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant sunrise = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant sunset = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant noon = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant nadir = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant civilTwilightDawn = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant civilTwilightDusk = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant nauticalTwilightDawn = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant nauticalTwilightDusk = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant astronomicalTwilightDawn = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlinx.datetime.Instant astronomicalTwilightDusk = null;
    
    public SolarEvents(@org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant sunrise, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant sunset, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant noon, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant nadir, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant civilTwilightDawn, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant civilTwilightDusk, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant nauticalTwilightDawn, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant nauticalTwilightDusk, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant astronomicalTwilightDawn, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant astronomicalTwilightDusk) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getSunrise() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getSunset() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getNoon() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getNadir() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getCivilTwilightDawn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getCivilTwilightDusk() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getNauticalTwilightDawn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getNauticalTwilightDusk() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getAstronomicalTwilightDawn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant getAstronomicalTwilightDusk() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlinx.datetime.Instant component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.time.SolarEvents copy(@org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant sunrise, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant sunset, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant noon, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant nadir, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant civilTwilightDawn, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant civilTwilightDusk, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant nauticalTwilightDawn, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant nauticalTwilightDusk, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant astronomicalTwilightDawn, @org.jetbrains.annotations.Nullable()
    kotlinx.datetime.Instant astronomicalTwilightDusk) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}