package com.livewallpaper.core.di;

/**
 * 性能系统依赖注入模块
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u0007\u001a\u00020\u0006H\u0007J\u0012\u0010\b\u001a\u00020\t2\b\b\u0001\u0010\n\u001a\u00020\u000bH\u0007J\"\u0010\f\u001a\u00020\r2\b\b\u0001\u0010\n\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\t2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u00a8\u0006\u0011"}, d2 = {"Lcom/livewallpaper/core/di/PerformanceModule;", "", "()V", "provideDirtyRegionManager", "Lcom/livewallpaper/core/performance/DirtyRegionManager;", "objectPoolManager", "Lcom/livewallpaper/core/performance/ObjectPoolManager;", "provideObjectPoolManager", "providePerformanceMonitor", "Lcom/livewallpaper/core/performance/PerformanceMonitor;", "context", "Landroid/content/Context;", "providePerformanceOptimizer", "Lcom/livewallpaper/core/performance/PerformanceOptimizer;", "performanceMonitor", "settingsRepository", "Lcom/livewallpaper/core/data/repository/SettingsRepository;", "core_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class PerformanceModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.di.PerformanceModule INSTANCE = null;
    
    private PerformanceModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.PerformanceMonitor providePerformanceMonitor(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.ObjectPoolManager provideObjectPoolManager() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.DirtyRegionManager provideDirtyRegionManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager objectPoolManager) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.PerformanceOptimizer providePerformanceOptimizer(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.PerformanceMonitor performanceMonitor, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SettingsRepository settingsRepository) {
        return null;
    }
}