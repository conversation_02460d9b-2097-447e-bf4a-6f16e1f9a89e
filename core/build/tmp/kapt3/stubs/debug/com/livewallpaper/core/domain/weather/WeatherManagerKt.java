package com.livewallpaper.core.domain.weather;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\f\u0010\u0000\u001a\u0004\u0018\u00010\u0001*\u00020\u0002\u001a\u0014\u0010\u0003\u001a\u00020\u0004*\u00020\u00022\b\b\u0002\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"getWeatherOrNull", "Lcom/livewallpaper/core/data/model/Weather;", "Lcom/livewallpaper/core/domain/weather/WeatherState;", "getWeatherTypeOrDefault", "Lcom/livewallpaper/core/data/model/WeatherType;", "default", "core_debug"})
public final class WeatherManagerKt {
    
    /**
     * 天气状态扩展函数
     */
    @org.jetbrains.annotations.Nullable()
    public static final com.livewallpaper.core.data.model.Weather getWeatherOrNull(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherState $this$getWeatherOrNull) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.model.WeatherType getWeatherTypeOrDefault(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.weather.WeatherState $this$getWeatherTypeOrDefault, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WeatherType p1_772401952) {
        return null;
    }
}