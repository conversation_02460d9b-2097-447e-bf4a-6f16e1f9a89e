package com.livewallpaper.core.domain.weather;

/**
 * 天气管理器
 * 负责天气数据的获取、处理和状态管理
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\bJ\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150\u0014H\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\bJ\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\f0\u0019J\u000e\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u001c\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010 J\u0006\u0010!\u001a\u00020\u001bR\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/livewallpaper/core/domain/weather/WeatherManager;", "", "weatherRepository", "Lcom/livewallpaper/core/data/repository/WeatherRepository;", "locationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "(Lcom/livewallpaper/core/data/repository/WeatherRepository;Lcom/livewallpaper/core/domain/location/LocationManager;)V", "cachedWeather", "Lcom/livewallpaper/core/data/model/Weather;", "lastWeatherUpdate", "Lkotlinx/datetime/Instant;", "getCurrentWeatherState", "Lcom/livewallpaper/core/domain/weather/WeatherState;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentWeatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "getWeatherDescription", "", "weather", "getWeatherForecast", "Lcom/livewallpaper/core/utils/Resource;", "", "Lcom/livewallpaper/core/data/model/WeatherForecast;", "getWeatherIconResource", "getWeatherStateFlow", "Lkotlinx/coroutines/flow/Flow;", "isApiKeyConfigured", "", "refreshWeather", "setApiKey", "", "apiKey", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "shouldUpdateWeather", "core_debug"})
public final class WeatherManager {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.repository.WeatherRepository weatherRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.location.LocationManager locationManager = null;
    @org.jetbrains.annotations.NotNull()
    private kotlinx.datetime.Instant lastWeatherUpdate;
    @org.jetbrains.annotations.Nullable()
    private com.livewallpaper.core.data.model.Weather cachedWeather;
    
    @javax.inject.Inject()
    public WeatherManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.WeatherRepository weatherRepository, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        super();
    }
    
    /**
     * 获取当前天气状态
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentWeatherState(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.weather.WeatherState> $completion) {
        return null;
    }
    
    /**
     * 获取天气状态流
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.domain.weather.WeatherState> getWeatherStateFlow() {
        return null;
    }
    
    /**
     * 获取天气预报
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWeatherForecast(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.data.model.WeatherForecast>>> $completion) {
        return null;
    }
    
    /**
     * 强制刷新天气数据
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshWeather(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.weather.WeatherState> $completion) {
        return null;
    }
    
    /**
     * 获取当前天气类型（用于场景选择）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentWeatherType(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.WeatherType> $completion) {
        return null;
    }
    
    /**
     * 检查是否需要更新天气数据
     */
    public final boolean shouldUpdateWeather() {
        return false;
    }
    
    /**
     * 获取天气描述文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getWeatherDescription(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Weather weather) {
        return null;
    }
    
    /**
     * 获取天气图标资源ID
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getWeatherIconResource(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Weather weather) {
        return null;
    }
    
    /**
     * 检查API密钥是否已配置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isApiKeyConfigured(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * 设置API密钥
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setApiKey(@org.jetbrains.annotations.NotNull()
    java.lang.String apiKey, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}