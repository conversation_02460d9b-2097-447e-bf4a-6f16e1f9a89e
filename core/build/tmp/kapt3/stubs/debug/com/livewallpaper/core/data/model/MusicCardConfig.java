package com.livewallpaper.core.data.model;

/**
 * 音乐卡片显示配置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0018\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BK\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003JO\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010!\u001a\u00020\u00032\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020&H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015\u00a8\u0006\'"}, d2 = {"Lcom/livewallpaper/core/data/model/MusicCardConfig;", "", "showAlbumArt", "", "showProgress", "showControls", "cardOpacity", "", "cardPosition", "Lcom/livewallpaper/core/data/model/CardPosition;", "autoHideDelay", "", "enableVisualization", "(ZZZFLcom/livewallpaper/core/data/model/CardPosition;JZ)V", "getAutoHideDelay", "()J", "getCardOpacity", "()F", "getCardPosition", "()Lcom/livewallpaper/core/data/model/CardPosition;", "getEnableVisualization", "()Z", "getShowAlbumArt", "getShowControls", "getShowProgress", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "", "core_debug"})
public final class MusicCardConfig {
    private final boolean showAlbumArt = false;
    private final boolean showProgress = false;
    private final boolean showControls = false;
    private final float cardOpacity = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.CardPosition cardPosition = null;
    private final long autoHideDelay = 0L;
    private final boolean enableVisualization = false;
    
    public MusicCardConfig(boolean showAlbumArt, boolean showProgress, boolean showControls, float cardOpacity, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.CardPosition cardPosition, long autoHideDelay, boolean enableVisualization) {
        super();
    }
    
    public final boolean getShowAlbumArt() {
        return false;
    }
    
    public final boolean getShowProgress() {
        return false;
    }
    
    public final boolean getShowControls() {
        return false;
    }
    
    public final float getCardOpacity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.CardPosition getCardPosition() {
        return null;
    }
    
    public final long getAutoHideDelay() {
        return 0L;
    }
    
    public final boolean getEnableVisualization() {
        return false;
    }
    
    public MusicCardConfig() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.CardPosition component5() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.MusicCardConfig copy(boolean showAlbumArt, boolean showProgress, boolean showControls, float cardOpacity, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.CardPosition cardPosition, long autoHideDelay, boolean enableVisualization) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}