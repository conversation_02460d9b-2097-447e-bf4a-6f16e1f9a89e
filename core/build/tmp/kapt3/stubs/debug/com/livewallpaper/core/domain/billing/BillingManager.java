package com.livewallpaper.core.domain.billing;

/**
 * 计费管理器
 * 处理应用内购买和订阅
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u0000 $2\u00020\u0001:\u0001$B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0010\u001a\u00020\u0011J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00110\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018J$\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00110\u00132\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u0017\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u001a\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u001a\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u001e0\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u001c\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00160\u00132\u0006\u0010\"\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010#R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006%"}, d2 = {"Lcom/livewallpaper/core/domain/billing/BillingManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_billingState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/livewallpaper/core/domain/billing/BillingState;", "_purchaseState", "Lcom/livewallpaper/core/domain/billing/PurchaseState;", "billingState", "Lkotlinx/coroutines/flow/StateFlow;", "getBillingState", "()Lkotlinx/coroutines/flow/StateFlow;", "purchaseState", "getPurchaseState", "disconnect", "", "initialize", "Lcom/livewallpaper/core/utils/Resource;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isProductPurchased", "", "productId", "", "launchPurchase", "activity", "Landroid/app/Activity;", "(Landroid/app/Activity;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "queryProducts", "", "Lcom/livewallpaper/core/domain/billing/ProductDetails;", "queryPurchases", "verifyPurchase", "purchaseToken", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "core_debug"})
public final class BillingManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.billing.BillingState> _billingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.BillingState> billingState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.billing.PurchaseState> _purchaseState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.PurchaseState> purchaseState = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PREMIUM_SCENES_PACK = "premium_scenes_pack";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String WEATHER_EFFECTS_PACK = "weather_effects_pack";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String MUSIC_VISUALIZER_PACK = "music_visualizer_pack";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PREMIUM_SUBSCRIPTION = "premium_subscription";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String REMOVE_ADS = "remove_ads";
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.domain.billing.BillingManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public BillingManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.BillingState> getBillingState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.billing.PurchaseState> getPurchaseState() {
        return null;
    }
    
    /**
     * 初始化计费服务
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initialize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 查询可用产品
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queryProducts(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<java.util.List<com.livewallpaper.core.domain.billing.ProductDetails>>> $completion) {
        return null;
    }
    
    /**
     * 发起购买
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object launchPurchase(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 查询已购买的产品
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queryPurchases(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<java.util.List<java.lang.String>>> $completion) {
        return null;
    }
    
    /**
     * 验证购买
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object verifyPurchase(@org.jetbrains.annotations.NotNull()
    java.lang.String purchaseToken, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<java.lang.Boolean>> $completion) {
        return null;
    }
    
    /**
     * 检查产品是否已购买
     */
    public final boolean isProductPurchased(@org.jetbrains.annotations.NotNull()
    java.lang.String productId) {
        return false;
    }
    
    /**
     * 断开计费服务
     */
    public final void disconnect() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/livewallpaper/core/domain/billing/BillingManager$Companion;", "", "()V", "MUSIC_VISUALIZER_PACK", "", "PREMIUM_SCENES_PACK", "PREMIUM_SUBSCRIPTION", "REMOVE_ADS", "WEATHER_EFFECTS_PACK", "core_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}