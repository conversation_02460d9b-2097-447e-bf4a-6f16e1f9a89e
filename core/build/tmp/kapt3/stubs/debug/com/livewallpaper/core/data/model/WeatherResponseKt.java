package com.livewallpaper.core.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0002\u001a\n\u0010\u0004\u001a\u00020\u0005*\u00020\u0006\u001a\u0012\u0010\u0007\u001a\u00020\b*\u00020\t2\u0006\u0010\n\u001a\u00020\u0003\u00a8\u0006\u000b"}, d2 = {"mapWeatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "weatherMain", "", "toWeather", "Lcom/livewallpaper/core/data/model/Weather;", "Lcom/livewallpaper/core/data/model/WeatherResponse;", "toWeatherForecast", "Lcom/livewallpaper/core/data/model/WeatherForecast;", "Lcom/livewallpaper/core/data/model/ForecastItem;", "location", "core_debug"})
public final class WeatherResponseKt {
    
    /**
     * 扩展函数：将API响应转换为应用内部数据模型
     */
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.model.Weather toWeather(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.WeatherResponse $this$toWeather) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.model.WeatherForecast toWeatherForecast(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.ForecastItem $this$toWeatherForecast, @org.jetbrains.annotations.NotNull()
    java.lang.String location) {
        return null;
    }
    
    /**
     * 将OpenWeatherMap的天气类型映射到应用内部枚举
     */
    private static final com.livewallpaper.core.data.model.WeatherType mapWeatherType(java.lang.String weatherMain) {
        return null;
    }
}