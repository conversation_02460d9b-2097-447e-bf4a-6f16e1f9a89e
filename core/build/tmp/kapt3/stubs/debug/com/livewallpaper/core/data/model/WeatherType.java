package com.livewallpaper.core.data.model;

/**
 * 天气类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/livewallpaper/core/data/model/WeatherType;", "", "(Ljava/lang/String;I)V", "<PERSON><PERSON><PERSON>", "CLOUDY", "RAINY", "SNOWY", "FOGGY", "STORMY", "WINDY", "core_debug"})
public enum WeatherType {
    /*public static final*/ CLEAR /* = new CLEAR() */,
    /*public static final*/ CLOUDY /* = new CLOUDY() */,
    /*public static final*/ RAINY /* = new RAINY() */,
    /*public static final*/ SNOWY /* = new SNOWY() */,
    /*public static final*/ FOGGY /* = new FOGGY() */,
    /*public static final*/ STORMY /* = new STORMY() */,
    /*public static final*/ WINDY /* = new WINDY() */;
    
    WeatherType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.WeatherType> getEntries() {
        return null;
    }
}