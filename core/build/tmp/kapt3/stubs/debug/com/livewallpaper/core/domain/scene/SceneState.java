package com.livewallpaper.core.domain.scene;

/**
 * 场景状态密封类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0006\u0007\b\u00a8\u0006\t"}, d2 = {"Lcom/livewallpaper/core/domain/scene/SceneState;", "", "()V", "Error", "Loading", "Success", "Lcom/livewallpaper/core/domain/scene/SceneState$Error;", "Lcom/livewallpaper/core/domain/scene/SceneState$Loading;", "Lcom/livewallpaper/core/domain/scene/SceneState$Success;", "core_debug"})
public abstract class SceneState {
    
    private SceneState() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/livewallpaper/core/domain/scene/SceneState$Error;", "Lcom/livewallpaper/core/domain/scene/SceneState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "core_debug"})
    public static final class Error extends com.livewallpaper.core.domain.scene.SceneState {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.scene.SceneState.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/livewallpaper/core/domain/scene/SceneState$Loading;", "Lcom/livewallpaper/core/domain/scene/SceneState;", "()V", "core_debug"})
    public static final class Loading extends com.livewallpaper.core.domain.scene.SceneState {
        @org.jetbrains.annotations.NotNull()
        public static final com.livewallpaper.core.domain.scene.SceneState.Loading INSTANCE = null;
        
        private Loading() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0016"}, d2 = {"Lcom/livewallpaper/core/domain/scene/SceneState$Success;", "Lcom/livewallpaper/core/domain/scene/SceneState;", "scene", "Lcom/livewallpaper/core/data/model/Scene;", "layers", "Lcom/livewallpaper/core/domain/scene/SceneLayers;", "(Lcom/livewallpaper/core/data/model/Scene;Lcom/livewallpaper/core/domain/scene/SceneLayers;)V", "getLayers", "()Lcom/livewallpaper/core/domain/scene/SceneLayers;", "getScene", "()Lcom/livewallpaper/core/data/model/Scene;", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "core_debug"})
    public static final class Success extends com.livewallpaper.core.domain.scene.SceneState {
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.data.model.Scene scene = null;
        @org.jetbrains.annotations.Nullable()
        private final com.livewallpaper.core.domain.scene.SceneLayers layers = null;
        
        public Success(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.Nullable()
        com.livewallpaper.core.domain.scene.SceneLayers layers) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.Scene getScene() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.livewallpaper.core.domain.scene.SceneLayers getLayers() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.Scene component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.livewallpaper.core.domain.scene.SceneLayers component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.scene.SceneState.Success copy(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.Scene scene, @org.jetbrains.annotations.Nullable()
        com.livewallpaper.core.domain.scene.SceneLayers layers) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}