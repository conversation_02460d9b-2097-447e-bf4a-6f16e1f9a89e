package com.livewallpaper.core.domain.ads;

/**
 * 广告管理器
 * 处理横幅广告、插屏广告和激励视频广告
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u0007\u0018\u0000 *2\u00020\u0001:\u0001*B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0012\u001a\u00020\u0013J\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0006\u0010\u001a\u001a\u00020\u001bJ\u001c\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00110\u00152\u0006\u0010\u001d\u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010\u001fJT\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00110\u00152\u0006\u0010\u001d\u001a\u00020\u001e26\u0010!\u001a2\u0012\u0013\u0012\u00110#\u00a2\u0006\f\b$\u0012\b\b%\u0012\u0004\b\b(&\u0012\u0013\u0012\u00110\'\u00a2\u0006\f\b$\u0012\b\b%\u0012\u0004\b\b((\u0012\u0004\u0012\u00020\u00110\"H\u0086@\u00a2\u0006\u0002\u0010)R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006+"}, d2 = {"Lcom/livewallpaper/core/domain/ads/AdManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_adState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/livewallpaper/core/domain/ads/AdState;", "_rewardedAdState", "Lcom/livewallpaper/core/domain/ads/RewardedAdState;", "adState", "Lkotlinx/coroutines/flow/StateFlow;", "getAdState", "()Lkotlinx/coroutines/flow/StateFlow;", "rewardedAdState", "getRewardedAdState", "cleanup", "", "getAdFrequency", "Lcom/livewallpaper/core/domain/ads/AdFrequency;", "initialize", "Lcom/livewallpaper/core/utils/Resource;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loadBannerAd", "loadInterstitialAd", "loadRewardedAd", "shouldShowAds", "", "showInterstitialAd", "activity", "Landroid/app/Activity;", "(Landroid/app/Activity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "showRewardedAd", "onRewardEarned", "Lkotlin/Function2;", "", "Lkotlin/ParameterName;", "name", "rewardType", "", "rewardAmount", "(Landroid/app/Activity;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "core_debug"})
public final class AdManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.ads.AdState> _adState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.ads.AdState> adState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.ads.RewardedAdState> _rewardedAdState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.ads.RewardedAdState> rewardedAdState = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/1033173712";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String REWARDED_AD_UNIT_ID = "ca-app-pub-3940256099942544/5224354917";
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.domain.ads.AdManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public AdManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.ads.AdState> getAdState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.ads.RewardedAdState> getRewardedAdState() {
        return null;
    }
    
    /**
     * 初始化广告SDK
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initialize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 加载横幅广告
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadBannerAd(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 加载插屏广告
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadInterstitialAd(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 加载激励视频广告
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadRewardedAd(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 显示插屏广告
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object showInterstitialAd(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 显示激励视频广告
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object showRewardedAd(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onRewardEarned, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 检查是否应该显示广告
     */
    public final boolean shouldShowAds() {
        return false;
    }
    
    /**
     * 获取广告频率控制
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.ads.AdFrequency getAdFrequency() {
        return null;
    }
    
    /**
     * 清理广告资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/livewallpaper/core/domain/ads/AdManager$Companion;", "", "()V", "BANNER_AD_UNIT_ID", "", "INTERSTITIAL_AD_UNIT_ID", "REWARDED_AD_UNIT_ID", "core_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}