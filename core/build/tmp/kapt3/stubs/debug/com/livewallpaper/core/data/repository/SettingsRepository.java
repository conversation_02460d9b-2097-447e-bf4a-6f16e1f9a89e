package com.livewallpaper.core.data.repository;

/**
 * 设置仓库
 * 负责管理用户偏好设置的读写和验证
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0006\n\u0002\b\n\n\u0002\u0010 \n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000bH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\rJ\u001a\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u000bH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0010\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010\rJ>\u0010\u0012\u001a\u00020\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u0010\u0018J2\u0010\u0019\u001a\u00020\u00112\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0086@\u00a2\u0006\u0002\u0010\u001fJ>\u0010 \u001a\u00020\u00112\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010#2\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u0010&JV\u0010\'\u001a\u00020\u00112\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010)\u001a\u0004\u0018\u00010*2\n\b\u0002\u0010+\u001a\u0004\u0018\u00010,2\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010.\u001a\u0004\u0018\u00010/2\n\b\u0002\u00100\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u00101JJ\u00102\u001a\u00020\u00112\n\b\u0002\u00103\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u00104\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u00105\u001a\u0004\u0018\u00010\u001c2\n\b\u0002\u00106\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u00107\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u00108JJ\u00109\u001a\u00020\u00112\n\b\u0002\u0010:\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010;\u001a\u0004\u0018\u00010/2\n\b\u0002\u0010<\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010=\u001a\u0004\u0018\u00010,2\n\b\u0002\u0010>\u001a\u0004\u0018\u00010?H\u0086@\u00a2\u0006\u0002\u0010@J2\u0010A\u001a\u00020\u00112\n\b\u0002\u0010B\u001a\u0004\u0018\u00010C2\n\b\u0002\u0010D\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010E\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u0010FJJ\u0010G\u001a\u00020\u00112\n\b\u0002\u0010H\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010I\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010J\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010K\u001a\u0004\u0018\u00010L2\n\b\u0002\u0010M\u001a\u0004\u0018\u00010LH\u0086@\u00a2\u0006\u0002\u0010NJJ\u0010O\u001a\u00020\u00112\n\b\u0002\u0010P\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010Q\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010R\u001a\u0004\u0018\u00010/2\n\b\u0002\u0010S\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010T\u001a\u0004\u0018\u00010\u0014H\u0086@\u00a2\u0006\u0002\u0010UJ\u0014\u0010V\u001a\b\u0012\u0004\u0012\u00020\f0WH\u0086@\u00a2\u0006\u0002\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006X"}, d2 = {"Lcom/livewallpaper/core/data/repository/SettingsRepository;", "", "settingsDataStore", "Lcom/livewallpaper/core/data/preferences/SettingsDataStore;", "(Lcom/livewallpaper/core/data/preferences/SettingsDataStore;)V", "settingsFlow", "Lkotlinx/coroutines/flow/Flow;", "Lcom/livewallpaper/core/data/model/WallpaperSettings;", "getSettingsFlow", "()Lkotlinx/coroutines/flow/Flow;", "exportSettings", "", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentSettings", "getSettingsStats", "resetToDefaults", "", "updateAdvancedSettings", "enableExperimentalFeatures", "", "enableCloudSync", "enableAnalytics", "enableCrashReporting", "(Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateBasicSettings", "isEnabled", "frameRate", "", "quality", "Lcom/livewallpaper/core/data/model/RenderQuality;", "(Ljava/lang/Boolean;Ljava/lang/Integer;Lcom/livewallpaper/core/data/model/RenderQuality;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDisplaySettings", "enableDebugInfo", "debugInfoPosition", "Lcom/livewallpaper/core/data/model/DebugInfoPosition;", "enableFpsCounter", "enableMemoryMonitor", "(Ljava/lang/Boolean;Lcom/livewallpaper/core/data/model/DebugInfoPosition;Ljava/lang/Boolean;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMusicSettings", "enableMusicVisualization", "musicCardPosition", "Lcom/livewallpaper/core/data/model/CardPosition;", "musicCardOpacity", "", "enableMusicCardAutoHide", "musicCardAutoHideDelay", "", "enableAudioVisualization", "(Ljava/lang/Boolean;Lcom/livewallpaper/core/data/model/CardPosition;Ljava/lang/Float;Ljava/lang/Boolean;Ljava/lang/Long;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePerformanceSettings", "enableBatteryOptimization", "enableLowPowerMode", "maxMemoryUsage", "enableGpuAcceleration", "enableObjectPooling", "(Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/Boolean;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSceneSettings", "enableAutoSceneSwitch", "sceneChangeInterval", "enableParallaxEffect", "parallaxIntensity", "preferredSceneCategory", "Lcom/livewallpaper/core/data/model/SceneCategory;", "(Ljava/lang/Boolean;Ljava/lang/Long;Ljava/lang/Boolean;Ljava/lang/Float;Lcom/livewallpaper/core/data/model/SceneCategory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateThemeSettings", "themeMode", "Lcom/livewallpaper/core/data/model/ThemeMode;", "accentColor", "enableDynamicColors", "(Lcom/livewallpaper/core/data/model/ThemeMode;Ljava/lang/String;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTimeSettings", "enableTimeBasedScenes", "enableSeasonalScenes", "userTimeZone", "customLatitude", "", "customLongitude", "(Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWeatherSettings", "enableWeatherEffects", "enableWeatherBasedScenes", "weatherUpdateInterval", "weatherApiKey", "enableLocationServices", "(Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateSettings", "", "core_debug"})
public final class SettingsRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.preferences.SettingsDataStore settingsDataStore = null;
    
    /**
     * 设置数据流
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.data.model.WallpaperSettings> settingsFlow = null;
    
    @javax.inject.Inject()
    public SettingsRepository(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.preferences.SettingsDataStore settingsDataStore) {
        super();
    }
    
    /**
     * 设置数据流
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.data.model.WallpaperSettings> getSettingsFlow() {
        return null;
    }
    
    /**
     * 获取当前设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.WallpaperSettings> $completion) {
        return null;
    }
    
    /**
     * 更新基础设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateBasicSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean isEnabled, @org.jetbrains.annotations.Nullable()
    java.lang.Integer frameRate, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.RenderQuality quality, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新场景设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateSceneSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableAutoSceneSwitch, @org.jetbrains.annotations.Nullable()
    java.lang.Long sceneChangeInterval, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableParallaxEffect, @org.jetbrains.annotations.Nullable()
    java.lang.Float parallaxIntensity, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.SceneCategory preferredSceneCategory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新时间设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateTimeSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableTimeBasedScenes, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableSeasonalScenes, @org.jetbrains.annotations.Nullable()
    java.lang.String userTimeZone, @org.jetbrains.annotations.Nullable()
    java.lang.Double customLatitude, @org.jetbrains.annotations.Nullable()
    java.lang.Double customLongitude, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新天气设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateWeatherSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableWeatherEffects, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableWeatherBasedScenes, @org.jetbrains.annotations.Nullable()
    java.lang.Long weatherUpdateInterval, @org.jetbrains.annotations.Nullable()
    java.lang.String weatherApiKey, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableLocationServices, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新音乐设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMusicSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableMusicVisualization, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.CardPosition musicCardPosition, @org.jetbrains.annotations.Nullable()
    java.lang.Float musicCardOpacity, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableMusicCardAutoHide, @org.jetbrains.annotations.Nullable()
    java.lang.Long musicCardAutoHideDelay, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableAudioVisualization, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新性能设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updatePerformanceSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableBatteryOptimization, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableLowPowerMode, @org.jetbrains.annotations.Nullable()
    java.lang.Integer maxMemoryUsage, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableGpuAcceleration, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableObjectPooling, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新显示设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDisplaySettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableDebugInfo, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.DebugInfoPosition debugInfoPosition, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableFpsCounter, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableMemoryMonitor, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新主题设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateThemeSettings(@org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.ThemeMode themeMode, @org.jetbrains.annotations.Nullable()
    java.lang.String accentColor, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableDynamicColors, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 更新高级设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateAdvancedSettings(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableExperimentalFeatures, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableCloudSync, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableAnalytics, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean enableCrashReporting, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 重置设置为默认值
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object resetToDefaults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 导出设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object exportSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
    
    /**
     * 验证设置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object validateSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * 获取设置统计信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSettingsStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
}