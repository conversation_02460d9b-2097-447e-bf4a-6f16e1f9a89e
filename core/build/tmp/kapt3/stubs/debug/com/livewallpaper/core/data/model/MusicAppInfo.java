package com.livewallpaper.core.data.model;

/**
 * 音乐应用信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u0000 \u00152\u00020\u0001:\u0001\u0015B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0006H\u00c6\u0003J\'\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00062\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\t\u00a8\u0006\u0016"}, d2 = {"Lcom/livewallpaper/core/data/model/MusicAppInfo;", "", "packageName", "", "appName", "isSupported", "", "(Ljava/lang/String;Ljava/lang/String;Z)V", "getAppName", "()Ljava/lang/String;", "()Z", "getPackageName", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "Companion", "core_debug"})
public final class MusicAppInfo {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String packageName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String appName = null;
    private final boolean isSupported = false;
    
    /**
     * 支持的音乐应用列表
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.livewallpaper.core.data.model.MusicAppInfo> SUPPORTED_MUSIC_APPS = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.model.MusicAppInfo.Companion Companion = null;
    
    public MusicAppInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName, @org.jetbrains.annotations.NotNull()
    java.lang.String appName, boolean isSupported) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPackageName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAppName() {
        return null;
    }
    
    public final boolean isSupported() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.MusicAppInfo copy(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName, @org.jetbrains.annotations.NotNull()
    java.lang.String appName, boolean isSupported) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\tJ\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\n\u001a\u00020\tR\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\r"}, d2 = {"Lcom/livewallpaper/core/data/model/MusicAppInfo$Companion;", "", "()V", "SUPPORTED_MUSIC_APPS", "", "Lcom/livewallpaper/core/data/model/MusicAppInfo;", "getSUPPORTED_MUSIC_APPS", "()Ljava/util/List;", "getAppName", "", "packageName", "isSupportedMusicApp", "", "core_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 支持的音乐应用列表
         */
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.livewallpaper.core.data.model.MusicAppInfo> getSUPPORTED_MUSIC_APPS() {
            return null;
        }
        
        /**
         * 检查是否为支持的音乐应用
         */
        public final boolean isSupportedMusicApp(@org.jetbrains.annotations.NotNull()
        java.lang.String packageName) {
            return false;
        }
        
        /**
         * 获取应用名称
         */
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAppName(@org.jetbrains.annotations.NotNull()
        java.lang.String packageName) {
            return null;
        }
    }
}