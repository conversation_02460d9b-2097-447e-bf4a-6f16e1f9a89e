package com.livewallpaper.core.domain.time;

/**
 * 时间阶段枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/livewallpaper/core/domain/time/TimePhase;", "", "(<PERSON>ja<PERSON>/lang/String;I)V", "NIGHT", "ASTRONOMICAL_TWILIGHT", "NAUTICAL_TWILIGHT", "CIVIL_TWILIGHT", "DAY", "UNKNOWN", "core_debug"})
public enum TimePhase {
    /*public static final*/ NIGHT /* = new NIGHT() */,
    /*public static final*/ ASTRONOMICAL_TWILIGHT /* = new ASTRONOMICAL_TWILIGHT() */,
    /*public static final*/ NAUTICAL_TWILIGHT /* = new NAUTICAL_TWILIGHT() */,
    /*public static final*/ CIVIL_TWILIGHT /* = new CIVIL_TWILIGHT() */,
    /*public static final*/ DAY /* = new DAY() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    TimePhase() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.domain.time.TimePhase> getEntries() {
        return null;
    }
}