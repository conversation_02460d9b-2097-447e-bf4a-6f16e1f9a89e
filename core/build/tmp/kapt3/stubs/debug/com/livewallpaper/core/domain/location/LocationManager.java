package com.livewallpaper.core.domain.location;

/**
 * 位置服务管理器
 * 负责获取用户位置信息，处理权限请求和位置缓存
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0082@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0019H\u0082@\u00a2\u0006\u0002\u0010\u001aJ*\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d0\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001fH\u0082@\u00a2\u0006\u0002\u0010!J\u0010\u0010\"\u001a\u0004\u0018\u00010\u0019H\u0082@\u00a2\u0006\u0002\u0010#J\u0014\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00150%H\u0086@\u00a2\u0006\u0002\u0010#J\u0012\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150%0\'J\u0014\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00150%H\u0082@\u00a2\u0006\u0002\u0010#J\u0018\u0010)\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001fH\u0002J\u0006\u0010*\u001a\u00020+J\u0010\u0010,\u001a\u00020+2\u0006\u0010\u0014\u001a\u00020\u0019H\u0002J\u0010\u0010-\u001a\u0004\u0018\u00010\u0019H\u0082@\u00a2\u0006\u0002\u0010#J,\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00150%2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010/\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u00100R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u001b\u0010\r\u001a\u00020\u000e8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0011\u0010\f\u001a\u0004\b\u000f\u0010\u0010R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/livewallpaper/core/domain/location/LocationManager;", "", "context", "Landroid/content/Context;", "preferences", "Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;", "(Landroid/content/Context;Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;)V", "fusedLocationClient", "Lcom/google/android/gms/location/FusedLocationProviderClient;", "getFusedLocationClient", "()Lcom/google/android/gms/location/FusedLocationProviderClient;", "fusedLocationClient$delegate", "Lkotlin/Lazy;", "geocoder", "Landroid/location/Geocoder;", "getGeocoder", "()Landroid/location/Geocoder;", "geocoder$delegate", "cacheLocation", "", "location", "Lcom/livewallpaper/core/data/model/Location;", "(Lcom/livewallpaper/core/data/model/Location;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "convertToLocation", "androidLocation", "Landroid/location/Location;", "(Landroid/location/Location;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCityFromCoordinates", "Lkotlin/Pair;", "", "latitude", "", "longitude", "(DDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentAndroidLocation", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentLocation", "Lcom/livewallpaper/core/utils/Resource;", "getLocationFlow", "Lkotlinx/coroutines/flow/Flow;", "getManualLocation", "getTimezoneFromCoordinates", "hasLocationPermission", "", "isLocationRecent", "requestNewLocation", "setManualLocation", "cityName", "(DDLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "core_debug"})
public final class LocationManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.preferences.WallpaperPreferences preferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy fusedLocationClient$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy geocoder$delegate = null;
    
    @javax.inject.Inject()
    public LocationManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.preferences.WallpaperPreferences preferences) {
        super();
    }
    
    private final com.google.android.gms.location.FusedLocationProviderClient getFusedLocationClient() {
        return null;
    }
    
    private final android.location.Geocoder getGeocoder() {
        return null;
    }
    
    /**
     * 检查位置权限是否已授予
     */
    public final boolean hasLocationPermission() {
        return false;
    }
    
    /**
     * 获取当前位置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentLocation(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Location>> $completion) {
        return null;
    }
    
    /**
     * 获取Android系统位置
     */
    private final java.lang.Object getCurrentAndroidLocation(kotlin.coroutines.Continuation<? super android.location.Location> $completion) {
        return null;
    }
    
    /**
     * 请求新的位置
     */
    private final java.lang.Object requestNewLocation(kotlin.coroutines.Continuation<? super android.location.Location> $completion) {
        return null;
    }
    
    /**
     * 检查位置是否是最近的
     */
    private final boolean isLocationRecent(android.location.Location location) {
        return false;
    }
    
    /**
     * 转换为应用的Location对象
     */
    private final java.lang.Object convertToLocation(android.location.Location androidLocation, kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Location> $completion) {
        return null;
    }
    
    /**
     * 根据坐标获取城市信息
     */
    private final java.lang.Object getCityFromCoordinates(double latitude, double longitude, kotlin.coroutines.Continuation<? super kotlin.Pair<java.lang.String, java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * 根据坐标获取时区
     */
    private final java.lang.String getTimezoneFromCoordinates(double latitude, double longitude) {
        return null;
    }
    
    /**
     * 获取手动设置的位置
     */
    private final java.lang.Object getManualLocation(kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Location>> $completion) {
        return null;
    }
    
    /**
     * 缓存位置信息
     */
    private final java.lang.Object cacheLocation(com.livewallpaper.core.data.model.Location location, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取位置信息流
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Location>> getLocationFlow() {
        return null;
    }
    
    /**
     * 设置手动位置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setManualLocation(double latitude, double longitude, @org.jetbrains.annotations.NotNull()
    java.lang.String cityName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.data.model.Location>> $completion) {
        return null;
    }
}