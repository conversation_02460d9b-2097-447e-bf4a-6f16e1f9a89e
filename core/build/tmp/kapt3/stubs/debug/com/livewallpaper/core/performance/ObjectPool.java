package com.livewallpaper.core.performance;

/**
 * 通用对象池实现
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B\u001d\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010\u000b\u001a\u00020\fJ\u000b\u0010\r\u001a\u00028\u0000\u00a2\u0006\u0002\u0010\u000eJ\u0013\u0010\u000f\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00028\u0000\u00a2\u0006\u0002\u0010\u0011J\u0006\u0010\u0012\u001a\u00020\u0006R\u000e\u0010\b\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00028\u00000\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00000\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/livewallpaper/core/performance/ObjectPool;", "T", "", "factory", "Lkotlin/Function0;", "maxSize", "", "(Lkotlin/jvm/functions/Function0;I)V", "currentSize", "pool", "Ljava/util/concurrent/ConcurrentLinkedQueue;", "clear", "", "obtain", "()Ljava/lang/Object;", "recycle", "obj", "(Ljava/lang/Object;)V", "size", "core_debug"})
public final class ObjectPool<T extends java.lang.Object> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<T> factory = null;
    private final int maxSize = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentLinkedQueue<T> pool = null;
    private int currentSize = 0;
    
    public ObjectPool(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<? extends T> factory, int maxSize) {
        super();
    }
    
    /**
     * 获取对象
     */
    public final T obtain() {
        return null;
    }
    
    /**
     * 回收对象
     */
    public final void recycle(T obj) {
    }
    
    /**
     * 清空对象池
     */
    public final void clear() {
    }
    
    /**
     * 获取当前对象池大小
     */
    public final int size() {
        return 0;
    }
}