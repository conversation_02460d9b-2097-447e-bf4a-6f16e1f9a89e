package com.livewallpaper.core.data.model;

/**
 * 场景分类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/livewallpaper/core/data/model/SceneCategory;", "", "(Ljava/lang/String;I)V", "NATURE", "CITY", "ABSTRACT", "SPACE", "OCEAN", "MOUNTAIN", "FOREST", "DESERT", "CUSTOM", "core_debug"})
public enum SceneCategory {
    /*public static final*/ NATURE /* = new NATURE() */,
    /*public static final*/ CITY /* = new CITY() */,
    /*public static final*/ ABSTRACT /* = new ABSTRACT() */,
    /*public static final*/ SPACE /* = new SPACE() */,
    /*public static final*/ OCEAN /* = new OCEAN() */,
    /*public static final*/ MOUNTAIN /* = new MOUNTAIN() */,
    /*public static final*/ FOREST /* = new FOREST() */,
    /*public static final*/ DESERT /* = new DESERT() */,
    /*public static final*/ CUSTOM /* = new CUSTOM() */;
    
    SceneCategory() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.SceneCategory> getEntries() {
        return null;
    }
}