package com.livewallpaper.core.data.database;

/**
 * 应用主数据库
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00072\u00020\u0001:\u0001\u0007B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&\u00a8\u0006\b"}, d2 = {"Lcom/livewallpaper/core/data/database/WallpaperDatabase;", "Landroidx/room/RoomDatabase;", "()V", "sceneDao", "Lcom/livewallpaper/core/data/database/dao/SceneDao;", "weatherDao", "Lcom/livewallpaper/core/data/database/dao/WeatherDao;", "Companion", "core_debug"})
@androidx.room.Database(entities = {com.livewallpaper.core.data.model.Scene.class, com.livewallpaper.core.data.model.Weather.class, com.livewallpaper.core.data.model.WeatherForecast.class}, version = 1, exportSchema = false)
@androidx.room.TypeConverters(value = {com.livewallpaper.core.data.database.Converters.class})
public abstract class WallpaperDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.livewallpaper.core.data.database.WallpaperDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.database.WallpaperDatabase.Companion Companion = null;
    
    public WallpaperDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.livewallpaper.core.data.database.dao.SceneDao sceneDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.livewallpaper.core.data.database.dao.WeatherDao weatherDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/livewallpaper/core/data/database/WallpaperDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/livewallpaper/core/data/database/WallpaperDatabase;", "getDatabase", "context", "Landroid/content/Context;", "core_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.database.WallpaperDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}