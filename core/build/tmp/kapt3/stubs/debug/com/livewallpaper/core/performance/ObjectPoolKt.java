package com.livewallpaper.core.performance;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a;\u0010\u0000\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006\u001a/\u0010\u0007\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u001a/\u0010\u000b\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u001a/\u0010\r\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u001a/\u0010\u000f\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u001a/\u0010\u0011\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u001a/\u0010\u0013\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u001a/\u0010\u0015\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\u00020\b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u0002H\u00010\u0005H\u0086\b\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\n\u0082\u0002\u0007\n\u0005\b\u009920\u0001\u00a8\u0006\u0017"}, d2 = {"use", "R", "T", "Lcom/livewallpaper/core/performance/ObjectPool;", "block", "Lkotlin/Function1;", "(Lcom/livewallpaper/core/performance/ObjectPool;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "useMatrix", "Lcom/livewallpaper/core/performance/ObjectPoolManager;", "Landroid/graphics/Matrix;", "(Lcom/livewallpaper/core/performance/ObjectPoolManager;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "usePaint", "Landroid/graphics/Paint;", "usePath", "Landroid/graphics/Path;", "usePoint", "Landroid/graphics/Point;", "usePointF", "Landroid/graphics/PointF;", "useRect", "Landroid/graphics/Rect;", "useRectF", "Landroid/graphics/RectF;", "core_debug"})
public final class ObjectPoolKt {
    
    /**
     * 对象池扩展函数，提供便捷的使用方式
     */
    public static final <T extends java.lang.Object, R extends java.lang.Object>R use(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPool<T> $this$use, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super T, ? extends R> block) {
        return null;
    }
    
    /**
     * ObjectPoolManager的扩展函数
     */
    public static final <R extends java.lang.Object>R usePaint(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$usePaint, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Paint, ? extends R> block) {
        return null;
    }
    
    public static final <R extends java.lang.Object>R useRect(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$useRect, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Rect, ? extends R> block) {
        return null;
    }
    
    public static final <R extends java.lang.Object>R useRectF(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$useRectF, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.RectF, ? extends R> block) {
        return null;
    }
    
    public static final <R extends java.lang.Object>R usePath(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$usePath, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Path, ? extends R> block) {
        return null;
    }
    
    public static final <R extends java.lang.Object>R useMatrix(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$useMatrix, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Matrix, ? extends R> block) {
        return null;
    }
    
    public static final <R extends java.lang.Object>R usePoint(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$usePoint, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.Point, ? extends R> block) {
        return null;
    }
    
    public static final <R extends java.lang.Object>R usePointF(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager $this$usePointF, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.graphics.PointF, ? extends R> block) {
        return null;
    }
}