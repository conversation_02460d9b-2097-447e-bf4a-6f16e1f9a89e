package com.livewallpaper.core.domain.time;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0000\u001a\u0014\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\b\b\u0002\u0010\u0003\u001a\u00020\u0001\u001a\u0014\u0010\u0004\u001a\u00020\u0005*\u00020\u00022\b\b\u0002\u0010\u0003\u001a\u00020\u0005\u001a\u0014\u0010\u0006\u001a\u00020\u0007*\u00020\u00022\b\b\u0002\u0010\u0003\u001a\u00020\u0007\u00a8\u0006\b"}, d2 = {"getPhaseOrDefault", "Lcom/livewallpaper/core/data/model/TimePhase;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "default", "getProgressOrDefault", "", "isDayOrDefault", "", "core_debug"})
public final class WallpaperTimeManagerKt {
    
    /**
     * 时间状态扩展函数
     */
    public static final double getProgressOrDefault(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState $this$getProgressOrDefault, double p1_772401952) {
        return 0.0;
    }
    
    public static final boolean isDayOrDefault(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState $this$isDayOrDefault, boolean p1_772401952) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.data.model.TimePhase getPhaseOrDefault(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.WallpaperTimeState $this$getPhaseOrDefault, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.TimePhase p1_772401952) {
        return null;
    }
}