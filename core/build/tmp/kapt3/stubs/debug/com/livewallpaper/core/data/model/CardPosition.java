package com.livewallpaper.core.data.model;

/**
 * 音乐卡片位置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/livewallpaper/core/data/model/CardPosition;", "", "(Ljava/lang/String;I)V", "TOP_LEFT", "TOP_RIGHT", "BOTTOM_LEFT", "BOTTOM_RIGHT", "CENTER", "core_debug"})
public enum CardPosition {
    /*public static final*/ TOP_LEFT /* = new TOP_LEFT() */,
    /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT() */,
    /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT() */,
    /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT() */,
    /*public static final*/ CENTER /* = new CENTER() */;
    
    CardPosition() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.CardPosition> getEntries() {
        return null;
    }
}