package com.livewallpaper.core.data.model;

/**
 * 时间段
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/livewallpaper/core/data/model/TimeOfDay;", "", "(<PERSON>ja<PERSON>/lang/String;I)V", "DAWN", "MORNING", "NOON", "AFTERNOON", "DUSK", "NIGHT", "core_debug"})
public enum TimeOfDay {
    /*public static final*/ DAWN /* = new DAWN() */,
    /*public static final*/ MORNING /* = new MORNING() */,
    /*public static final*/ NOON /* = new NOON() */,
    /*public static final*/ AFTERNOON /* = new AFTERNOON() */,
    /*public static final*/ DUSK /* = new DUSK() */,
    /*public static final*/ NIGHT /* = new NIGHT() */;
    
    TimeOfDay() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.TimeOfDay> getEntries() {
        return null;
    }
}