package com.livewallpaper.core.performance;

/**
 * 脏区域管理器
 * 负责管理需要重绘的区域，实现局部重绘优化
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u0011J\u000e\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0015\u001a\u00020\nJ\u000e\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0016\u001a\u00020\u0017J&\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u0006J\u000e\u0010\u001c\u001a\u00020\f2\u0006\u0010\u001d\u001a\u00020\u001eJ\u0006\u0010\u001f\u001a\u00020\u000fJ\u0006\u0010 \u001a\u00020!J\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\n0#J\u0006\u0010\u000b\u001a\u00020\fJ\u0018\u0010$\u001a\u00020\f2\u0006\u0010%\u001a\u00020\n2\u0006\u0010&\u001a\u00020\nH\u0002J\u0016\u0010\'\u001a\u00020\f2\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u0006J\u000e\u0010*\u001a\u00020\f2\u0006\u0010\u0015\u001a\u00020\nJ\u0006\u0010+\u001a\u00020\u000fJ\b\u0010,\u001a\u00020\u000fH\u0002J\u0010\u0010-\u001a\u00020\u000f2\b\b\u0002\u0010.\u001a\u00020\u0011J\u000e\u0010/\u001a\u00020\u000f2\u0006\u0010\u001d\u001a\u00020\u001eJ\u0016\u00100\u001a\u00020\u000f2\u0006\u00101\u001a\u00020\u00062\u0006\u00102\u001a\u00020\u0006R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/livewallpaper/core/performance/DirtyRegionManager;", "", "objectPoolManager", "Lcom/livewallpaper/core/performance/ObjectPoolManager;", "(Lcom/livewallpaper/core/performance/ObjectPoolManager;)V", "canvasHeight", "", "canvasWidth", "dirtyRegions", "", "Landroid/graphics/Rect;", "isFullRedrawRequired", "", "mergedRegions", "addDirtyCircle", "", "centerX", "", "centerY", "radius", "addDirtyRegion", "rect", "rectF", "Landroid/graphics/RectF;", "left", "top", "right", "bottom", "applyClipping", "canvas", "Landroid/graphics/Canvas;", "clearDirtyRegions", "getDirtyRegionStats", "Lcom/livewallpaper/core/performance/DirtyRegionStats;", "getDirtyRegions", "", "isOverlappingOrAdjacent", "rect1", "rect2", "isPointInDirtyRegion", "x", "y", "isRectIntersectingDirtyRegion", "markFullRedraw", "mergeOverlappingRegions", "optimizeDirtyRegions", "threshold", "restoreCanvas", "setCanvasSize", "width", "height", "core_debug"})
public final class DirtyRegionManager {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPoolManager objectPoolManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<android.graphics.Rect> dirtyRegions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<android.graphics.Rect> mergedRegions = null;
    private int canvasWidth = 0;
    private int canvasHeight = 0;
    private boolean isFullRedrawRequired = true;
    
    @javax.inject.Inject()
    public DirtyRegionManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.performance.ObjectPoolManager objectPoolManager) {
        super();
    }
    
    /**
     * 设置画布尺寸
     */
    public final void setCanvasSize(int width, int height) {
    }
    
    /**
     * 标记需要全屏重绘
     */
    public final void markFullRedraw() {
    }
    
    /**
     * 添加脏区域
     */
    public final void addDirtyRegion(int left, int top, int right, int bottom) {
    }
    
    /**
     * 添加脏区域（RectF版本）
     */
    public final void addDirtyRegion(@org.jetbrains.annotations.NotNull()
    android.graphics.RectF rectF) {
    }
    
    /**
     * 添加脏区域（Rect版本）
     */
    public final void addDirtyRegion(@org.jetbrains.annotations.NotNull()
    android.graphics.Rect rect) {
    }
    
    /**
     * 添加圆形脏区域
     */
    public final void addDirtyCircle(float centerX, float centerY, float radius) {
    }
    
    /**
     * 合并重叠的脏区域
     */
    private final void mergeOverlappingRegions() {
    }
    
    /**
     * 检查两个区域是否重叠或相邻
     */
    private final boolean isOverlappingOrAdjacent(android.graphics.Rect rect1, android.graphics.Rect rect2) {
        return false;
    }
    
    /**
     * 获取需要重绘的区域列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<android.graphics.Rect> getDirtyRegions() {
        return null;
    }
    
    /**
     * 检查是否需要全屏重绘
     */
    public final boolean isFullRedrawRequired() {
        return false;
    }
    
    /**
     * 清理脏区域（在重绘完成后调用）
     */
    public final void clearDirtyRegions() {
    }
    
    /**
     * 检查点是否在脏区域内
     */
    public final boolean isPointInDirtyRegion(int x, int y) {
        return false;
    }
    
    /**
     * 检查矩形是否与脏区域相交
     */
    public final boolean isRectIntersectingDirtyRegion(@org.jetbrains.annotations.NotNull()
    android.graphics.Rect rect) {
        return false;
    }
    
    /**
     * 获取脏区域统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.DirtyRegionStats getDirtyRegionStats() {
        return null;
    }
    
    /**
     * 优化脏区域
     * 如果脏区域覆盖面积超过阈值，则改为全屏重绘
     */
    public final void optimizeDirtyRegions(float threshold) {
    }
    
    /**
     * 应用Canvas裁剪
     */
    public final boolean applyClipping(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
        return false;
    }
    
    /**
     * 恢复Canvas状态
     */
    public final void restoreCanvas(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
}