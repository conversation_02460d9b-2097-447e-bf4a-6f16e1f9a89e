package com.livewallpaper.core.utils;

/**
 * 更新检查结果
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0006\u0007\b\u00a8\u0006\t"}, d2 = {"Lcom/livewallpaper/core/utils/UpdateCheckResult;", "", "()V", "Error", "NoUpdate", "UpdateAvailable", "Lcom/livewallpaper/core/utils/UpdateCheckResult$Error;", "Lcom/livewallpaper/core/utils/UpdateCheckResult$NoUpdate;", "Lcom/livewallpaper/core/utils/UpdateCheckResult$UpdateAvailable;", "core_debug"})
public abstract class UpdateCheckResult {
    
    private UpdateCheckResult() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/livewallpaper/core/utils/UpdateCheckResult$Error;", "Lcom/livewallpaper/core/utils/UpdateCheckResult;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "core_debug"})
    public static final class Error extends com.livewallpaper.core.utils.UpdateCheckResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.utils.UpdateCheckResult.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/livewallpaper/core/utils/UpdateCheckResult$NoUpdate;", "Lcom/livewallpaper/core/utils/UpdateCheckResult;", "currentVersion", "Lcom/livewallpaper/core/data/model/AppVersion;", "(Lcom/livewallpaper/core/data/model/AppVersion;)V", "getCurrentVersion", "()Lcom/livewallpaper/core/data/model/AppVersion;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "core_debug"})
    public static final class NoUpdate extends com.livewallpaper.core.utils.UpdateCheckResult {
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.data.model.AppVersion currentVersion = null;
        
        public NoUpdate(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.AppVersion currentVersion) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.AppVersion getCurrentVersion() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.AppVersion component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.utils.UpdateCheckResult.NoUpdate copy(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.AppVersion currentVersion) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0014"}, d2 = {"Lcom/livewallpaper/core/utils/UpdateCheckResult$UpdateAvailable;", "Lcom/livewallpaper/core/utils/UpdateCheckResult;", "currentVersion", "Lcom/livewallpaper/core/data/model/AppVersion;", "latestVersion", "(Lcom/livewallpaper/core/data/model/AppVersion;Lcom/livewallpaper/core/data/model/AppVersion;)V", "getCurrentVersion", "()Lcom/livewallpaper/core/data/model/AppVersion;", "getLatestVersion", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "core_debug"})
    public static final class UpdateAvailable extends com.livewallpaper.core.utils.UpdateCheckResult {
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.data.model.AppVersion currentVersion = null;
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.data.model.AppVersion latestVersion = null;
        
        public UpdateAvailable(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.AppVersion currentVersion, @org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.AppVersion latestVersion) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.AppVersion getCurrentVersion() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.AppVersion getLatestVersion() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.AppVersion component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.AppVersion component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.utils.UpdateCheckResult.UpdateAvailable copy(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.AppVersion currentVersion, @org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.AppVersion latestVersion) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}