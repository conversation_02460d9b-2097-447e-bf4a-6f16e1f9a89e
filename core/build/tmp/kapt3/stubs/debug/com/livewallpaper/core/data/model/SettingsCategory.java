package com.livewallpaper.core.data.model;

/**
 * 设置分类枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011\u00a8\u0006\u0012"}, d2 = {"Lcom/livewallpaper/core/data/model/SettingsCategory;", "", "displayName", "", "icon", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "getIcon", "GENERAL", "SCENE", "TIME", "WEATHER", "MUSIC", "PERFORMANCE", "DISPLAY", "THEME", "ADVANCED", "core_debug"})
public enum SettingsCategory {
    /*public static final*/ GENERAL /* = new GENERAL(null, null) */,
    /*public static final*/ SCENE /* = new SCENE(null, null) */,
    /*public static final*/ TIME /* = new TIME(null, null) */,
    /*public static final*/ WEATHER /* = new WEATHER(null, null) */,
    /*public static final*/ MUSIC /* = new MUSIC(null, null) */,
    /*public static final*/ PERFORMANCE /* = new PERFORMANCE(null, null) */,
    /*public static final*/ DISPLAY /* = new DISPLAY(null, null) */,
    /*public static final*/ THEME /* = new THEME(null, null) */,
    /*public static final*/ ADVANCED /* = new ADVANCED(null, null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String icon = null;
    
    SettingsCategory(java.lang.String displayName, java.lang.String icon) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIcon() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.SettingsCategory> getEntries() {
        return null;
    }
}