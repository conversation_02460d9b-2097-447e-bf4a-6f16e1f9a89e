package com.livewallpaper.core.utils;

/**
 * 占位图片生成器
 * 用于在没有真实图片资源时生成测试用的占位图片
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0015\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J2\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\fH\u0002J(\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nJ(\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nJ(\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nJ\u001a\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\t\u001a\u0004\u0018\u00010\nH\u0002\u00a8\u0006\u0014"}, d2 = {"Lcom/livewallpaper/core/utils/PlaceholderImageGenerator;", "", "()V", "addSceneElements", "", "canvas", "Landroid/graphics/Canvas;", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "width", "", "height", "generateForeground", "Landroid/graphics/Bitmap;", "generateMiddleground", "generateSceneBackground", "getColorsForScene", "", "core_debug"})
public final class PlaceholderImageGenerator {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.utils.PlaceholderImageGenerator INSTANCE = null;
    
    private PlaceholderImageGenerator() {
        super();
    }
    
    /**
     * 生成场景背景图片
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Bitmap generateSceneBackground(int width, int height, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    /**
     * 生成中景图层
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Bitmap generateMiddleground(int width, int height, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    /**
     * 生成前景图层
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Bitmap generateForeground(int width, int height, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    /**
     * 根据场景类型和时间获取颜色
     */
    private final int[] getColorsForScene(com.livewallpaper.core.data.model.SceneCategory category, com.livewallpaper.core.data.model.TimeOfDay timeOfDay) {
        return null;
    }
    
    /**
     * 添加场景装饰元素
     */
    private final void addSceneElements(android.graphics.Canvas canvas, com.livewallpaper.core.data.model.SceneCategory category, com.livewallpaper.core.data.model.TimeOfDay timeOfDay, int width, int height) {
    }
}