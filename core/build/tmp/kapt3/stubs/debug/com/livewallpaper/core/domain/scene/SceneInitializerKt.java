package com.livewallpaper.core.domain.scene;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\f\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001e\u0010\u0000\u001a\u0002H\u0001\"\u0004\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u0002H\u0082@\u00a2\u0006\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"first", "T", "Lkotlinx/coroutines/flow/Flow;", "(Lkotlinx/coroutines/flow/Flow;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "core_debug"})
public final class SceneInitializerKt {
    
    /**
     * 扩展函数，获取第一个元素
     */
    private static final <T extends java.lang.Object>java.lang.Object first(kotlinx.coroutines.flow.Flow<? extends T> $this$first, kotlin.coroutines.Continuation<? super T> $completion) {
        return null;
    }
}