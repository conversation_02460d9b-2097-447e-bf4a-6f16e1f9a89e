package com.livewallpaper.core.domain.scene;

/**
 * 场景初始化器
 * 负责创建和初始化默认场景数据
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002J\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/livewallpaper/core/domain/scene/SceneInitializer;", "", "sceneRepository", "Lcom/livewallpaper/core/data/repository/SceneRepository;", "(Lcom/livewallpaper/core/data/repository/SceneRepository;)V", "createDefaultScenes", "", "Lcom/livewallpaper/core/data/model/Scene;", "initializeDefaultScenes", "Lcom/livewallpaper/core/utils/Resource;", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "shouldInitialize", "", "core_debug"})
public final class SceneInitializer {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.repository.SceneRepository sceneRepository = null;
    
    @javax.inject.Inject()
    public SceneInitializer(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.repository.SceneRepository sceneRepository) {
        super();
    }
    
    /**
     * 初始化默认场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initializeDefaultScenes(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<kotlin.Unit>> $completion) {
        return null;
    }
    
    /**
     * 创建默认场景列表
     */
    private final java.util.List<com.livewallpaper.core.data.model.Scene> createDefaultScenes() {
        return null;
    }
    
    /**
     * 检查是否需要初始化
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object shouldInitialize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}