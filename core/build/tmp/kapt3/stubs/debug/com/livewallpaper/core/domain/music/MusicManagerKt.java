package com.livewallpaper.core.domain.music;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0018\n\u0000\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\f\u0010\u0003\u001a\u0004\u0018\u00010\u0004*\u00020\u0002\u001a\n\u0010\u0005\u001a\u00020\u0006*\u00020\u0002\u00a8\u0006\u0007"}, d2 = {"getDisplayText", "", "Lcom/livewallpaper/core/domain/music/MusicState;", "getMusicInfoOrNull", "Lcom/livewallpaper/core/data/model/MusicInfo;", "isPlaying", "", "core_debug"})
public final class MusicManagerKt {
    
    /**
     * 音乐状态扩展函数
     */
    @org.jetbrains.annotations.Nullable()
    public static final com.livewallpaper.core.data.model.MusicInfo getMusicInfoOrNull(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicState $this$getMusicInfoOrNull) {
        return null;
    }
    
    public static final boolean isPlaying(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicState $this$isPlaying) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDisplayText(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.music.MusicState $this$getDisplayText) {
        return null;
    }
}