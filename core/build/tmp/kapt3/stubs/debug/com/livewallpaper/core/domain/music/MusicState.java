package com.livewallpaper.core.domain.music;

/**
 * 音乐状态密封类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/livewallpaper/core/domain/music/MusicState;", "", "()V", "Error", "Loading", "NoMusic", "Success", "Lcom/livewallpaper/core/domain/music/MusicState$Error;", "Lcom/livewallpaper/core/domain/music/MusicState$Loading;", "Lcom/livewallpaper/core/domain/music/MusicState$NoMusic;", "Lcom/livewallpaper/core/domain/music/MusicState$Success;", "core_debug"})
public abstract class MusicState {
    
    private MusicState() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/livewallpaper/core/domain/music/MusicState$Error;", "Lcom/livewallpaper/core/domain/music/MusicState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "core_debug"})
    public static final class Error extends com.livewallpaper.core.domain.music.MusicState {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.music.MusicState.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/livewallpaper/core/domain/music/MusicState$Loading;", "Lcom/livewallpaper/core/domain/music/MusicState;", "()V", "core_debug"})
    public static final class Loading extends com.livewallpaper.core.domain.music.MusicState {
        @org.jetbrains.annotations.NotNull()
        public static final com.livewallpaper.core.domain.music.MusicState.Loading INSTANCE = null;
        
        private Loading() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/livewallpaper/core/domain/music/MusicState$NoMusic;", "Lcom/livewallpaper/core/domain/music/MusicState;", "()V", "core_debug"})
    public static final class NoMusic extends com.livewallpaper.core.domain.music.MusicState {
        @org.jetbrains.annotations.NotNull()
        public static final com.livewallpaper.core.domain.music.MusicState.NoMusic INSTANCE = null;
        
        private NoMusic() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0016"}, d2 = {"Lcom/livewallpaper/core/domain/music/MusicState$Success;", "Lcom/livewallpaper/core/domain/music/MusicState;", "musicInfo", "Lcom/livewallpaper/core/data/model/MusicInfo;", "timestamp", "Lkotlinx/datetime/Instant;", "(Lcom/livewallpaper/core/data/model/MusicInfo;Lkotlinx/datetime/Instant;)V", "getMusicInfo", "()Lcom/livewallpaper/core/data/model/MusicInfo;", "getTimestamp", "()Lkotlinx/datetime/Instant;", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "core_debug"})
    public static final class Success extends com.livewallpaper.core.domain.music.MusicState {
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.data.model.MusicInfo musicInfo = null;
        @org.jetbrains.annotations.NotNull()
        private final kotlinx.datetime.Instant timestamp = null;
        
        public Success(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.MusicInfo musicInfo, @org.jetbrains.annotations.NotNull()
        kotlinx.datetime.Instant timestamp) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.MusicInfo getMusicInfo() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.datetime.Instant getTimestamp() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.MusicInfo component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.datetime.Instant component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.music.MusicState.Success copy(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.MusicInfo musicInfo, @org.jetbrains.annotations.NotNull()
        kotlinx.datetime.Instant timestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}