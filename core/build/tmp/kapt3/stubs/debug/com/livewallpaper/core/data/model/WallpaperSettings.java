package com.livewallpaper.core.data.model;

/**
 * 动态壁纸设置数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\bm\b\u0087\b\u0018\u00002\u00020\u0001B\u009b\u0003\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0015\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0019\u001a\u00020\n\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u001e\u0012\b\b\u0002\u0010\u001f\u001a\u00020\r\u0012\b\b\u0002\u0010 \u001a\u00020\u0003\u0012\b\b\u0002\u0010!\u001a\u00020\n\u0012\b\b\u0002\u0010\"\u001a\u00020\u0003\u0012\b\b\u0002\u0010#\u001a\u00020\u0003\u0012\b\b\u0002\u0010$\u001a\u00020\u0003\u0012\b\b\u0002\u0010%\u001a\u00020\u0005\u0012\b\b\u0002\u0010&\u001a\u00020\u0003\u0012\b\b\u0002\u0010\'\u001a\u00020\u0003\u0012\b\b\u0002\u0010(\u001a\u00020\u0003\u0012\b\b\u0002\u0010)\u001a\u00020*\u0012\b\b\u0002\u0010+\u001a\u00020\u0003\u0012\b\b\u0002\u0010,\u001a\u00020\u0003\u0012\b\b\u0002\u0010-\u001a\u00020.\u0012\b\b\u0002\u0010/\u001a\u00020\u0013\u0012\b\b\u0002\u00100\u001a\u00020\u0003\u0012\b\b\u0002\u00101\u001a\u00020\u0003\u0012\b\b\u0002\u00102\u001a\u00020\u0003\u0012\b\b\u0002\u00103\u001a\u00020\u0003\u0012\b\b\u0002\u00104\u001a\u00020\u0003\u00a2\u0006\u0002\u00105J\t\u0010i\u001a\u00020\u0003H\u00c6\u0003J\t\u0010j\u001a\u00020\u0003H\u00c6\u0003J\t\u0010k\u001a\u00020\u0013H\u00c6\u0003J\u0010\u0010l\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u00109J\u0010\u0010m\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u00109J\t\u0010n\u001a\u00020\u0003H\u00c6\u0003J\t\u0010o\u001a\u00020\u0003H\u00c6\u0003J\t\u0010p\u001a\u00020\nH\u00c6\u0003J\t\u0010q\u001a\u00020\u0013H\u00c6\u0003J\t\u0010r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010s\u001a\u00020\u0003H\u00c6\u0003J\t\u0010t\u001a\u00020\u0005H\u00c6\u0003J\t\u0010u\u001a\u00020\u001eH\u00c6\u0003J\t\u0010v\u001a\u00020\rH\u00c6\u0003J\t\u0010w\u001a\u00020\u0003H\u00c6\u0003J\t\u0010x\u001a\u00020\nH\u00c6\u0003J\t\u0010y\u001a\u00020\u0003H\u00c6\u0003J\t\u0010z\u001a\u00020\u0003H\u00c6\u0003J\t\u0010{\u001a\u00020\u0003H\u00c6\u0003J\t\u0010|\u001a\u00020\u0005H\u00c6\u0003J\t\u0010}\u001a\u00020\u0003H\u00c6\u0003J\t\u0010~\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u007f\u001a\u00020\u0007H\u00c6\u0003J\n\u0010\u0080\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0081\u0001\u001a\u00020*H\u00c6\u0003J\n\u0010\u0082\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0083\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0084\u0001\u001a\u00020.H\u00c6\u0003J\n\u0010\u0085\u0001\u001a\u00020\u0013H\u00c6\u0003J\n\u0010\u0086\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0087\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0088\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0089\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u008a\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u008b\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u008c\u0001\u001a\u00020\nH\u00c6\u0003J\n\u0010\u008d\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u008e\u0001\u001a\u00020\rH\u00c6\u0003J\f\u0010\u008f\u0001\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003J\n\u0010\u0090\u0001\u001a\u00020\u0003H\u00c6\u0003J\u00a6\u0003\u0010\u0091\u0001\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u00032\b\b\u0002\u0010\u0018\u001a\u00020\u00032\b\b\u0002\u0010\u0019\u001a\u00020\n2\b\b\u0002\u0010\u001a\u001a\u00020\u00132\b\b\u0002\u0010\u001b\u001a\u00020\u00032\b\b\u0002\u0010\u001c\u001a\u00020\u00032\b\b\u0002\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u001f\u001a\u00020\r2\b\b\u0002\u0010 \u001a\u00020\u00032\b\b\u0002\u0010!\u001a\u00020\n2\b\b\u0002\u0010\"\u001a\u00020\u00032\b\b\u0002\u0010#\u001a\u00020\u00032\b\b\u0002\u0010$\u001a\u00020\u00032\b\b\u0002\u0010%\u001a\u00020\u00052\b\b\u0002\u0010&\u001a\u00020\u00032\b\b\u0002\u0010\'\u001a\u00020\u00032\b\b\u0002\u0010(\u001a\u00020\u00032\b\b\u0002\u0010)\u001a\u00020*2\b\b\u0002\u0010+\u001a\u00020\u00032\b\b\u0002\u0010,\u001a\u00020\u00032\b\b\u0002\u0010-\u001a\u00020.2\b\b\u0002\u0010/\u001a\u00020\u00132\b\b\u0002\u00100\u001a\u00020\u00032\b\b\u0002\u00101\u001a\u00020\u00032\b\b\u0002\u00102\u001a\u00020\u00032\b\b\u0002\u00103\u001a\u00020\u00032\b\b\u0002\u00104\u001a\u00020\u0003H\u00c6\u0001\u00a2\u0006\u0003\u0010\u0092\u0001J\u0015\u0010\u0093\u0001\u001a\u00020\u00032\t\u0010\u0094\u0001\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0007\u0010\u0095\u0001\u001a\u00020\u0005J\u0007\u0010\u0096\u0001\u001a\u00020\u0007J\n\u0010\u0097\u0001\u001a\u00020\u0005H\u00d6\u0001J\u0007\u0010\u0098\u0001\u001a\u00020\u0003J\u0007\u0010\u0099\u0001\u001a\u00020\u0003J\n\u0010\u009a\u0001\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010/\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107R\u0015\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\n\n\u0002\u0010:\u001a\u0004\b8\u00109R\u0015\u0010\u0016\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\n\n\u0002\u0010:\u001a\u0004\b;\u00109R\u0011\u0010)\u001a\u00020*\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010=R\u0011\u00103\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010?R\u0011\u0010\"\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010?R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010?R\u0011\u0010#\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010?R\u0011\u00102\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010?R\u0011\u00104\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010?R\u0011\u0010(\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010?R\u0011\u00100\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010?R\u0011\u00101\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010?R\u0011\u0010+\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010?R\u0011\u0010&\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u0010?R\u0011\u0010\u001b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u0010?R\u0011\u0010$\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u0010?R\u0011\u0010,\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u0010?R\u0011\u0010 \u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u0010?R\u0011\u0010\u001c\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010?R\u0011\u0010\'\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u0010?R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u0010?R\u0011\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u0010?R\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u0010?R\u0011\u0010\u0018\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bS\u0010?R\u0011\u0010\u0017\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bT\u0010?R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bU\u0010VR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010?R\u0011\u0010%\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bW\u0010VR\u0011\u0010!\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bX\u0010YR\u0011\u0010\u001f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bZ\u0010[R\u0011\u0010\u001d\u001a\u00020\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b\\\u0010]R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b^\u0010[R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b_\u0010`R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\ba\u0010bR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bc\u0010YR\u0011\u0010-\u001a\u00020.\u00a2\u0006\b\n\u0000\u001a\u0004\bd\u0010eR\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\bf\u00107R\u0011\u0010\u001a\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\bg\u00107R\u0011\u0010\u0019\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bh\u0010Y\u00a8\u0006\u009b\u0001"}, d2 = {"Lcom/livewallpaper/core/data/model/WallpaperSettings;", "", "isEnabled", "", "frameRate", "", "quality", "Lcom/livewallpaper/core/data/model/RenderQuality;", "enableAutoSceneSwitch", "sceneChangeInterval", "", "enableParallaxEffect", "parallaxIntensity", "", "preferredSceneCategory", "Lcom/livewallpaper/core/data/model/SceneCategory;", "enableTimeBasedScenes", "enableSeasonalScenes", "userTimeZone", "", "customLatitude", "", "customLongitude", "enableWeatherEffects", "enableWeatherBasedScenes", "weatherUpdateInterval", "weatherApiKey", "enableLocationServices", "enableMusicVisualization", "musicCardPosition", "Lcom/livewallpaper/core/data/model/CardPosition;", "musicCardOpacity", "enableMusicCardAutoHide", "musicCardAutoHideDelay", "enableAudioVisualization", "enableBatteryOptimization", "enableLowPowerMode", "maxMemoryUsage", "enableGpuAcceleration", "enableObjectPooling", "enableDebugInfo", "debugInfoPosition", "Lcom/livewallpaper/core/data/model/DebugInfoPosition;", "enableFpsCounter", "enableMemoryMonitor", "themeMode", "Lcom/livewallpaper/core/data/model/ThemeMode;", "accentColor", "enableDynamicColors", "enableExperimentalFeatures", "enableCloudSync", "enableAnalytics", "enableCrashReporting", "(ZILcom/livewallpaper/core/data/model/RenderQuality;ZJZFLcom/livewallpaper/core/data/model/SceneCategory;ZZLjava/lang/String;Ljava/lang/Double;Ljava/lang/Double;ZZJLjava/lang/String;ZZLcom/livewallpaper/core/data/model/CardPosition;FZJZZZIZZZLcom/livewallpaper/core/data/model/DebugInfoPosition;ZZLcom/livewallpaper/core/data/model/ThemeMode;Ljava/lang/String;ZZZZZ)V", "getAccentColor", "()Ljava/lang/String;", "getCustomLatitude", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getCustomLongitude", "getDebugInfoPosition", "()Lcom/livewallpaper/core/data/model/DebugInfoPosition;", "getEnableAnalytics", "()Z", "getEnableAudioVisualization", "getEnableAutoSceneSwitch", "getEnableBatteryOptimization", "getEnableCloudSync", "getEnableCrashReporting", "getEnableDebugInfo", "getEnableDynamicColors", "getEnableExperimentalFeatures", "getEnableFpsCounter", "getEnableGpuAcceleration", "getEnableLocationServices", "getEnableLowPowerMode", "getEnableMemoryMonitor", "getEnableMusicCardAutoHide", "getEnableMusicVisualization", "getEnableObjectPooling", "getEnableParallaxEffect", "getEnableSeasonalScenes", "getEnableTimeBasedScenes", "getEnableWeatherBasedScenes", "getEnableWeatherEffects", "getFrameRate", "()I", "getMaxMemoryUsage", "getMusicCardAutoHideDelay", "()J", "getMusicCardOpacity", "()F", "getMusicCardPosition", "()Lcom/livewallpaper/core/data/model/CardPosition;", "getParallaxIntensity", "getPreferredSceneCategory", "()Lcom/livewallpaper/core/data/model/SceneCategory;", "getQuality", "()Lcom/livewallpaper/core/data/model/RenderQuality;", "getSceneChangeInterval", "getThemeMode", "()Lcom/livewallpaper/core/data/model/ThemeMode;", "getUserTimeZone", "getWeatherApiKey", "getWeatherUpdateInterval", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component37", "component38", "component39", "component4", "component40", "component5", "component6", "component7", "component8", "component9", "copy", "(ZILcom/livewallpaper/core/data/model/RenderQuality;ZJZFLcom/livewallpaper/core/data/model/SceneCategory;ZZLjava/lang/String;Ljava/lang/Double;Ljava/lang/Double;ZZJLjava/lang/String;ZZLcom/livewallpaper/core/data/model/CardPosition;FZJZZZIZZZLcom/livewallpaper/core/data/model/DebugInfoPosition;ZZLcom/livewallpaper/core/data/model/ThemeMode;Ljava/lang/String;ZZZZZ)Lcom/livewallpaper/core/data/model/WallpaperSettings;", "equals", "other", "getEffectiveFrameRate", "getEffectiveQuality", "hashCode", "requiresLocationServices", "requiresNetworkAccess", "toString", "core_debug"})
public final class WallpaperSettings {
    private final boolean isEnabled = false;
    private final int frameRate = 0;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.RenderQuality quality = null;
    private final boolean enableAutoSceneSwitch = false;
    private final long sceneChangeInterval = 0L;
    private final boolean enableParallaxEffect = false;
    private final float parallaxIntensity = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final com.livewallpaper.core.data.model.SceneCategory preferredSceneCategory = null;
    private final boolean enableTimeBasedScenes = false;
    private final boolean enableSeasonalScenes = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String userTimeZone = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double customLatitude = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double customLongitude = null;
    private final boolean enableWeatherEffects = false;
    private final boolean enableWeatherBasedScenes = false;
    private final long weatherUpdateInterval = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String weatherApiKey = null;
    private final boolean enableLocationServices = false;
    private final boolean enableMusicVisualization = false;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.CardPosition musicCardPosition = null;
    private final float musicCardOpacity = 0.0F;
    private final boolean enableMusicCardAutoHide = false;
    private final long musicCardAutoHideDelay = 0L;
    private final boolean enableAudioVisualization = false;
    private final boolean enableBatteryOptimization = false;
    private final boolean enableLowPowerMode = false;
    private final int maxMemoryUsage = 0;
    private final boolean enableGpuAcceleration = false;
    private final boolean enableObjectPooling = false;
    private final boolean enableDebugInfo = false;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.DebugInfoPosition debugInfoPosition = null;
    private final boolean enableFpsCounter = false;
    private final boolean enableMemoryMonitor = false;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.ThemeMode themeMode = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String accentColor = null;
    private final boolean enableDynamicColors = false;
    private final boolean enableExperimentalFeatures = false;
    private final boolean enableCloudSync = false;
    private final boolean enableAnalytics = false;
    private final boolean enableCrashReporting = false;
    
    public WallpaperSettings(boolean isEnabled, int frameRate, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.RenderQuality quality, boolean enableAutoSceneSwitch, long sceneChangeInterval, boolean enableParallaxEffect, float parallaxIntensity, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.SceneCategory preferredSceneCategory, boolean enableTimeBasedScenes, boolean enableSeasonalScenes, @org.jetbrains.annotations.NotNull()
    java.lang.String userTimeZone, @org.jetbrains.annotations.Nullable()
    java.lang.Double customLatitude, @org.jetbrains.annotations.Nullable()
    java.lang.Double customLongitude, boolean enableWeatherEffects, boolean enableWeatherBasedScenes, long weatherUpdateInterval, @org.jetbrains.annotations.NotNull()
    java.lang.String weatherApiKey, boolean enableLocationServices, boolean enableMusicVisualization, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.CardPosition musicCardPosition, float musicCardOpacity, boolean enableMusicCardAutoHide, long musicCardAutoHideDelay, boolean enableAudioVisualization, boolean enableBatteryOptimization, boolean enableLowPowerMode, int maxMemoryUsage, boolean enableGpuAcceleration, boolean enableObjectPooling, boolean enableDebugInfo, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.DebugInfoPosition debugInfoPosition, boolean enableFpsCounter, boolean enableMemoryMonitor, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.ThemeMode themeMode, @org.jetbrains.annotations.NotNull()
    java.lang.String accentColor, boolean enableDynamicColors, boolean enableExperimentalFeatures, boolean enableCloudSync, boolean enableAnalytics, boolean enableCrashReporting) {
        super();
    }
    
    public final boolean isEnabled() {
        return false;
    }
    
    public final int getFrameRate() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.RenderQuality getQuality() {
        return null;
    }
    
    public final boolean getEnableAutoSceneSwitch() {
        return false;
    }
    
    public final long getSceneChangeInterval() {
        return 0L;
    }
    
    public final boolean getEnableParallaxEffect() {
        return false;
    }
    
    public final float getParallaxIntensity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.SceneCategory getPreferredSceneCategory() {
        return null;
    }
    
    public final boolean getEnableTimeBasedScenes() {
        return false;
    }
    
    public final boolean getEnableSeasonalScenes() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUserTimeZone() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getCustomLatitude() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getCustomLongitude() {
        return null;
    }
    
    public final boolean getEnableWeatherEffects() {
        return false;
    }
    
    public final boolean getEnableWeatherBasedScenes() {
        return false;
    }
    
    public final long getWeatherUpdateInterval() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getWeatherApiKey() {
        return null;
    }
    
    public final boolean getEnableLocationServices() {
        return false;
    }
    
    public final boolean getEnableMusicVisualization() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.CardPosition getMusicCardPosition() {
        return null;
    }
    
    public final float getMusicCardOpacity() {
        return 0.0F;
    }
    
    public final boolean getEnableMusicCardAutoHide() {
        return false;
    }
    
    public final long getMusicCardAutoHideDelay() {
        return 0L;
    }
    
    public final boolean getEnableAudioVisualization() {
        return false;
    }
    
    public final boolean getEnableBatteryOptimization() {
        return false;
    }
    
    public final boolean getEnableLowPowerMode() {
        return false;
    }
    
    public final int getMaxMemoryUsage() {
        return 0;
    }
    
    public final boolean getEnableGpuAcceleration() {
        return false;
    }
    
    public final boolean getEnableObjectPooling() {
        return false;
    }
    
    public final boolean getEnableDebugInfo() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.DebugInfoPosition getDebugInfoPosition() {
        return null;
    }
    
    public final boolean getEnableFpsCounter() {
        return false;
    }
    
    public final boolean getEnableMemoryMonitor() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.ThemeMode getThemeMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAccentColor() {
        return null;
    }
    
    public final boolean getEnableDynamicColors() {
        return false;
    }
    
    public final boolean getEnableExperimentalFeatures() {
        return false;
    }
    
    public final boolean getEnableCloudSync() {
        return false;
    }
    
    public final boolean getEnableAnalytics() {
        return false;
    }
    
    public final boolean getEnableCrashReporting() {
        return false;
    }
    
    /**
     * 获取实际帧率（考虑性能优化）
     */
    public final int getEffectiveFrameRate() {
        return 0;
    }
    
    /**
     * 获取实际渲染质量（考虑性能优化）
     */
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.RenderQuality getEffectiveQuality() {
        return null;
    }
    
    /**
     * 检查是否启用了位置服务相关功能
     */
    public final boolean requiresLocationServices() {
        return false;
    }
    
    /**
     * 检查是否启用了网络相关功能
     */
    public final boolean requiresNetworkAccess() {
        return false;
    }
    
    public WallpaperSettings() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component10() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component13() {
        return null;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final long component16() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component17() {
        return null;
    }
    
    public final boolean component18() {
        return false;
    }
    
    public final boolean component19() {
        return false;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.CardPosition component20() {
        return null;
    }
    
    public final float component21() {
        return 0.0F;
    }
    
    public final boolean component22() {
        return false;
    }
    
    public final long component23() {
        return 0L;
    }
    
    public final boolean component24() {
        return false;
    }
    
    public final boolean component25() {
        return false;
    }
    
    public final boolean component26() {
        return false;
    }
    
    public final int component27() {
        return 0;
    }
    
    public final boolean component28() {
        return false;
    }
    
    public final boolean component29() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.RenderQuality component3() {
        return null;
    }
    
    public final boolean component30() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.DebugInfoPosition component31() {
        return null;
    }
    
    public final boolean component32() {
        return false;
    }
    
    public final boolean component33() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.ThemeMode component34() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component35() {
        return null;
    }
    
    public final boolean component36() {
        return false;
    }
    
    public final boolean component37() {
        return false;
    }
    
    public final boolean component38() {
        return false;
    }
    
    public final boolean component39() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component40() {
        return false;
    }
    
    public final long component5() {
        return 0L;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.SceneCategory component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.WallpaperSettings copy(boolean isEnabled, int frameRate, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.RenderQuality quality, boolean enableAutoSceneSwitch, long sceneChangeInterval, boolean enableParallaxEffect, float parallaxIntensity, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.SceneCategory preferredSceneCategory, boolean enableTimeBasedScenes, boolean enableSeasonalScenes, @org.jetbrains.annotations.NotNull()
    java.lang.String userTimeZone, @org.jetbrains.annotations.Nullable()
    java.lang.Double customLatitude, @org.jetbrains.annotations.Nullable()
    java.lang.Double customLongitude, boolean enableWeatherEffects, boolean enableWeatherBasedScenes, long weatherUpdateInterval, @org.jetbrains.annotations.NotNull()
    java.lang.String weatherApiKey, boolean enableLocationServices, boolean enableMusicVisualization, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.CardPosition musicCardPosition, float musicCardOpacity, boolean enableMusicCardAutoHide, long musicCardAutoHideDelay, boolean enableAudioVisualization, boolean enableBatteryOptimization, boolean enableLowPowerMode, int maxMemoryUsage, boolean enableGpuAcceleration, boolean enableObjectPooling, boolean enableDebugInfo, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.DebugInfoPosition debugInfoPosition, boolean enableFpsCounter, boolean enableMemoryMonitor, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.ThemeMode themeMode, @org.jetbrains.annotations.NotNull()
    java.lang.String accentColor, boolean enableDynamicColors, boolean enableExperimentalFeatures, boolean enableCloudSync, boolean enableAnalytics, boolean enableCrashReporting) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}