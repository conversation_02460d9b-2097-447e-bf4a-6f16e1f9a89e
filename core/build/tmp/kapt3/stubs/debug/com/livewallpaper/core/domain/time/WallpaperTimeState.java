package com.livewallpaper.core.domain.time;

/**
 * 壁纸时间状态密封类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0006\u0007\b\u00a8\u0006\t"}, d2 = {"Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "", "()V", "Error", "Loading", "Success", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Error;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Loading;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Success;", "core_debug"})
public abstract class WallpaperTimeState {
    
    private WallpaperTimeState() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Error;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "core_debug"})
    public static final class Error extends com.livewallpaper.core.domain.time.WallpaperTimeState {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.time.WallpaperTimeState.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Loading;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "()V", "core_debug"})
    public static final class Loading extends com.livewallpaper.core.domain.time.WallpaperTimeState {
        @org.jetbrains.annotations.NotNull()
        public static final com.livewallpaper.core.domain.time.WallpaperTimeState.Loading INSTANCE = null;
        
        private Loading() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001b"}, d2 = {"Lcom/livewallpaper/core/domain/time/WallpaperTimeState$Success;", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "timeProgress", "Lcom/livewallpaper/core/domain/time/TimeProgress;", "location", "Lcom/livewallpaper/core/data/model/Location;", "timestamp", "Lkotlinx/datetime/Instant;", "(Lcom/livewallpaper/core/domain/time/TimeProgress;Lcom/livewallpaper/core/data/model/Location;Lkotlinx/datetime/Instant;)V", "getLocation", "()Lcom/livewallpaper/core/data/model/Location;", "getTimeProgress", "()Lcom/livewallpaper/core/domain/time/TimeProgress;", "getTimestamp", "()Lkotlinx/datetime/Instant;", "component1", "component2", "component3", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "core_debug"})
    public static final class Success extends com.livewallpaper.core.domain.time.WallpaperTimeState {
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.domain.time.TimeProgress timeProgress = null;
        @org.jetbrains.annotations.NotNull()
        private final com.livewallpaper.core.data.model.Location location = null;
        @org.jetbrains.annotations.NotNull()
        private final kotlinx.datetime.Instant timestamp = null;
        
        public Success(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.domain.time.TimeProgress timeProgress, @org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.Location location, @org.jetbrains.annotations.NotNull()
        kotlinx.datetime.Instant timestamp) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.time.TimeProgress getTimeProgress() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.Location getLocation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.datetime.Instant getTimestamp() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.time.TimeProgress component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.data.model.Location component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.datetime.Instant component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.livewallpaper.core.domain.time.WallpaperTimeState.Success copy(@org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.domain.time.TimeProgress timeProgress, @org.jetbrains.annotations.NotNull()
        com.livewallpaper.core.data.model.Location location, @org.jetbrains.annotations.NotNull()
        kotlinx.datetime.Instant timestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}