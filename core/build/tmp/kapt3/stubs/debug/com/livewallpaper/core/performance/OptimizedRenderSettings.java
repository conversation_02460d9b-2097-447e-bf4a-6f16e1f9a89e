package com.livewallpaper.core.performance;

/**
 * 优化后的渲染设置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b!\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BM\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\u0006\u0010\u000b\u001a\u00020\u0007\u0012\u0006\u0010\f\u001a\u00020\u0007\u0012\u0006\u0010\r\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0007H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003Jc\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00072\b\b\u0002\u0010\f\u001a\u00020\u00072\b\b\u0002\u0010\r\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010%\u001a\u00020\u00072\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001J\t\u0010(\u001a\u00020)H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006*"}, d2 = {"Lcom/livewallpaper/core/performance/OptimizedRenderSettings;", "", "frameRate", "", "quality", "Lcom/livewallpaper/core/data/model/RenderQuality;", "enableParallax", "", "enableWeatherEffects", "enableMusicVisualization", "maxParticles", "enableObjectPooling", "enableDirtyRegions", "gcTriggerThreshold", "(ILcom/livewallpaper/core/data/model/RenderQuality;ZZZIZZI)V", "getEnableDirtyRegions", "()Z", "getEnableMusicVisualization", "getEnableObjectPooling", "getEnableParallax", "getEnableWeatherEffects", "getFrameRate", "()I", "getGcTriggerThreshold", "getMaxParticles", "getQuality", "()Lcom/livewallpaper/core/data/model/RenderQuality;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "", "core_debug"})
public final class OptimizedRenderSettings {
    private final int frameRate = 0;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.RenderQuality quality = null;
    private final boolean enableParallax = false;
    private final boolean enableWeatherEffects = false;
    private final boolean enableMusicVisualization = false;
    private final int maxParticles = 0;
    private final boolean enableObjectPooling = false;
    private final boolean enableDirtyRegions = false;
    private final int gcTriggerThreshold = 0;
    
    public OptimizedRenderSettings(int frameRate, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.RenderQuality quality, boolean enableParallax, boolean enableWeatherEffects, boolean enableMusicVisualization, int maxParticles, boolean enableObjectPooling, boolean enableDirtyRegions, int gcTriggerThreshold) {
        super();
    }
    
    public final int getFrameRate() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.RenderQuality getQuality() {
        return null;
    }
    
    public final boolean getEnableParallax() {
        return false;
    }
    
    public final boolean getEnableWeatherEffects() {
        return false;
    }
    
    public final boolean getEnableMusicVisualization() {
        return false;
    }
    
    public final int getMaxParticles() {
        return 0;
    }
    
    public final boolean getEnableObjectPooling() {
        return false;
    }
    
    public final boolean getEnableDirtyRegions() {
        return false;
    }
    
    public final int getGcTriggerThreshold() {
        return 0;
    }
    
    public final int component1() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.RenderQuality component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.performance.OptimizedRenderSettings copy(int frameRate, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.RenderQuality quality, boolean enableParallax, boolean enableWeatherEffects, boolean enableMusicVisualization, int maxParticles, boolean enableObjectPooling, boolean enableDirtyRegions, int gcTriggerThreshold) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}