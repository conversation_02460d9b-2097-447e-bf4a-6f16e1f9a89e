package com.livewallpaper.core.di;

/**
 * 时间和位置服务依赖注入模块
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0007J\u0012\u0010\t\u001a\u00020\b2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u0004H\u0007\u00a8\u0006\r"}, d2 = {"Lcom/livewallpaper/core/di/TimeModule;", "", "()V", "provideLocationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "context", "Landroid/content/Context;", "preferences", "Lcom/livewallpaper/core/data/preferences/WallpaperPreferences;", "provideWallpaperPreferences", "provideWallpaperTimeManager", "Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "locationManager", "core_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class TimeModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.livewallpaper.core.di.TimeModule INSTANCE = null;
    
    private TimeModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.preferences.WallpaperPreferences provideWallpaperPreferences(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.location.LocationManager provideLocationManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.preferences.WallpaperPreferences preferences) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.domain.time.WallpaperTimeManager provideWallpaperTimeManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        return null;
    }
}