package com.livewallpaper.core.data.model;

/**
 * 渲染质量枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\n\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/livewallpaper/core/data/model/RenderQuality;", "", "displayName", "", "scale", "", "(Ljava/lang/String;ILjava/lang/String;F)V", "getDisplayName", "()Ljava/lang/String;", "getScale", "()F", "LOW", "MEDIUM", "HIGH", "ULTRA", "core_debug"})
public enum RenderQuality {
    /*public static final*/ LOW /* = new LOW(null, 0.0F) */,
    /*public static final*/ MEDIUM /* = new MEDIUM(null, 0.0F) */,
    /*public static final*/ HIGH /* = new HIGH(null, 0.0F) */,
    /*public static final*/ ULTRA /* = new ULTRA(null, 0.0F) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    private final float scale = 0.0F;
    
    RenderQuality(java.lang.String displayName, float scale) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final float getScale() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.RenderQuality> getEntries() {
        return null;
    }
}