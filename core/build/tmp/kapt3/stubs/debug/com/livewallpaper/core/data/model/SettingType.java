package com.livewallpaper.core.data.model;

/**
 * 设置类型枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/livewallpaper/core/data/model/SettingType;", "", "(Ljava/lang/String;I)V", "BOOLEAN", "INTEGER", "FLOAT", "STRING", "SELECTION", "SLIDER", "COLOR", "ACTION", "core_debug"})
public enum SettingType {
    /*public static final*/ BOOLEAN /* = new BOOLEAN() */,
    /*public static final*/ INTEGER /* = new INTEGER() */,
    /*public static final*/ FLOAT /* = new FLOAT() */,
    /*public static final*/ STRING /* = new STRING() */,
    /*public static final*/ SELECTION /* = new SELECTION() */,
    /*public static final*/ SLIDER /* = new SLIDER() */,
    /*public static final*/ COLOR /* = new COLOR() */,
    /*public static final*/ ACTION /* = new ACTION() */;
    
    SettingType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.SettingType> getEntries() {
        return null;
    }
}