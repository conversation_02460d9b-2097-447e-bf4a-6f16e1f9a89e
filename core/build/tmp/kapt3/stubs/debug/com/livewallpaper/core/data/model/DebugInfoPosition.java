package com.livewallpaper.core.data.model;

/**
 * 调试信息位置枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/livewallpaper/core/data/model/DebugInfoPosition;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "TOP_LEFT", "TOP_RIGHT", "BOTTOM_LEFT", "BOTTOM_RIGHT", "core_debug"})
public enum DebugInfoPosition {
    /*public static final*/ TOP_LEFT /* = new TOP_LEFT(null) */,
    /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT(null) */,
    /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT(null) */,
    /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    DebugInfoPosition(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.livewallpaper.core.data.model.DebugInfoPosition> getEntries() {
        return null;
    }
}