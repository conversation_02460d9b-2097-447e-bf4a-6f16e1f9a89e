package com.livewallpaper.core.domain.update;

/**
 * 应用更新检查器
 * 检查应用更新并提供更新提醒功能
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J \u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00140\u00132\b\b\u0002\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0006\u0010\u0018\u001a\u00020\u0019J\u0018\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\rH\u0002J\u001a\u0010\u001e\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0002J\u0018\u0010#\u001a\u00020\r2\u0006\u0010$\u001a\u00020\u001b2\u0006\u0010%\u001a\u00020\u001bH\u0002J\u0010\u0010&\u001a\u0004\u0018\u00010\"H\u0082@\u00a2\u0006\u0002\u0010\'J\b\u0010(\u001a\u00020 H\u0002J\u000e\u0010)\u001a\u00020\u00192\u0006\u0010*\u001a\u00020\rJ\u0010\u0010+\u001a\u00020\u00162\u0006\u0010*\u001a\u00020\rH\u0002J\u0018\u0010,\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\r2\u0006\u0010.\u001a\u00020\rH\u0002J\b\u0010/\u001a\u00020\u0016H\u0002J\b\u00100\u001a\u00020\u0019H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082D\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u00061"}, d2 = {"Lcom/livewallpaper/core/domain/update/AppUpdateChecker;", "", "context", "Landroid/content/Context;", "httpClient", "Lokhttp3/OkHttpClient;", "(Landroid/content/Context;Lokhttp3/OkHttpClient;)V", "_updateState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/livewallpaper/core/domain/update/UpdateState;", "json", "Lkotlinx/serialization/json/Json;", "updateCheckUrl", "", "updateState", "Lkotlinx/coroutines/flow/StateFlow;", "getUpdateState", "()Lkotlinx/coroutines/flow/StateFlow;", "checkForUpdates", "Lcom/livewallpaper/core/utils/Resource;", "Lcom/livewallpaper/core/domain/update/UpdateInfo;", "forceCheck", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearUpdateState", "", "compareVersionStrings", "", "version1", "version2", "compareVersions", "current", "Lcom/livewallpaper/core/domain/update/VersionInfo;", "server", "Lcom/livewallpaper/core/domain/update/ServerVersionResponse;", "estimateUpdateSize", "currentVersionCode", "latestVersionCode", "fetchLatestVersionFromServer", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentVersionInfo", "ignoreCurrentUpdate", "version", "isVersionIgnored", "isVersionTooOld", "currentVersion", "minSupportedVersion", "shouldCheckForUpdates", "updateLastCheckTime", "core_debug"})
public final class AppUpdateChecker {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient httpClient = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.livewallpaper.core.domain.update.UpdateState> _updateState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.update.UpdateState> updateState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.serialization.json.Json json = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String updateCheckUrl = "https://api.example.com/app/version";
    
    @javax.inject.Inject()
    public AppUpdateChecker(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient httpClient) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.livewallpaper.core.domain.update.UpdateState> getUpdateState() {
        return null;
    }
    
    /**
     * 检查应用更新
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object checkForUpdates(boolean forceCheck, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.domain.update.UpdateInfo>> $completion) {
        return null;
    }
    
    /**
     * 获取当前版本信息
     */
    private final com.livewallpaper.core.domain.update.VersionInfo getCurrentVersionInfo() {
        return null;
    }
    
    /**
     * 从服务器获取最新版本信息
     */
    private final java.lang.Object fetchLatestVersionFromServer(kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.update.ServerVersionResponse> $completion) {
        return null;
    }
    
    /**
     * 比较版本
     */
    private final com.livewallpaper.core.domain.update.UpdateInfo compareVersions(com.livewallpaper.core.domain.update.VersionInfo current, com.livewallpaper.core.domain.update.ServerVersionResponse server) {
        return null;
    }
    
    /**
     * 检查版本是否过旧
     */
    private final boolean isVersionTooOld(java.lang.String currentVersion, java.lang.String minSupportedVersion) {
        return false;
    }
    
    /**
     * 比较版本字符串
     */
    private final int compareVersionStrings(java.lang.String version1, java.lang.String version2) {
        return 0;
    }
    
    /**
     * 估算更新大小
     */
    private final java.lang.String estimateUpdateSize(int currentVersionCode, int latestVersionCode) {
        return null;
    }
    
    /**
     * 检查是否应该进行更新检查
     */
    private final boolean shouldCheckForUpdates() {
        return false;
    }
    
    /**
     * 更新最后检查时间
     */
    private final void updateLastCheckTime() {
    }
    
    /**
     * 忽略当前版本更新
     */
    public final void ignoreCurrentUpdate(@org.jetbrains.annotations.NotNull()
    java.lang.String version) {
    }
    
    /**
     * 检查版本是否被忽略
     */
    private final boolean isVersionIgnored(java.lang.String version) {
        return false;
    }
    
    /**
     * 清除更新状态
     */
    public final void clearUpdateState() {
    }
}