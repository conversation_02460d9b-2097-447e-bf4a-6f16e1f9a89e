package com.livewallpaper.core.domain.music;

/**
 * 音乐卡片渲染器
 * 负责在Canvas上绘制音乐信息卡片
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J8\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\fH\u0002J \u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u0015\u001a\u00020\fH\u0002J0\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u0017\u001a\u00020\f2\u0006\u0010\u0018\u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\fH\u0002J0\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\fH\u0002J0\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\fH\u0002J8\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\f2\u0006\u0010!\u001a\u00020\fH\u0002J@\u0010\"\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010!\u001a\u00020\fJ\u0018\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020&2\u0006\u0010(\u001a\u00020)H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/livewallpaper/core/domain/music/MusicCardRenderer;", "", "()V", "cardPaint", "Landroid/graphics/Paint;", "progressPaint", "textPaint", "calculateCardPosition", "Landroid/graphics/PointF;", "position", "Lcom/livewallpaper/core/data/model/CardPosition;", "canvasWidth", "", "canvasHeight", "cardWidth", "cardHeight", "margin", "drawAlbumArt", "", "canvas", "Landroid/graphics/Canvas;", "size", "drawCardBackground", "width", "height", "opacity", "drawMusicInfo", "musicInfo", "Lcom/livewallpaper/core/data/model/MusicInfo;", "drawProgressBar", "drawVisualization", "visualizationData", "Lcom/livewallpaper/core/data/model/MusicVisualizationData;", "animationTime", "renderMusicCard", "config", "Lcom/livewallpaper/core/data/model/MusicCardConfig;", "truncateText", "", "text", "maxLength", "", "core_debug"})
public final class MusicCardRenderer {
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint cardPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint textPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint progressPaint = null;
    
    @javax.inject.Inject()
    public MusicCardRenderer() {
        super();
    }
    
    /**
     * 渲染音乐卡片
     */
    public final void renderMusicCard(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.MusicInfo musicInfo, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.MusicVisualizationData visualizationData, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.MusicCardConfig config, float canvasWidth, float canvasHeight, float animationTime) {
    }
    
    /**
     * 计算卡片位置
     */
    private final android.graphics.PointF calculateCardPosition(com.livewallpaper.core.data.model.CardPosition position, float canvasWidth, float canvasHeight, float cardWidth, float cardHeight, float margin) {
        return null;
    }
    
    /**
     * 绘制卡片背景
     */
    private final void drawCardBackground(android.graphics.Canvas canvas, android.graphics.PointF position, float width, float height, float opacity) {
    }
    
    /**
     * 绘制专辑封面占位符
     */
    private final void drawAlbumArt(android.graphics.Canvas canvas, android.graphics.PointF position, float size) {
    }
    
    /**
     * 绘制音乐信息
     */
    private final void drawMusicInfo(android.graphics.Canvas canvas, com.livewallpaper.core.data.model.MusicInfo musicInfo, android.graphics.PointF position, float cardWidth, float cardHeight) {
    }
    
    /**
     * 绘制进度条
     */
    private final void drawProgressBar(android.graphics.Canvas canvas, com.livewallpaper.core.data.model.MusicInfo musicInfo, android.graphics.PointF position, float cardWidth, float cardHeight) {
    }
    
    /**
     * 绘制可视化效果
     */
    private final void drawVisualization(android.graphics.Canvas canvas, com.livewallpaper.core.data.model.MusicVisualizationData visualizationData, android.graphics.PointF position, float cardWidth, float cardHeight, float animationTime) {
    }
    
    /**
     * 截断文本
     */
    private final java.lang.String truncateText(java.lang.String text, int maxLength) {
        return null;
    }
}