package com.livewallpaper.core.data.model;

/**
 * 场景数据模型
 * 用于存储壁纸场景的基本信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\'\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0091\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0002\u0010\u0017J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003J\t\u0010-\u001a\u00020\u0012H\u00c6\u0003J\t\u0010.\u001a\u00020\u0012H\u00c6\u0003J\t\u0010/\u001a\u00020\u0015H\u00c6\u0003J\t\u00100\u001a\u00020\u0015H\u00c6\u0003J\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00104\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\nH\u00c6\u0003J\u000b\u00107\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u00108\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u009f\u0001\u00109\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00122\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0015H\u00c6\u0001J\u0013\u0010:\u001a\u00020\u00122\b\u0010;\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010<\u001a\u00020=H\u00d6\u0001J\t\u0010>\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0019R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0011\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010!R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010!R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001dR\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*\u00a8\u0006?"}, d2 = {"Lcom/livewallpaper/core/data/model/Scene;", "", "id", "", "name", "description", "backgroundImagePath", "foregroundImagePath", "middlegroundImagePath", "category", "Lcom/livewallpaper/core/data/model/SceneCategory;", "season", "Lcom/livewallpaper/core/data/model/Season;", "timeOfDay", "Lcom/livewallpaper/core/data/model/TimeOfDay;", "weatherType", "Lcom/livewallpaper/core/data/model/WeatherType;", "isPremium", "", "isCustom", "createdAt", "", "updatedAt", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/livewallpaper/core/data/model/SceneCategory;Lcom/livewallpaper/core/data/model/Season;Lcom/livewallpaper/core/data/model/TimeOfDay;Lcom/livewallpaper/core/data/model/WeatherType;ZZJJ)V", "getBackgroundImagePath", "()Ljava/lang/String;", "getCategory", "()Lcom/livewallpaper/core/data/model/SceneCategory;", "getCreatedAt", "()J", "getDescription", "getForegroundImagePath", "getId", "()Z", "getMiddlegroundImagePath", "getName", "getSeason", "()Lcom/livewallpaper/core/data/model/Season;", "getTimeOfDay", "()Lcom/livewallpaper/core/data/model/TimeOfDay;", "getUpdatedAt", "getWeatherType", "()Lcom/livewallpaper/core/data/model/WeatherType;", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "core_debug"})
@androidx.room.Entity(tableName = "scenes")
public final class Scene {
    @androidx.room.PrimaryKey()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String backgroundImagePath = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String foregroundImagePath = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String middlegroundImagePath = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.SceneCategory category = null;
    @org.jetbrains.annotations.Nullable()
    private final com.livewallpaper.core.data.model.Season season = null;
    @org.jetbrains.annotations.Nullable()
    private final com.livewallpaper.core.data.model.TimeOfDay timeOfDay = null;
    @org.jetbrains.annotations.Nullable()
    private final com.livewallpaper.core.data.model.WeatherType weatherType = null;
    private final boolean isPremium = false;
    private final boolean isCustom = false;
    private final long createdAt = 0L;
    private final long updatedAt = 0L;
    
    public Scene(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String backgroundImagePath, @org.jetbrains.annotations.Nullable()
    java.lang.String foregroundImagePath, @org.jetbrains.annotations.Nullable()
    java.lang.String middlegroundImagePath, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.Season season, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.WeatherType weatherType, boolean isPremium, boolean isCustom, long createdAt, long updatedAt) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBackgroundImagePath() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getForegroundImagePath() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMiddlegroundImagePath() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.SceneCategory getCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.Season getSeason() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.TimeOfDay getTimeOfDay() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.WeatherType getWeatherType() {
        return null;
    }
    
    public final boolean isPremium() {
        return false;
    }
    
    public final boolean isCustom() {
        return false;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long getUpdatedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.WeatherType component10() {
        return null;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final long component13() {
        return 0L;
    }
    
    public final long component14() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.SceneCategory component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.Season component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.livewallpaper.core.data.model.TimeOfDay component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.Scene copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String backgroundImagePath, @org.jetbrains.annotations.Nullable()
    java.lang.String foregroundImagePath, @org.jetbrains.annotations.Nullable()
    java.lang.String middlegroundImagePath, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.SceneCategory category, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.Season season, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.TimeOfDay timeOfDay, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.WeatherType weatherType, boolean isPremium, boolean isCustom, long createdAt, long updatedAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}