package com.livewallpaper.core.performance;

/**
 * 对象池管理器
 * 负责管理常用对象的复用，减少GC压力
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\b\n\u0002\b\u0016\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0012\u001a\u00020\u0013J\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u0015J\u0006\u0010\u0018\u001a\u00020\u0005J\u0006\u0010\u0019\u001a\u00020\u0007J\u0006\u0010\u001a\u001a\u00020\tJ\u0006\u0010\u001b\u001a\u00020\rJ\u0006\u0010\u001c\u001a\u00020\u000bJ\u0006\u0010\u001d\u001a\u00020\u0011J\u0006\u0010\u001e\u001a\u00020\u000fJ\u000e\u0010\u001f\u001a\u00020\u00132\u0006\u0010 \u001a\u00020\u0005J\u000e\u0010!\u001a\u00020\u00132\u0006\u0010\"\u001a\u00020\u0007J\u000e\u0010#\u001a\u00020\u00132\u0006\u0010$\u001a\u00020\tJ\u000e\u0010%\u001a\u00020\u00132\u0006\u0010&\u001a\u00020\rJ\u000e\u0010\'\u001a\u00020\u00132\u0006\u0010(\u001a\u00020\u000bJ\u000e\u0010)\u001a\u00020\u00132\u0006\u0010*\u001a\u00020\u0011J\u000e\u0010+\u001a\u00020\u00132\u0006\u0010,\u001a\u00020\u000fR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/livewallpaper/core/performance/ObjectPoolManager;", "", "()V", "matrixPool", "Lcom/livewallpaper/core/performance/ObjectPool;", "Landroid/graphics/Matrix;", "paintPool", "Landroid/graphics/Paint;", "pathPool", "Landroid/graphics/Path;", "pointFPool", "Landroid/graphics/PointF;", "pointPool", "Landroid/graphics/Point;", "rectFPool", "Landroid/graphics/RectF;", "rectPool", "Landroid/graphics/Rect;", "clearAllPools", "", "getPoolStats", "", "", "", "obtainMatrix", "obtainPaint", "obtainPath", "obtainPoint", "obtainPointF", "obtainRect", "obtainRectF", "recycleMatrix", "matrix", "recyclePaint", "paint", "recyclePath", "path", "recyclePoint", "point", "recyclePointF", "pointF", "recycleRect", "rect", "recycleRectF", "rectF", "core_debug"})
public final class ObjectPoolManager {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.Paint> paintPool = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.Rect> rectPool = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.RectF> rectFPool = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.Path> pathPool = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.Matrix> matrixPool = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.Point> pointPool = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.performance.ObjectPool<android.graphics.PointF> pointFPool = null;
    
    @javax.inject.Inject()
    public ObjectPoolManager() {
        super();
    }
    
    /**
     * 获取Paint对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Paint obtainPaint() {
        return null;
    }
    
    /**
     * 回收Paint对象
     */
    public final void recyclePaint(@org.jetbrains.annotations.NotNull()
    android.graphics.Paint paint) {
    }
    
    /**
     * 获取Rect对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Rect obtainRect() {
        return null;
    }
    
    /**
     * 回收Rect对象
     */
    public final void recycleRect(@org.jetbrains.annotations.NotNull()
    android.graphics.Rect rect) {
    }
    
    /**
     * 获取RectF对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.RectF obtainRectF() {
        return null;
    }
    
    /**
     * 回收RectF对象
     */
    public final void recycleRectF(@org.jetbrains.annotations.NotNull()
    android.graphics.RectF rectF) {
    }
    
    /**
     * 获取Path对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Path obtainPath() {
        return null;
    }
    
    /**
     * 回收Path对象
     */
    public final void recyclePath(@org.jetbrains.annotations.NotNull()
    android.graphics.Path path) {
    }
    
    /**
     * 获取Matrix对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Matrix obtainMatrix() {
        return null;
    }
    
    /**
     * 回收Matrix对象
     */
    public final void recycleMatrix(@org.jetbrains.annotations.NotNull()
    android.graphics.Matrix matrix) {
    }
    
    /**
     * 获取Point对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Point obtainPoint() {
        return null;
    }
    
    /**
     * 回收Point对象
     */
    public final void recyclePoint(@org.jetbrains.annotations.NotNull()
    android.graphics.Point point) {
    }
    
    /**
     * 获取PointF对象
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.PointF obtainPointF() {
        return null;
    }
    
    /**
     * 回收PointF对象
     */
    public final void recyclePointF(@org.jetbrains.annotations.NotNull()
    android.graphics.PointF pointF) {
    }
    
    /**
     * 清理所有对象池
     */
    public final void clearAllPools() {
    }
    
    /**
     * 获取对象池统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Integer> getPoolStats() {
        return null;
    }
}