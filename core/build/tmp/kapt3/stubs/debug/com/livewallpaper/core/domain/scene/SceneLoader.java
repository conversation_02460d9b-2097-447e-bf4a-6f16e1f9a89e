package com.livewallpaper.core.domain.scene;

/**
 * 场景资源加载器
 * 负责异步加载和管理场景图片资源
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\bH\u0002J \u0010\u0011\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\f2\u0006\u0010\u0015\u001a\u00020\fH\u0002J\u0006\u0010\u0016\u001a\u00020\u000eJ(\u0010\u0017\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\f2\u0006\u0010\u001b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u0012\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\u0006\u0010\u001f\u001a\u00020\u0007H\u0002J(\u0010 \u001a\u0004\u0018\u00010\b2\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010!\u001a\u00020\f2\u0006\u0010\"\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010#J,\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00190%2\u0006\u0010&\u001a\u00020\'2\u0006\u0010!\u001a\u00020\f2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010(J&\u0010)\u001a\u00020\u000e2\u0006\u0010&\u001a\u00020\'2\u0006\u0010!\u001a\u00020\f2\u0006\u0010\"\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010(R\u001a\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082D\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/livewallpaper/core/domain/scene/SceneLoader;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "bitmapCache", "", "", "Landroid/graphics/Bitmap;", "currentCacheSize", "", "maxCacheSize", "", "cacheBitmap", "", "key", "bitmap", "calculateInSampleSize", "options", "Landroid/graphics/BitmapFactory$Options;", "reqWidth", "reqHeight", "clearCache", "createCompositeImage", "layers", "Lcom/livewallpaper/core/domain/scene/SceneLayers;", "width", "height", "(Lcom/livewallpaper/core/domain/scene/SceneLayers;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInputStream", "Ljava/io/InputStream;", "path", "loadBitmap", "targetWidth", "targetHeight", "(Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loadScene", "Lcom/livewallpaper/core/utils/Resource;", "scene", "Lcom/livewallpaper/core/data/model/Scene;", "(Lcom/livewallpaper/core/data/model/Scene;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "preloadScene", "core_debug"})
public final class SceneLoader {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, android.graphics.Bitmap> bitmapCache = null;
    private final int maxCacheSize = 52428800;
    private long currentCacheSize = 0L;
    
    @javax.inject.Inject()
    public SceneLoader(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 加载场景的所有图层
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object loadScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, int targetWidth, int targetHeight, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.utils.Resource<com.livewallpaper.core.domain.scene.SceneLayers>> $completion) {
        return null;
    }
    
    /**
     * 加载单个图片资源
     */
    private final java.lang.Object loadBitmap(java.lang.String path, int targetWidth, int targetHeight, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 获取输入流
     */
    private final java.io.InputStream getInputStream(java.lang.String path) {
        return null;
    }
    
    /**
     * 计算采样率
     */
    private final int calculateInSampleSize(android.graphics.BitmapFactory.Options options, int reqWidth, int reqHeight) {
        return 0;
    }
    
    /**
     * 缓存bitmap
     */
    private final void cacheBitmap(java.lang.String key, android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 清理缓存
     */
    public final void clearCache() {
    }
    
    /**
     * 预加载场景
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object preloadScene(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.Scene scene, int targetWidth, int targetHeight, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 创建合成图片
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createCompositeImage(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.scene.SceneLayers layers, int width, int height, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
}