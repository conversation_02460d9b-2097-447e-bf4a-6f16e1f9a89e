package com.livewallpaper.core.data.model;

/**
 * 时间进度数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u000bH\u00c6\u0003JE\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u00052\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\u001f\u001a\u00020 J\u0006\u0010!\u001a\u00020 J\u0006\u0010\"\u001a\u00020#J\t\u0010$\u001a\u00020#H\u00d6\u0001J\t\u0010%\u001a\u00020&H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006\'"}, d2 = {"Lcom/livewallpaper/core/data/model/TimeProgress;", "", "progress", "", "isDay", "", "phase", "Lcom/livewallpaper/core/data/model/TimePhase;", "sunriseTime", "sunsetTime", "timestamp", "Lkotlinx/datetime/Instant;", "(DZLcom/livewallpaper/core/data/model/TimePhase;DDLkotlinx/datetime/Instant;)V", "()Z", "getPhase", "()Lcom/livewallpaper/core/data/model/TimePhase;", "getProgress", "()D", "getSunriseTime", "getSunsetTime", "getTimestamp", "()Lkotlinx/datetime/Instant;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "getColorTemperature", "", "getLightIntensity", "getProgressPercent", "", "hashCode", "toString", "", "core_debug"})
public final class TimeProgress {
    private final double progress = 0.0;
    private final boolean isDay = false;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.data.model.TimePhase phase = null;
    private final double sunriseTime = 0.0;
    private final double sunsetTime = 0.0;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.datetime.Instant timestamp = null;
    
    public TimeProgress(double progress, boolean isDay, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.TimePhase phase, double sunriseTime, double sunsetTime, @org.jetbrains.annotations.NotNull()
    kotlinx.datetime.Instant timestamp) {
        super();
    }
    
    public final double getProgress() {
        return 0.0;
    }
    
    public final boolean isDay() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.TimePhase getPhase() {
        return null;
    }
    
    public final double getSunriseTime() {
        return 0.0;
    }
    
    public final double getSunsetTime() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.datetime.Instant getTimestamp() {
        return null;
    }
    
    /**
     * 获取时间进度百分比
     */
    public final int getProgressPercent() {
        return 0;
    }
    
    /**
     * 获取光照强度
     */
    public final float getLightIntensity() {
        return 0.0F;
    }
    
    /**
     * 获取色温
     */
    public final float getColorTemperature() {
        return 0.0F;
    }
    
    public final double component1() {
        return 0.0;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.TimePhase component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.datetime.Instant component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.livewallpaper.core.data.model.TimeProgress copy(double progress, boolean isDay, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.data.model.TimePhase phase, double sunriseTime, double sunsetTime, @org.jetbrains.annotations.NotNull()
    kotlinx.datetime.Instant timestamp) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}