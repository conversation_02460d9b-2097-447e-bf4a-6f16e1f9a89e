package com.livewallpaper.core.domain.weather;

/**
 * 天气效果渲染器
 * 负责渲染各种天气相关的视觉效果
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J(\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016H\u0002J(\u0010\u0019\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016H\u0002J(\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016H\u0002J0\u0010\u001b\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0002J0\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0002J0\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\b\u0010\u001f\u001a\u0004\u0018\u00010 2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016J0\u0010!\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0002J\u0018\u0010\"\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016H\u0002J \u0010#\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0002J \u0010$\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/livewallpaper/core/domain/weather/WeatherEffectRenderer;", "", "()V", "fogPaint", "Landroid/graphics/Paint;", "fogParticles", "", "Lcom/livewallpaper/core/domain/weather/FogParticle;", "lightningFlashes", "Lcom/livewallpaper/core/domain/weather/LightningFlash;", "lightningPaint", "rainDrops", "Lcom/livewallpaper/core/domain/weather/RainDrop;", "rainPaint", "snowFlakes", "Lcom/livewallpaper/core/domain/weather/SnowFlake;", "snowPaint", "renderCloudShadows", "", "canvas", "Landroid/graphics/Canvas;", "animationTime", "", "width", "height", "renderFogEffect", "renderLightningEffect", "renderRainEffect", "windSpeed", "renderSnowEffect", "renderWeatherEffects", "weather", "Lcom/livewallpaper/core/data/model/Weather;", "renderWindEffect", "updateFogParticles", "updateRainDrops", "updateSnowFlakes", "core_debug"})
public final class WeatherEffectRenderer {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.livewallpaper.core.domain.weather.RainDrop> rainDrops = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.livewallpaper.core.domain.weather.SnowFlake> snowFlakes = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.livewallpaper.core.domain.weather.FogParticle> fogParticles = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.livewallpaper.core.domain.weather.LightningFlash> lightningFlashes = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint rainPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint snowPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint fogPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint lightningPaint = null;
    
    @javax.inject.Inject()
    public WeatherEffectRenderer() {
        super();
    }
    
    /**
     * 渲染天气效果
     */
    public final void renderWeatherEffects(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas, @org.jetbrains.annotations.Nullable()
    com.livewallpaper.core.data.model.Weather weather, float animationTime, float width, float height) {
    }
    
    /**
     * 渲染雨效果
     */
    private final void renderRainEffect(android.graphics.Canvas canvas, float animationTime, float width, float height, float windSpeed) {
    }
    
    /**
     * 渲染雪效果
     */
    private final void renderSnowEffect(android.graphics.Canvas canvas, float animationTime, float width, float height, float windSpeed) {
    }
    
    /**
     * 渲染雾效果
     */
    private final void renderFogEffect(android.graphics.Canvas canvas, float animationTime, float width, float height) {
    }
    
    /**
     * 渲染闪电效果
     */
    private final void renderLightningEffect(android.graphics.Canvas canvas, float animationTime, float width, float height) {
    }
    
    /**
     * 渲染云影效果
     */
    private final void renderCloudShadows(android.graphics.Canvas canvas, float animationTime, float width, float height) {
    }
    
    /**
     * 渲染风效果
     */
    private final void renderWindEffect(android.graphics.Canvas canvas, float animationTime, float width, float height, float windSpeed) {
    }
    
    /**
     * 更新雨滴
     */
    private final void updateRainDrops(float width, float height, float windSpeed) {
    }
    
    /**
     * 更新雪花
     */
    private final void updateSnowFlakes(float width, float height, float windSpeed) {
    }
    
    /**
     * 更新雾粒子
     */
    private final void updateFogParticles(float width, float height) {
    }
}