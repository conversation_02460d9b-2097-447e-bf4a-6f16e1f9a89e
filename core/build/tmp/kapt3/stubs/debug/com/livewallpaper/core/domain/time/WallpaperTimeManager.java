package com.livewallpaper.core.domain.time;

/**
 * 壁纸时间管理器
 * 整合时间计算和位置服务，为壁纸提供统一的时间状态
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u000b\u001a\u0004\u0018\u00010\bH\u0082@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0011\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\fJ\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00100\u0014J\u000e\u0010\u0015\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0017\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\fR\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "", "timeAndSunCalcManager", "Lcom/livewallpaper/core/domain/time/TimeAndSunCalcManager;", "locationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "(Lcom/livewallpaper/core/domain/time/TimeAndSunCalcManager;Lcom/livewallpaper/core/domain/location/LocationManager;)V", "cachedLocation", "Lcom/livewallpaper/core/data/model/Location;", "lastLocationUpdate", "Lkotlinx/datetime/Instant;", "getCurrentLocation", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentTimePhase", "Lcom/livewallpaper/core/domain/time/TimePhase;", "getCurrentTimeState", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "getSimpleTimeProgress", "", "getTimeStateFlow", "Lkotlinx/coroutines/flow/Flow;", "isDaytime", "", "refreshLocation", "core_debug"})
public final class WallpaperTimeManager {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.time.TimeAndSunCalcManager timeAndSunCalcManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.location.LocationManager locationManager = null;
    @org.jetbrains.annotations.Nullable()
    private com.livewallpaper.core.data.model.Location cachedLocation;
    @org.jetbrains.annotations.NotNull()
    private kotlinx.datetime.Instant lastLocationUpdate;
    
    @javax.inject.Inject()
    public WallpaperTimeManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.time.TimeAndSunCalcManager timeAndSunCalcManager, @org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        super();
    }
    
    /**
     * 获取当前时间状态
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentTimeState(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.time.WallpaperTimeState> $completion) {
        return null;
    }
    
    /**
     * 获取时间状态流
     * 自动处理位置更新和时间计算
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.domain.time.WallpaperTimeState> getTimeStateFlow() {
        return null;
    }
    
    /**
     * 获取当前位置（带缓存）
     */
    private final java.lang.Object getCurrentLocation(kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Location> $completion) {
        return null;
    }
    
    /**
     * 强制刷新位置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshLocation(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.time.WallpaperTimeState> $completion) {
        return null;
    }
    
    /**
     * 获取简化的时间进度（仅数值）
     * 用于性能敏感的场景，如壁纸渲染
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSimpleTimeProgress(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * 检查是否为白天
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isDaytime(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * 获取当前时间阶段
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentTimePhase(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.time.TimePhase> $completion) {
        return null;
    }
}