package com.livewallpaper.core.domain.time;

/**
 * 壁纸时间管理器
 * 整合时间计算和位置服务，为壁纸提供统一的时间状态
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0006H\u0002J\u0010\u0010\f\u001a\u0004\u0018\u00010\u0006H\u0082@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0010\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0012\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\rJ\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015J\u000e\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0018\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010\rR\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/livewallpaper/core/domain/time/WallpaperTimeManager;", "", "locationManager", "Lcom/livewallpaper/core/domain/location/LocationManager;", "(Lcom/livewallpaper/core/domain/location/LocationManager;)V", "cachedLocation", "Lcom/livewallpaper/core/data/model/Location;", "lastLocationUpdate", "Lkotlinx/datetime/Instant;", "calculateTimeProgress", "Lcom/livewallpaper/core/data/model/TimeProgress;", "location", "getCurrentLocation", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentTimePhase", "Lcom/livewallpaper/core/data/model/TimePhase;", "getCurrentTimeState", "Lcom/livewallpaper/core/domain/time/WallpaperTimeState;", "getSimpleTimeProgress", "", "getTimeStateFlow", "Lkotlinx/coroutines/flow/Flow;", "isDaytime", "", "refreshLocation", "core_debug"})
public final class WallpaperTimeManager {
    @org.jetbrains.annotations.NotNull()
    private final com.livewallpaper.core.domain.location.LocationManager locationManager = null;
    @org.jetbrains.annotations.Nullable()
    private com.livewallpaper.core.data.model.Location cachedLocation;
    @org.jetbrains.annotations.NotNull()
    private kotlinx.datetime.Instant lastLocationUpdate;
    
    @javax.inject.Inject()
    public WallpaperTimeManager(@org.jetbrains.annotations.NotNull()
    com.livewallpaper.core.domain.location.LocationManager locationManager) {
        super();
    }
    
    /**
     * 获取当前时间状态
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentTimeState(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.time.WallpaperTimeState> $completion) {
        return null;
    }
    
    /**
     * 获取时间状态流
     * 自动处理位置更新和时间计算
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.livewallpaper.core.domain.time.WallpaperTimeState> getTimeStateFlow() {
        return null;
    }
    
    /**
     * 获取当前位置（带缓存）
     */
    private final java.lang.Object getCurrentLocation(kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.Location> $completion) {
        return null;
    }
    
    /**
     * 强制刷新位置
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshLocation(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.domain.time.WallpaperTimeState> $completion) {
        return null;
    }
    
    /**
     * 获取简化的时间进度（仅数值）
     * 用于性能敏感的场景，如壁纸渲染
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSimpleTimeProgress(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    /**
     * 检查是否为白天
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isDaytime(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * 获取当前时间阶段
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentTimePhase(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.livewallpaper.core.data.model.TimePhase> $completion) {
        return null;
    }
    
    /**
     * 计算时间进度（简化版本）
     */
    private final com.livewallpaper.core.data.model.TimeProgress calculateTimeProgress(com.livewallpaper.core.data.model.Location location) {
        return null;
    }
}